import { ReferenceRepository } from '@/repositories/reference.repository';
import { RequestRepository } from '@/repositories/request.repository';
import { Service, Inject, ServiceResponse, logger } from '@c-visitor/framework';
import {
  IReference,
  GetRecognitionsResponse,
  ReferenceWithRecognitionStats,
} from '@c-visitor/types';
import { FileService } from './file.service';
import { BaseService } from './base.service';
import RecognitionModel, { IRecognition, RecognitionDocument } from '@/models/entities/Recognition';
import { RecognitionRepository, ListRecognitionQuery } from '@/repositories/recognition.repository';
import { GetRequestRecognitions } from '@/models/queries/GetRequestRecognitions';
import { GetReferenceRecognitionsQuery } from '@/models/queries/GetReferenceRecognitionsQuery';
import { format as formatDate } from 'date-fns';

@Service()
export class RecognitionService extends BaseService {
  constructor(
    @Inject(RequestRepository) private readonly requestRepository: RequestRepository,
    @Inject(RecognitionRepository) private readonly recognitionRepository: RecognitionRepository,
    @Inject(FileService) private readonly fileService: FileService,
    @Inject(ReferenceRepository) private readonly referenceRepository: ReferenceRepository,
  ) {
    super();
  }

  async getHistories(
    query: GetRequestRecognitions,
  ): Promise<ServiceResponse<GetRecognitionsResponse>> {
    try {
      const { requestId, referenceId, pageIndex, pageSize, fromDate, toDate } =
        query;

      // Get request data with references
      const request = await this.requestRepository.findByIdWithReferences(requestId);

      if (!request) {
        return this.error(`Request with ID ${requestId} not found`);
      }

      // Check if references exist
      if (!request.references || request.references.length === 0) {
        return this.success(
          {
            references: [],
            recognitions: [],
            total: 0,
            pageIndex,
            pageSize,
            totalPages: 0,
          },
          'No references found for this request',
        );
      }

      // Extract all references and their mapping IDs
      const references = request.references as IReference[];

      // Filter references by referenceId if provided
      const filteredReferences = referenceId
        ? references.filter((ref) => ref.id === referenceId)
        : references;

      if (filteredReferences.length === 0) {
        return this.success(
          {
            references: [],
            recognitions: [],
            total: 0,
            pageIndex,
            pageSize,
            totalPages: 0,
          },
          'No matching references found for this request',
        );
      }

      // Get all mapping IDs from filtered references
      const mappings = filteredReferences.flatMap(
        (item) => item.mappings || [],
      );

      // If no mappings found, return empty array
      if (mappings.length === 0) {
        return this.success(
          {
            references: filteredReferences.map((ref) => ({
              reference: ref,
              recognitionCount: 0,
              firstCheckIn: undefined,
              lastCheckOut: undefined,
            })),
            recognitions: [],
            total: 0,
            pageIndex,
            pageSize,
            totalPages: 0,
          },
          'No mappings found for references in this request',
        );
      }

      // Build filter query for Mongoose
      const filter: Record<string, any> = {
        recognizeId: { $in: mappings }
      };

      // Add date filters if provided
      if (fromDate || toDate) {
        filter.createdAt = {};

        if (fromDate) {
          filter.createdAt.$gte = fromDate;
        }

        if (toDate) {
          filter.createdAt.$lte = toDate;
        }
      }

      // Set up query parameters for pagination
      const queryParams: ListRecognitionQuery = {
        pageIndex,
        pageSize,
        fromDate,
        toDate
      };

      // Find recognitions with pagination and filtering
      const findResult = await this.recognitionRepository.getRecognitions({
        ...queryParams,
        // Override the recognizeId with our mappings array
        recognizeId: undefined // We're using $in with multiple IDs, so don't use the single ID filter
      });

      // Convert documents to plain objects for the response
      const recognitions = findResult.rows.map(doc => {
        const plainDoc = doc.toObject();
        return {
          ...plainDoc,
          id: plainDoc._id?.toString() || '',
        } as IRecognition;
      });

      // Get all recognitions (without pagination) to calculate stats
      const allRecognitions = await this.recognitionRepository.findByRecognizeIds(mappings);

      // Group recognitions by recognizeId
      const recognitionsByMappingId: Record<string, IRecognition[]> = {};

      allRecognitions.forEach(recognition => {
        const plainDoc = recognition.toObject();
        const rec = {
          ...plainDoc,
          id: plainDoc._id.toString(),
        } as IRecognition;

        if (!recognitionsByMappingId[rec.recognizeId]) {
          recognitionsByMappingId[rec.recognizeId] = [];
        }
        recognitionsByMappingId[rec.recognizeId].push(rec);
      });

      // Calculate stats for each reference
      const referencesWithStats: ReferenceWithRecognitionStats[] =
        filteredReferences.map((reference) => {
          const refMappings = reference.mappings || [];
          let totalCount = 0;
          let firstCheckIn: Date | undefined;
          let lastCheckOut: Date | undefined;

          refMappings.forEach((mappingId) => {
            const recognitionsForMapping =
              recognitionsByMappingId[mappingId] || [];
            totalCount += recognitionsForMapping.length;

            if (recognitionsForMapping.length > 0) {
              // First check-in is the earliest record
              const firstRec = recognitionsForMapping[0];
              if (
                firstRec?.createdAt &&
                (!firstCheckIn || new Date(firstRec.createdAt) < firstCheckIn)
              ) {
                firstCheckIn = new Date(firstRec.createdAt);
              }

              // Last check-out is the latest record
              const lastRec =
                recognitionsForMapping[recognitionsForMapping.length - 1];
              if (
                lastRec?.createdAt &&
                (!lastCheckOut || new Date(lastRec.createdAt) > lastCheckOut)
              ) {
                lastCheckOut = new Date(lastRec.createdAt);
              }
            }
          });

          return {
            reference,
            recognitionCount: totalCount,
            firstCheckIn,
            lastCheckOut,
          };
        });

      // Create the response object
      const response: GetRecognitionsResponse = {
        references: referencesWithStats,
        recognitions,
        total: findResult.count,
        pageIndex,
        pageSize,
        totalPages: Math.ceil(findResult.count / pageSize),
      };

      return this.success(
        response,
        'Recognition histories retrieved successfully',
      );
    } catch (error) {
      return this.error('Failed to retrieve recognition histories', error);
    }
  }

  async getReferenceRecognitions(
    query: GetReferenceRecognitionsQuery,
  ): Promise<ServiceResponse<GetRecognitionsResponse>> {
    try {
      const { referenceId, pageIndex, pageSize, fromDate, toDate } = query;

      // Get reference data
      const reference = await this.referenceRepository.findById(referenceId);

      if (!reference) {
        return this.error(`Reference with ID ${referenceId} not found`);
      }

      // Check if mappings exist
      if (!reference.mappings || reference.mappings.length === 0) {
        // Convert reference document to plain object with explicit ID
        const plainReference = reference.toObject();
        const referenceData = {
          ...plainReference,
          id: plainReference._id.toString(),
        } as IReference;

        return this.success(
          {
            references: [
              {
                reference: referenceData,
                recognitionCount: 0,
                firstCheckIn: undefined,
                lastCheckOut: undefined,
              },
            ],
            recognitions: [],
            total: 0,
            pageIndex,
            pageSize,
            totalPages: 0,
          },
          'No mappings found for this reference',
        );
      }

      // Get all mapping IDs from reference
      const mappings = reference.mappings || [];

      // Build filter query for Mongoose
      const filter: Record<string, any> = {
        recognizeId: { $in: mappings }
      };

      // Add date filters if provided
      if (fromDate || toDate) {
        filter.createdAt = {};

        if (fromDate) {
          filter.createdAt.$gte = fromDate instanceof Date ? fromDate : new Date(fromDate);
        }

        if (toDate) {
          filter.createdAt.$lte = toDate instanceof Date ? toDate : new Date(toDate);
        }
      }

      // Set up query parameters for pagination
      const queryParams: ListRecognitionQuery = {
        pageIndex,
        pageSize,
        fromDate: fromDate instanceof Date ? fromDate : fromDate ? new Date(fromDate) : undefined,
        toDate: toDate instanceof Date ? toDate : toDate ? new Date(toDate) : undefined
      };

      // Find recognitions with pagination and filtering
      const findResult = await this.recognitionRepository.getRecognitions({
        ...queryParams,
        // Override the recognizeId with our mappings array
        recognizeId: undefined // We're using $in with multiple IDs, so don't use the single ID filter
      });

      // Convert documents to plain objects for the response
      const recognitions = findResult.rows.map(doc => {
        const plainDoc = doc.toObject();
        return {
          ...plainDoc,
          id: plainDoc._id?.toString() || '',
        } as IRecognition;
      });

      // Get all recognitions (without pagination) to calculate stats
      const allRecognitions = await this.recognitionRepository.findByRecognizeIds(mappings);

      // Calculate stats for the reference
      const totalCount = allRecognitions.length;
      let firstCheckIn: Date | undefined;
      let lastCheckOut: Date | undefined;

      if (allRecognitions.length > 0) {
        // First check-in is the earliest record (sort by createdAt ascending)
        const sortedByDate = [...allRecognitions].sort((a, b) =>
          a.createdAt && b.createdAt ? a.createdAt.getTime() - b.createdAt.getTime() : 0
        );

        if (sortedByDate[0]?.createdAt) {
          firstCheckIn = new Date(sortedByDate[0].createdAt);
        }

        // Last check-out is the latest record
        const lastRec = sortedByDate[sortedByDate.length - 1];
        if (lastRec?.createdAt) {
          lastCheckOut = new Date(lastRec.createdAt);
        }
      }

      // Convert reference document to plain object with explicit ID
      const plainReference = reference.toObject();
      const referenceData = {
        ...plainReference,
        id: plainReference._id.toString(),
      } as IReference;

      const referenceWithStats: ReferenceWithRecognitionStats = {
        reference: referenceData,
        recognitionCount: totalCount,
        firstCheckIn,
        lastCheckOut,
      };

      // Create the response object
      const response: GetRecognitionsResponse = {
        references: [referenceWithStats],
        recognitions,
        total: findResult.count,
        pageIndex,
        pageSize,
        totalPages: Math.ceil(findResult.count / pageSize),
      };

      return this.success(
        response,
        'Recognition histories retrieved successfully',
      );
    } catch (error) {
      return this.error(
        'Failed to retrieve reference recognition histories',
        error,
      );
    }
  }

  /**
   * Create or update a recognition
   * @param command Recognition data
   * @returns Service response with the created/updated recognition
   */
  async upsertAsync(command: IRecognition): Promise<ServiceResponse<RecognitionDocument>> {
    try {
      // Use Mongoose transaction
      const result = await this.recognitionRepository.withTransaction(async (session) => {
        if (!command.updatedAt) {
          command.updatedAt = new Date();
        }

        // Format date for filename
        const dateString = formatDate(command.updatedAt, 'yyyyMMddHHmmss');

        // Save the detected face image to MinIO 'images' bucket
        const savedFileName = await this.fileService.saveBase64Image(
          command.detectedFace,
          `facial_recognition_${command.recognizeId}_${dateString}.jpg`,
          'image/jpg',
          'images', // Use 'images' bucket as specified in requirements
        );

        // Update the detectedFace field with the file name
        const recognitionData: Partial<RecognitionDocument> = {
          avatar: command.avatar,
          company: command.company,
          detectedFace: savedFileName, // Use the file name
          deviceId: command.deviceId,
          eventId: command.eventId,
          job: command.job,
          mask: command.mask,
          recognizeId: command.recognizeId,
          recognizeName: command.recognizeName,
          recognizeStatus: command.recognizeStatus,
          srcId: command.srcId,
          temperature: command.temperature,
          trackingId: command.trackingId,
        };

        // If ID is provided, use it for the update
        if (command.id) {
          recognitionData._id = command.id;
        }

        // Create or update the recognition
        const response = await this.recognitionRepository.upsertAsync(
          recognitionData,
          session
        );

        return response;
      });

      return this.success(result, 'Recognition created successfully');
    } catch (error) {
      logger.error('Failed to create/update recognition', { error });
      return this.error('Failed to create/update recognition', error);
    }
  }

  /**
   * Get recognition records by user ID with date range filtering
   * @param userId User ID to get recognitions for
   * @param fromDate Optional start date filter
   * @param toDate Optional end date filter
   * @param pageIndex Page number (1-based)
   * @param pageSize Number of records per page
   * @returns Service response with recognition records
   */
  async getRecognitionsByUser(
    userId: string,
    fromDate?: Date,
    toDate?: Date,
    pageIndex: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{
    recognitions: RecognitionDocument[];
    total: number;
    pageIndex: number;
    pageSize: number;
    totalPages: number;
  }>> {
    try {
      // Build filter query for Mongoose
      const filter: Record<string, any> = {
        recognizeId: userId
      };

      // Add date filters if provided
      if (fromDate || toDate) {
        filter.createdAt = {};

        if (fromDate) {
          filter.createdAt.$gte = fromDate;
        }

        if (toDate) {
          filter.createdAt.$lte = toDate;
        }
      }

      // Calculate pagination
      const skip = (pageIndex - 1) * pageSize;

      // Get total count
      const total = await RecognitionModel.countDocuments(filter);

      // Get paginated recognitions
      const recognitions = await RecognitionModel
        .find(filter)
        .sort({ createdAt: -1 }) // Sort by newest first
        .skip(skip)
        .limit(pageSize)
        .exec();

      // Calculate total pages
      const totalPages = Math.ceil(total / pageSize);

      return this.success(
        {
          recognitions,
          total,
          pageIndex,
          pageSize,
          totalPages,
        },
        'Recognition records retrieved successfully'
      );
    } catch (error) {
      logger.error('Error in getRecognitionsByUser:', error);
      return this.error('Failed to retrieve recognition records');
    }
  }
}
