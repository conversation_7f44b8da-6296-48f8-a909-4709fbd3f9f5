'use client'

import * as React from 'react'
import { Control, FieldPath, FieldValues } from 'react-hook-form'

import { DateTimePicker } from './date-time-picker'
import { FormControl, FormField, FormItem } from '@/components/ui/form'

interface FormDateTimePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>
  name: TName
  disabled?: (date: Date) => boolean
  formSubmitted?: boolean
}

export function FormDateTimePicker<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({ control, name, disabled, formSubmitted = false }: FormDateTimePickerProps<TFieldValues, TName>) {

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className="flex flex-col w-full">
          <FormControl>
            <DateTimePicker
              date={field.value}
              setDate={field.onChange}
              disabled={disabled}
              hasError={formSubmitted && !!fieldState.error}
            />
          </FormControl>
          <div className="h-5 text-sm font-medium text-destructive mt-1">
            {formSubmitted && fieldState.error?.message}
          </div>
        </FormItem>
      )}
    />
  )
}
