/**
 * @fileoverview Export all prototype extensions
 *
 * This module imports and exports all prototype extensions to ensure they are loaded
 * and available throughout the application.
 *
 * @module @c-visitor/shared/lib/prototypes
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Import all prototype extensions to ensure they are loaded
import './String.prototype';
import './Number.prototype';
// import './Object.prototype';
import './JSON.prototype';
import './Date.prototype';

/**
 * Export an empty object to make this a module
 * This is required for TypeScript to recognize this file as a module
 * rather than a script file.
 */
export {};
