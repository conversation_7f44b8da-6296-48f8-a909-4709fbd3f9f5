version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    # Look for package.json and lockfiles in the root directory
    directory: "/"
    # Check for updates once a week (on Monday)
    schedule:
      interval: "weekly"
      day: "monday"
    # Group all dependencies into a single PR
    groups:
      dependencies:
        patterns:
          - "*"
    # Set appropriate review assignment
    reviewers:
      - "team-maintainers"
    # Set version update behavior
    versioning-strategy: increase
    # Limit the number of open PRs
    open-pull-requests-limit: 10

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
    # Group all dependencies into a single PR
    groups:
      github-actions:
        patterns:
          - "*"
    open-pull-requests-limit: 5
