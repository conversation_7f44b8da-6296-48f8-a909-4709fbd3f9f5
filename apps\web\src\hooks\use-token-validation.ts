import { useEffect } from 'react';
import { forceLogout } from '@/utils/token-validator';
import { useAuth } from './use-auth';

/**
 * Hook to validate token immediately on app startup and periodically
 * Automatically refreshes token if needed or logs out user if refresh fails
 */
export const useTokenValidation = () => {
  const { refreshTokenIfNeeded } = useAuth();

  useEffect(() => {
    const validateToken = async () => {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        // No token, redirect to login if not already on auth page
        if (!window.location.pathname.includes('/auth/')) {
          forceLogout();
        }
        return;
      }

      try {
        // Try to refresh token if needed (expires within 5 minutes)
        const refreshed = await refreshTokenIfNeeded();

        if (refreshed) {
          console.log('Token refreshed successfully');
          return;
        }

        // Make a lightweight API call to validate current token
        const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000'}/api/request`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
        });

        if (response.status === 401) {
          // Token expired or invalid - axios interceptor should handle this
          // but if it doesn't, we'll force logout
          console.log('Token validation failed with 401');
        }
      } catch (error) {
        console.error('Token validation failed:', error);
        // Network error - don't logout to avoid bad UX
        // The axios interceptor will handle 401s from actual API calls
      }
    };

    // Validate token immediately on mount (priority)
    validateToken();

    // Set up periodic validation every 3 minutes (more frequent)
    const interval = setInterval(validateToken, 3 * 60 * 1000);

    // Also validate when page becomes visible (user switches back to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        validateToken();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
};
