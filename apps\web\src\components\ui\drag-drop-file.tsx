'use client'

import * as React from 'react'
import { useCallback, useState } from 'react'
import { cn } from '@/lib/utils'
import { UploadIcon, XCircle } from 'lucide-react'

interface DragDropFileProps {
  text?: string
  className?: string
  /**
   * Callback that returns the File object when an image is selected or null when removed
   */
  onChange?: (file: File | null) => void
  /**
   * Callback that returns the base64 string of the image when selected or null when removed
   */
  onImageUrl?: (base64: string | null) => void
  accept?: string
  maxSize?: number // in MB
  disabled?: boolean
  /**
   * Initial base64 image to display (optional)
   */
  initialImage?: string | null
}

export function DragDropFile({
  text = 'Upload ảnh',
  className,
  onChange,
  onImageUrl,
  accept = 'image/*',
  maxSize = 5, // 5MB default
  disabled = false,
  initialImage = null
}: DragDropFileProps) {
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<string | null>(initialImage)
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Set initial image if provided
  React.useEffect(() => {
    if (initialImage) {
      setPreview(initialImage)
    }
  }, [initialImage])

  // Convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  // Validate file
  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return 'Please select an image file'
    }

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024)
    if (fileSizeInMB > maxSize) {
      return `File size must be less than ${maxSize}MB`
    }

    return null
  }

  // Handle file processing
  const handleFile = useCallback(
    async (file: File) => {
      setError(null)

      // Validate file
      const validationError = validateFile(file)
      if (validationError) {
        setError(validationError)
        return
      }

      try {
        // Convert to base64
        const base64 = await fileToBase64(file)
        
        // Set preview
        setPreview(base64)
        
        // Call callbacks
        onChange?.(file)
        onImageUrl?.(base64)
      } catch (error) {
        setError('Failed to process file')
        console.error('File processing error:', error)
      }
    },
    [onChange, onImageUrl, maxSize]
  )

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  // Handle drag events
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  // Handle drop event
  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)
      
      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFile(e.dataTransfer.files[0])
      }
    },
    [handleFile]
  )

  // Handle button click
  const handleButtonClick = () => {
    inputRef.current?.click()
  }

  // Handle remove file - this is the explicit removal action
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    setPreview(null)
    onChange?.(null)
    onImageUrl?.(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="file"
        className="sr-only"
        onChange={handleChange}
        accept={accept}
        disabled={disabled}
      />
      
      <div
        className={cn(
          'relative border-2 border-dashed rounded-lg transition-colors cursor-pointer',
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300',
          disabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={!disabled ? handleButtonClick : undefined}
      >
        {preview ? (
          <div className="relative">
            <img
              src={preview}
              alt="Preview"
              className="w-full h-full object-cover rounded-lg"
            />
            {!disabled && (
              <button
                type="button"
                onClick={handleRemove}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              >
                <XCircle className="w-4 h-4" />
              </button>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <UploadIcon className="w-8 h-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-1">{text}</p>
            <p className="text-xs text-gray-400">
              Drag and drop or click to select
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Max size: {maxSize}MB
            </p>
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
    </div>
  )
}
