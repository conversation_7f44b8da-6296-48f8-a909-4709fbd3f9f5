import { ValidationError } from '../http-error.js';

type SanitizableObject = Record<string, any>;

/**
 * Utility functions for validation and sanitization
 */
export class ValidationUtils {
  /**
   * Sanitize a string to prevent common injection attacks
   * @param value String to sanitize
   * @returns Sanitized string
   */
  static sanitizeString(value: unknown): string {
    if (typeof value !== 'string') return '';

    return value
      .replace(/[<>]/g, '') // Xoá dấu < >
      .replace(/javascript:/gi, '') // Xoá javascript:
      .replace(/\b(on\w+)=/gi, '') // Xoá event handler như onclick=
      .replace(/('|--|#|%27|%23)/gi, '') // Xoá các pattern thường thấy trong SQL injection
      .trim();
  }

  /**
   * Sanitize an object recursively
   * @param obj Object to sanitize
   * @returns Sanitized object
   */
  static sanitizeObject<T extends SanitizableObject>(obj: T): T {
    const result = { ...obj };

    for (const key in result) {
      const value = result[key];

      if (typeof value === 'string') {
        result[key] = this.sanitizeString(value) as any;
      } else if (value !== null && typeof value === 'object') {
        // Use type assertion to tell TypeScript that the result will be compatible
        result[key] = this.sanitizeObject(value as SanitizableObject) as any;
      }
    }

    return result;
  }

  /**
   * Validate that required fields are present in an object
   * @param data Object to validate
   * @param requiredFields Array of required field names
   * @throws ValidationError if any required field is missing
   */
  static validateRequiredFields(
    data: Record<string, any>,
    requiredFields: string[]
  ): void {
    const missingFields = requiredFields.filter((field) => {
      const value = data[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      throw new ValidationError(
        'Required fields missing',
        missingFields.map((field) => ({
          field,
          message: `${field} is required`,
        }))
      );
    }
  }

  /**
   * Validate that a value is within a specified range
   * @param value Value to validate
   * @param min Minimum allowed value
   * @param max Maximum allowed value
   * @param fieldName Name of the field for error message
   * @throws ValidationError if value is outside the range
   */
  static validateRange(
    value: number,
    min: number,
    max: number,
    fieldName: string
  ): void {
    if (value < min || value > max) {
      throw new ValidationError(
        `${fieldName} must be between ${min} and ${max}`,
        [
          {
            field: fieldName,
            message: `Value must be between ${min} and ${max}`,
          },
        ]
      );
    }
  }

  /**
   * Validate that a string matches a regular expression pattern
   * @param value String to validate
   * @param pattern Regular expression pattern
   * @param fieldName Name of the field for error message
   * @throws ValidationError if string doesn't match the pattern
   */
  static validatePattern(
    value: string,
    pattern: RegExp,
    fieldName: string
  ): void {
    if (!pattern.test(value)) {
      throw new ValidationError(`${fieldName} has invalid format`, [
        {
          field: fieldName,
          message: 'Invalid format',
        },
      ]);
    }
  }
}
