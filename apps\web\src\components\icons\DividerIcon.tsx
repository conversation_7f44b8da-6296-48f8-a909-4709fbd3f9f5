import React from 'react';

interface DividerIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const DividerIcon: React.FC<DividerIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0", 
  width = 1, 
  height = 16 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 1 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <line x1="0.5" y1="2.18557e-8" x2="0.499999" y2={16} stroke="#DDE4EE" />
    </svg>
  );
};
