import UserModel, { UserDocument } from '@/models/entities/User';
import { Service } from '@c-visitor/framework';
import { UserRole } from '@c-visitor/types';

/**
 * Repository for User entity
 */
@Service()
export class UserRepository {
  /**
   * Find a user by ID
   * @param id User ID
   * @returns User or null if not found
   */
  async findById(id: string): Promise<UserDocument | null> {
    return UserModel.findById(id).exec();
  }

  /**
   * Find a user by phone number
   * @param phone Phone number
   * @returns User or null if not found
   */
  async findByPhone(phone: string): Promise<UserDocument | null> {
    return UserModel.findOne({ phone }).exec();
  }

  /**
   * Find a user by email
   * @param email Email address
   * @returns User or null if not found
   */
  async findByEmail(email: string): Promise<UserDocument | null> {
    return UserModel.findOne({ email }).exec();
  }

  /**
   * Find a user by phone number or email
   * @param identifier Phone number or email
   * @returns User or null if not found
   */
  async findByPhoneOrEmail(identifier: string): Promise<UserDocument | null> {
    return UserModel.findOne({
      $or: [{ phone: identifier }, { email: identifier }],
    }).exec();
  }

  /**
   * Find all users
   * @returns Array of users
   */
  async findAll(): Promise<UserDocument[]> {
    return UserModel.find().exec();
  }

  /**
   * Find users by role
   * @param role User role
   * @returns Array of users with the specified role
   */
  async findByRole(role: UserRole): Promise<UserDocument[]> {
    return UserModel.find({ role }).exec();
  }

  /**
   * Create a new user
   * @param userData User data
   * @returns Created user
   */
  async create(userData: Partial<UserDocument>): Promise<UserDocument> {
    const user = new UserModel(userData);
    return user.save();
  }

  /**
   * Update a user
   * @param id User ID
   * @param userData User data to update
   * @returns Updated user or null if not found
   */
  async update(
    id: string,
    userData: Partial<UserDocument>,
  ): Promise<UserDocument | null> {
    return UserModel.findByIdAndUpdate(id, userData, { new: true }).exec();
  }

  /**
   * Delete a user
   * @param id User ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await UserModel.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }
}
