import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import React, { useEffect, useMemo } from 'react';

import { DataTablePagination } from './pagination';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pageCount?: number;
  pageSize?: number;
  pageIndex?: number;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onPageChange?: (pageIndex: number) => void;
  totalPages?: number;
  totalCount?: number;
  isLoading?: boolean;
  manualPagination?: boolean;
  sizeChanger?: boolean;
  showPagination?: boolean;
  className?: string;
  /**
   * Optional: Custom column widths, e.g. { id: 120, name: 200 }
   */
  columnWidths?: Record<string, number | string>;
  /**
   * Enable row selection with checkboxes
   */
  enableRowSelection?: boolean;
  /**
   * Callback when row selection changes
   */
  onRowSelectionChange?: (selectedRows: TData[], currentData: TData[]) => void;
  /**
   * External row selection state (for persistent selection across pages)
   */
  rowSelection?: Record<number, boolean>;
  /**
   * Custom max height for table container
   */
  maxHeight?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  pageCount = 0,
  pageSize = 10,
  pageIndex = 0,
  onPaginationChange,
  onPageSizeChange,
  onPageChange,
  totalPages,
  totalCount,
  isLoading = false,
  manualPagination = false,
  sizeChanger = false,
  showPagination = true,
  className,
  columnWidths = {},
  enableRowSelection = false,
  onRowSelectionChange,
  rowSelection: externalRowSelection,
  maxHeight = 'max-h-[500px]',
}: DataTableProps<TData, TValue>) {
  const [internalRowSelection, setInternalRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({
    pageIndex,
    pageSize,
  });

  // Track if this is the initial mount to avoid calling callbacks on mount
  const isInitialMount = React.useRef(true);
  const prevPageSize = React.useRef(pageSize);
  const prevPageIndex = React.useRef(pageIndex);

  // Use external row selection if provided, otherwise use internal state
  const rowSelection = externalRowSelection || internalRowSelection;
  const setRowSelection = externalRowSelection
    ? (updater: any) => {
        // When external row selection is provided, we need to call the callback
        // to let the parent component handle the state update
        if (onRowSelectionChange && enableRowSelection) {
          const newSelection =
            typeof updater === 'function' ? updater(rowSelection) : updater;
          const selectedRowIndices = Object.keys(newSelection).map(Number);
          const selectedRows = selectedRowIndices
            .map((index) => data[index])
            .filter((row): row is TData => row !== undefined);
          onRowSelectionChange(selectedRows, data);
        }
      }
    : setInternalRowSelection;

  // Update pagination state when props change
  useEffect(() => {
    setPagination({
      pageIndex,
      pageSize,
    });
  }, [pageIndex, pageSize]);

  // Notify parent component when pagination changes
  useEffect(() => {
    if (onPaginationChange) {
      onPaginationChange(pagination.pageIndex, pagination.pageSize);
    }
  }, [pagination.pageIndex, pagination.pageSize, onPaginationChange]);

  // Notify parent component when page size changes specifically
  useEffect(() => {
    // Skip on initial mount and only call when page size actually changes
    if (
      onPageSizeChange &&
      !isInitialMount.current &&
      prevPageSize.current !== pagination.pageSize
    ) {
      onPageSizeChange(pagination.pageSize);
    }
    prevPageSize.current = pagination.pageSize;
  }, [pagination.pageSize, onPageSizeChange]);

  // Notify parent component when page index changes specifically
  useEffect(() => {
    // Skip on initial mount and only call when page index actually changes
    if (
      onPageChange &&
      !isInitialMount.current &&
      prevPageIndex.current !== pagination.pageIndex
    ) {
      onPageChange(pagination.pageIndex);
    }
    prevPageIndex.current = pagination.pageIndex;
  }, [pagination.pageIndex, onPageChange]);

  // Mark that initial mount is complete
  useEffect(() => {
    isInitialMount.current = false;
  }, []);

  // Notify parent component when row selection changes (only for internal state)
  useEffect(() => {
    if (onRowSelectionChange && enableRowSelection && !externalRowSelection) {
      const selectedRowIndices = Object.keys(internalRowSelection).map(Number);
      const selectedRows = selectedRowIndices
        .map((index) => data[index])
        .filter((row): row is TData => row !== undefined);
      onRowSelectionChange(selectedRows, data);
    }
  }, [
    internalRowSelection,
    data,
    onRowSelectionChange,
    enableRowSelection,
    externalRowSelection,
  ]);

  // Create columns with optional checkbox column
  const columnsWithSelection = useMemo(() => {
    if (!enableRowSelection) {
      return columns;
    }

    const checkboxColumn: ColumnDef<TData, TValue> = {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="cursor-pointer"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="cursor-pointer"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    };

    return [checkboxColumn, ...columns];
  }, [columns, enableRowSelection]);

  const table = useReactTable({
    data,
    columns: columnsWithSelection,
    pageCount: totalPages || (manualPagination ? pageCount : undefined),
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: enableRowSelection,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel:
      manualPagination || totalPages ? undefined : getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: manualPagination || !!(totalPages || totalCount),
  });

return (
  <div className={`w-full ${showPagination ? 'space-y-4' : ''}`}>
    <div className={`rounded-md border ${className || ''}`}>
      <div className={`relative w-full overflow-auto ${maxHeight}`}>
        <Table className="table-fixed"> {/* table-fixed để hỗ trợ độ rộng cột ổn định */}
          <TableHeader className="sticky top-0 bg-white z-30 shadow-sm">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const colKey = header.column.id;
                  const width = columnWidths[colKey];
                  return (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      className="h-12 px-5 text-left align-middle font-medium text-muted-foreground bg-white border-b"
                      style={
                        width
                          ? { width, minWidth: width, maxWidth: width }
                          : {}
                      }
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columnsWithSelection.length}
                  className="h-24 text-center"
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => {
                    const colKey = cell.column.id;
                    const width = columnWidths[colKey];
                    return (
                      <TableCell
                        key={cell.id}
                        className="p-3 px-5 align-middle"
                        style={
                          width
                            ? { width, minWidth: width, maxWidth: width }
                            : {}
                        }
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columnsWithSelection.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
    {showPagination && (
      <DataTablePagination table={table} sizeChanger={sizeChanger} />
    )}
  </div>
);

}
