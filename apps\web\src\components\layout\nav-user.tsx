'use client';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { LogOut } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useNavigate } from '@tanstack/react-router';
import { toast } from 'sonner';
import { useEffect } from 'react';

export function NavUser() {
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      console.log(user);
    }
  }, [user]);

  // Function to handle logout
  const handleLogout = () => {
    try {
      // Clear localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');

      // Show success message
      toast.success('Đăng xuất thành công!');

      // Redirect to sign in page
      navigate({ to: '/auth/sign-in' });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('<PERSON><PERSON> lỗi xảy ra khi đăng xuất');
    }
  };

  // Don't render if no user
  if (!user) {
    return null;
  }

  // Generate user initials
  const getUserInitials = (fullName: string) => {
    const words = fullName?.split(' ').filter(Boolean) || [];
    if (words.length === 1) {
      return words[0].slice(0, 2).toUpperCase();
    } else if (words.length > 1) {
      return (words[0][0] + words[words.length - 1][0]).toUpperCase();
    }
    return 'U';
  };

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center gap-2 rounded-lg transition-colors cursor-pointer hover:bg-gray-50 p-2">
            <div className="grid flex-1 text-right text-sm leading-tight">
              <span className="truncate font-medium">{user.fullName}</span>
              <span className="truncate text-xs text-gray-500">
                {user.email}
              </span>
              {user.roles && user.roles.length > 0 && (
                <span className="truncate text-xs text-blue-600 font-medium">
                  {user.roles.join(', ')}
                </span>
              )}
            </div>
            <div className="flex items-center">
              <div className="bg-[#008FD31A] text-[#008FD3] flex items-center justify-center rounded-full px-1.5 py-1.5 min-w-[32px] max-h-[32px]">
                <span className="text-[12px] font-bold">
                  {getUserInitials(user.fullName)}
                </span>
              </div>
            </div>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-56 mt-2 rounded-lg"
          align="end"
          sideOffset={4}
        >
          <div className="px-2 py-1.5 text-sm text-gray-500">
            <div className="font-medium text-gray-900">{user.fullName}</div>
            <div className="text-xs">{user.email}</div>
            {user.roles && user.roles.length > 0 && (
              <div className="text-xs text-blue-600 mt-1">
                {user.roles.join(', ')}
              </div>
            )}
          </div>
          <div className="border-t my-1"></div>
          <DropdownMenuItem onClick={handleLogout} className="">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sign-out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
