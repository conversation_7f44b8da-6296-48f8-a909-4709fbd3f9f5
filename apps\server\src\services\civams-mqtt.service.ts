import { Service, logger, Inject } from '@c-visitor/framework';
import { MqttService } from './mqtt.service';
import { RecognitionService } from './recognition.service';
import { environment } from '@/configs/environment';
import { IRecognition } from '@c-visitor/types';

@Service()
export class CivamsMqttService {
  constructor(
    @Inject(MqttService) private readonly mqttService: MqttService,
    @Inject(RecognitionService) private readonly recognitionService: RecognitionService,
  ) {
    this.initialize();
  }

  /**
   * Initialize MQTT client and start subscriptions
   */
  private async initialize(): Promise<void> {
    try {
      // Setup MQTT client with configuration
      const mqttConfig = {
        host: environment.MQTT_HOST,
        clientId: `cvisitor_server_${Date.now()}`, // Generate unique client ID
        username: environment.MQTT_USERNAME,
        password: environment.MQTT_PASSWORD,
        port: environment.MQTT_PORT,
        protocol: environment.MQTT_PROTOCOL as any,
        rejectUnauthorized: environment.MQTT_REJECT_UNAUTHORIZED,
      };

      this.mqttService.setupClient(mqttConfig);

      // Start subscriptions
      await this.subscribe();

      logger.info('Civams MQTT service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Civams MQTT service', { error });
    }
  }

  /**
   * Subscribe to MQTT topics
   */
  async subscribe(): Promise<void> {
    try {
      // Subscribe to matcher results topic
      await this.mqttService.subscribe(
        'topic/matcher_results/',
        async (message) => {
          logger.info('Received message from topic/matcher_results/', {
            topic: message.topic,
            payloadSize: message.payload.length,
            timestamp: new Date().toISOString()
          });
          await this.handleMatcherResults(message);
        },
      );

      // Subscribe to face quality acknowledgment topic
      await this.mqttService.subscribe(
        'topic/FaceQualityAck/',
        async (message) => {
          await this.handleFaceQualityAck(message);
        },
      );

      logger.info('Successfully subscribed to Civams MQTT topics');
    } catch (error) {
      logger.error('Failed to subscribe to MQTT topics', { error });
    }
  }

  /**
   * Handle matcher results messages
   */
  private async handleMatcherResults(message: any): Promise<void> {
    try {
      logger.info('Processing matcher results message', {
        topic: message.topic,
        timestamp: new Date().toISOString(),
        messageSize: message.payload.length
      });

      // Convert message buffer to JSON
      const payload = message.payload.toString();
      logger.debug('Raw payload received', { payload });

      const recognitionData = JSON.parse(payload) as IRecognition;

      logger.info('Parsed recognition data', {
        topic: message.topic,
        recognizeId: recognitionData.recognizeId,
        recognizeName: recognitionData.recognizeName,
        recognizeStatus: recognitionData.recognizeStatus,
        deviceId: recognitionData.deviceId,
        eventId: recognitionData.eventId,
        temperature: recognitionData.temperature,
        mask: recognitionData.mask
      });

      // Only process successful recognitions with valid IDs
      if (
        Number(recognitionData.recognizeStatus) === 1 &&
        recognitionData.recognizeId !== 'unknown'
      ) {
        logger.info('Processing valid recognition result', {
          recognizeId: recognitionData.recognizeId,
          recognizeName: recognitionData.recognizeName,
          recognizeStatus: recognitionData.recognizeStatus
        });

        const result = await this.recognitionService.upsertAsync(recognitionData);

        if (result.success) {
          logger.info('✅ Successfully saved recognition result to database', {
            recognitionId: result.data?._id,
            recognizeId: recognitionData.recognizeId,
            recognizeName: recognitionData.recognizeName,
            deviceId: recognitionData.deviceId,
            eventId: recognitionData.eventId,
            timestamp: new Date().toISOString()
          });
        } else {
          logger.error('❌ Failed to save recognition result to database', {
            error: result.message,
            recognizeId: recognitionData.recognizeId,
            recognizeName: recognitionData.recognizeName,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        logger.warn('⚠️ Skipping recognition result - Invalid status or unknown ID', {
          recognizeId: recognitionData.recognizeId,
          recognizeName: recognitionData.recognizeName,
          recognizeStatus: recognitionData.recognizeStatus,
          reason: Number(recognitionData.recognizeStatus) !== 1 ? 'Invalid status' : 'Unknown ID',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('🚨 Critical error processing matcher results message', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        topic: message.topic,
        payload: message.payload.toString(),
        timestamp: new Date().toISOString(),
        payloadSize: message.payload.length
      });
    }
  }

  /**
   * Handle face quality acknowledgment messages
   */
  private async handleFaceQualityAck(message: any): Promise<void> {
    try {
      const payload = message.payload.toString();
      const ackData = JSON.parse(payload);

      logger.info('Received face quality acknowledgment', {
        topic: message.topic,
        data: ackData,
      });

      // Process the acknowledgment data as needed
      // This could involve updating request status, notifying clients, etc.
    } catch (error) {
      logger.error('Error processing face quality ack message', {
        error,
        topic: message.topic,
        payload: message.payload.toString(),
      });
    }
  }

  /**
   * Publish face quality request
   */
  async publishFaceQualityRequest(
    imageName: string,
    imageBase64: string,
    eventId: string,
    companyId: string,
  ): Promise<void> {
    try {
      const payload = {
        device_id: 'civams-face-id',
        event_id: eventId,
        img_info: [
          {
            img_name: imageName,
            image: imageBase64.split(',').pop(), // Remove data URL prefix
          },
        ],
        db_id: [companyId],
      };

      await this.mqttService.publish(
        'topic/FaceQualityRequest/',
        JSON.stringify(payload),
      );

      logger.info('Published face quality request', {
        eventId,
        companyId,
        imageName,
      });
    } catch (error) {
      logger.error('Failed to publish face quality request', {
        error,
        eventId,
        companyId,
        imageName,
      });
    }
  }

  /**
   * Publish a custom message to a topic
   */
  async publish(topic: string, message: any): Promise<void> {
    try {
      const payload = typeof message === 'string' ? message : JSON.stringify(message);
      await this.mqttService.publish(topic, payload);

      logger.debug('Published MQTT message', { topic });
    } catch (error) {
      logger.error('Failed to publish MQTT message', { error, topic });
    }
  }

  /**
   * Subscribe to a custom topic with handler
   */
  async subscribeToTopic(
    topic: string,
    handler: (message: any) => void | Promise<void>,
  ): Promise<void> {
    try {
      await this.mqttService.subscribe(topic, handler);
      logger.info('Subscribed to custom topic', { topic });
    } catch (error) {
      logger.error('Failed to subscribe to custom topic', { error, topic });
    }
  }

  /**
   * Unsubscribe from a topic
   */
  async unsubscribe(topic: string): Promise<void> {
    try {
      await this.mqttService.unsubscribe(topic);
      logger.info('Unsubscribed from topic', { topic });
    } catch (error) {
      logger.error('Failed to unsubscribe from topic', { error, topic });
    }
  }

  /**
   * Check if MQTT client is connected
   */
  isConnected(): boolean {
    return this.mqttService.isClientConnected();
  }

  /**
   * Close MQTT connection
   */
  async close(): Promise<void> {
    try {
      await this.mqttService.close();
      logger.info('Civams MQTT service closed');
    } catch (error) {
      logger.error('Error closing Civams MQTT service', { error });
    }
  }
}
