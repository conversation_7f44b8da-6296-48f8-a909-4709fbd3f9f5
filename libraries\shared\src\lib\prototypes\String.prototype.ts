/**
 * @fileoverview String prototype extensions to enhance string functionality
 *
 * This module extends the native String prototype with additional utility methods
 * for common string manipulation tasks. These extensions make string operations
 * more concise and readable throughout the application.
 *
 * @module @c-visitor/shared/lib/prototypes/String.prototype
 * <AUTHOR> Development Team
 * @version 1.0.0
 *
 * @example
 * // Import in your application's entry point
 * import '@c-visitor/shared';
 *
 * // Then use the extensions anywhere in your code
 * const slug = "Hello World!".toSlug(); // "hello-world"
 * const isValid = "<EMAIL>".isEmail(); // true
 */

// Extend the String interface to include our new methods
declare global {
  interface String {
    /**
     * Capitalizes the first letter of the string while leaving the rest unchanged
     *
     * @returns A new string with the first letter capitalized
     * @category Case Conversion
     * @example
     * ```typescript
     * "hello world".capitalize(); // Returns "Hello world"
     * "".capitalize(); // Returns ""
     * "a".capitalize(); // Returns "A"
     * ```
     */
    capitalize(): string;

    /**
     * Converts a string to camelCase format
     *
     * Transforms the string by removing non-alphanumeric characters and capitalizing
     * the first letter of each word except the first one.
     *
     * @returns A new string in camelCase format
     * @category Case Conversion
     * @example
     * ```typescript
     * "hello world".toCamelCase(); // Returns "helloWorld"
     * "Hello-World".toCamelCase(); // Returns "helloWorld"
     * "hello_world".toCamelCase(); // Returns "helloWorld"
     * ```
     */
    toCamelCase(): string;

    /**
     * Converts a string to PascalCase format
     *
     * Transforms the string by removing non-alphanumeric characters and capitalizing
     * the first letter of each word.
     *
     * @returns A new string in PascalCase format
     * @category Case Conversion
     * @example
     * ```typescript
     * "hello world".toPascalCase(); // Returns "HelloWorld"
     * "hello-world".toPascalCase(); // Returns "HelloWorld"
     * "hello_world".toPascalCase(); // Returns "HelloWorld"
     * ```
     */
    toPascalCase(): string;

    /**
     * Converts a string to snake_case format
     *
     * Transforms the string by replacing spaces and special characters with underscores,
     * inserting underscores before capital letters, and converting all characters to lowercase.
     *
     * @returns A new string in snake_case format
     * @category Case Conversion
     * @example
     * ```typescript
     * "helloWorld".toSnakeCase(); // Returns "hello_world"
     * "Hello World".toSnakeCase(); // Returns "hello_world"
     * "hello-world".toSnakeCase(); // Returns "hello_world"
     * ```
     */
    toSnakeCase(): string;

    /**
     * Converts a string to kebab-case format
     *
     * Transforms the string by replacing spaces and special characters with hyphens,
     * inserting hyphens before capital letters, and converting all characters to lowercase.
     * Commonly used for CSS class names and URL slugs.
     *
     * @returns A new string in kebab-case format
     * @category Case Conversion
     * @example
     * ```typescript
     * "helloWorld".toKebabCase(); // Returns "hello-world"
     * "Hello World".toKebabCase(); // Returns "hello-world"
     * "hello_world".toKebabCase(); // Returns "hello-world"
     * ```
     */
    toKebabCase(): string;

    /**
     * Truncates a string to the specified length and adds an ellipsis if truncated
     *
     * Useful for displaying long text in limited space, such as in tooltips or previews.
     * If the string is shorter than or equal to the specified length, it remains unchanged.
     *
     * @param length The maximum length of the string (not including the ellipsis)
     * @param ellipsis The string to append if truncated (default: "...")
     * @returns A truncated string with ellipsis if needed
     * @category Text Manipulation
     * @example
     * ```typescript
     * "Hello world".truncate(5); // Returns "Hello..."
     * "Hello".truncate(5); // Returns "Hello" (no truncation needed)
     * "Hello world".truncate(5, "…"); // Returns "Hello…"
     * ```
     */
    truncate(length: number, ellipsis?: string): string;

    /**
     * Checks if a string is empty or contains only whitespace characters
     *
     * This method is useful for validating user input or checking if a string has meaningful content.
     * It uses the native `trim()` method to remove all whitespace before checking the length.
     *
     * @returns True if the string is empty or contains only whitespace characters
     * @category Validation
     * @example
     * ```typescript
     * "  ".isBlank(); // Returns true
     * "".isBlank(); // Returns true
     * "hello".isBlank(); // Returns false
     * " hello ".isBlank(); // Returns false
     * ```
     * @see isNotBlank
     */
    isBlank(): boolean;

    /**
     * Checks if a string is not empty and contains at least one non-whitespace character
     *
     * This is the opposite of `isBlank()` and is useful for validating that a string has meaningful content.
     *
     * @returns True if the string is not empty and contains at least one non-whitespace character
     * @category Validation
     * @example
     * ```typescript
     * "hello".isNotBlank(); // Returns true
     * " hello ".isNotBlank(); // Returns true
     * "".isNotBlank(); // Returns false
     * "  ".isNotBlank(); // Returns false
     * ```
     * @see isBlank
     */
    isNotBlank(): boolean;

    /**
     * Checks if a string is a valid email address
     *
     * Uses a regular expression to validate the email format. This validation is basic
     * and checks for the standard email format (<EMAIL>).
     *
     * @returns True if the string is a valid email address
     * @category Validation
     * @example
     * ```typescript
     * "<EMAIL>".isEmail(); // Returns true
     * "invalid-email".isEmail(); // Returns false
     * "user@domain".isEmail(); // Returns false (missing TLD)
     * ```
     */
    isEmail(): boolean;

    /**
     * Checks if a string is a valid URL
     *
     * Uses the built-in URL constructor to validate the URL format. This provides
     * robust validation for various URL formats including different protocols,
     * query parameters, and fragments.
     *
     * @returns True if the string is a valid URL
     * @category Validation
     * @example
     * ```typescript
     * "https://example.com".isUrl(); // Returns true
     * "http://localhost:3000".isUrl(); // Returns true
     * "ftp://server.com/file.txt".isUrl(); // Returns true
     * "invalid-url".isUrl(); // Returns false
     * ```
     */
    isUrl(): boolean;

    /**
     * Checks if a string contains only numeric characters
     * @returns True if the string contains only numeric characters
     * @example "12345".isNumeric() => true
     */
    isNumeric(): boolean;

    /**
     * Checks if a string contains only alphabetic characters
     * @returns True if the string contains only alphabetic characters
     * @example "hello".isAlpha() => true
     */
    isAlpha(): boolean;

    /**
     * Checks if a string contains only alphanumeric characters
     * @returns True if the string contains only alphanumeric characters
     * @example "hello123".isAlphanumeric() => true
     */
    isAlphanumeric(): boolean;

    /**
     * Removes all HTML tags from a string
     * @returns A new string with all HTML tags removed
     * @example "<p>Hello</p>".stripHtml() => "Hello"
     */
    stripHtml(): string;

    /**
     * Escapes HTML special characters in a string
     * @returns A new string with HTML special characters escaped
     * @example "<script>".escapeHtml() => "&lt;script&gt;"
     */
    escapeHtml(): string;

    /**
     * Unescapes HTML special characters in a string
     * @returns A new string with HTML special characters unescaped
     * @example "&lt;script&gt;".unescapeHtml() => "<script>"
     */
    unescapeHtml(): string;

    /**
     * Removes all special characters from a string
     * @returns A new string with all special characters removed
     * @example "Hello, World!".removeSpecialChars() => "Hello World"
     */
    removeSpecialChars(): string;

    /**
     * Converts a string to a slug (URL-friendly string)
     * @returns A new string in slug format
     * @example "Hello World!".toSlug() => "hello-world"
     */
    toSlug(): string;

    /**
     * Pads a string to the specified length with the specified character
     * @param length The target length of the string
     * @param padChar The character to pad with (default: " ")
     * @returns A new string padded to the specified length
     * @example "hello".padBoth(9, "-") => "--hello--"
     */
    padBoth(length: number, padChar?: string): string;

    /**
     * Reverses a string
     * @returns A new string with the characters in reverse order
     * @example "hello".reverse() => "olleh"
     */
    reverse(): string;

    /**
     * Counts the occurrences of a substring in a string
     * @param substring The substring to count
     * @param caseSensitive Whether the search should be case-sensitive (default: true)
     * @returns The number of occurrences of the substring
     * @example "hello hello".countSubstring("hello") => 2
     */
    countSubstring(substring: string, caseSensitive?: boolean): number;

    /**
     * Extracts all numbers from a string
     * @returns A new string containing only the numeric characters
     * @example "abc123def456".extractNumbers() => "123456"
     */
    extractNumbers(): string;

    /**
     * Extracts all letters from a string
     * @returns A new string containing only the alphabetic characters
     * @example "abc123def456".extractLetters() => "abcdef"
     */
    extractLetters(): string;

    /**
     * Converts a string to title case (capitalize the first letter of each word)
     * @returns A new string in title case
     * @example "hello world".toTitleCase() => "Hello World"
     */
    toTitleCase(): string;

    /**
     * Sanitizes a string to prevent common injection attacks
     *
     * Removes potentially dangerous characters and patterns that could be used for
     * cross-site scripting (XSS), SQL injection, or other injection attacks.
     * This includes:
     * - HTML tags (< and >)
     * - JavaScript protocol handlers
     * - Event handlers (onclick, onload, etc.)
     * - SQL comment markers and quotes
     *
     * @returns A sanitized string with potentially dangerous content removed
     * @category Security
     * @example
     * ```typescript
     * "<script>alert('XSS')</script>".sanitize(); // Returns "alertXSS"
     * "javascript:alert(1)".sanitize(); // Returns "alert(1)"
     * "onclick=alert(1)".sanitize(); // Returns "alert(1)"
     * "SELECT * FROM users--".sanitize(); // Returns "SELECT * FROM users"
     * ```
     */
    sanitize(): string;

    /**
     * Formats a string using named placeholders
     *
     * Replaces placeholders in the format `{name}` with corresponding values from the provided object.
     * If a placeholder doesn't have a corresponding value, it remains unchanged in the output string.
     *
     * @param params An object containing the named parameters to insert into the string
     * @returns A formatted string with placeholders replaced by their values
     * @category Text Manipulation
     * @example
     * ```typescript
     * "Hello, {name}!".format({ name: "World" }); // Returns "Hello, World!"
     * "The {item} costs ${price}".format({ item: "book", price: 10.99 }); // Returns "The book costs $10.99"
     * "Value: {value}".format({}); // Returns "Value: {value}" (placeholder not found in params)
     * ```
     */
    format(params: Record<string, unknown>): string;

    /**
     * Checks if a string matches a regular expression pattern
     * @param pattern The regular expression pattern to match
     * @returns True if the string matches the pattern
     * @example "hello".matches(/^[a-z]+$/) => true
     */
    matches(pattern: RegExp): boolean;

    /**
     * Converts a string to a boolean value
     * @returns True if the string is "true", "yes", "1", or "on" (case-insensitive)
     * @example "true".toBoolean() => true
     */
    toBoolean(): boolean;

    /**
     * Converts a string to a number
     * @returns The string converted to a number, or NaN if the string is not a valid number
     * @example "123".toNumber() => 123
     */
    toNumber(): number;

    /**
     * Converts a JSON string to an object
     *
     * Attempts to parse the string as JSON and returns the resulting object.
     * If parsing fails (invalid JSON), it returns null instead of throwing an exception.
     *
     * @template T The expected type of the parsed object (defaults to unknown)
     * @returns The parsed object of type T, or null if the string is not valid JSON
     * @category Conversion
     * @example
     * ```typescript
     * '{"name":"John"}'.toJson(); // Returns { name: "John" }
     *
     * // With type parameter
     * interface User { name: string; age: number; }
     * '{"name":"John","age":30}'.toJson<User>(); // Returns { name: "John", age: 30 } as User
     *
     * // Invalid JSON
     * 'not json'.toJson(); // Returns null
     * ```
     * @see isJson
     */
    toJson<T = unknown>(): T | null;

    /**
     * Checks if a string is valid JSON
     *
     * Attempts to parse the string as JSON and returns whether the parsing was successful.
     * This method doesn't throw exceptions for invalid JSON.
     *
     * @returns True if the string is valid JSON, false otherwise
     * @category Validation
     * @example
     * ```typescript
     * '{"name":"John"}'.isJson(); // Returns true
     * '{"name":"John",}'.isJson(); // Returns false (trailing comma is invalid JSON)
     * 'not json'.isJson(); // Returns false
     * ```
     * @see toJson
     */
    isJson(): boolean;

    /**
     * Wraps a string with the specified wrapper
     * @param wrapper The string to wrap with
     * @returns A new string wrapped with the specified wrapper
     * @example "hello".wrap("*") => "*hello*"
     */
    wrap(wrapper: string): string;

    /**
     * Unwraps a string by removing the specified wrapper from both ends
     * @param wrapper The wrapper to remove
     * @returns A new string with the wrapper removed from both ends
     * @example "*hello*".unwrap("*") => "hello"
     */
    unwrap(wrapper: string): string;

    /**
     * Checks if a string starts with any of the specified prefixes
     * @param prefixes The prefixes to check
     * @returns True if the string starts with any of the specified prefixes
     * @example "hello".startsWithAny(["he", "ha"]) => true
     */
    startsWithAny(prefixes: string[]): boolean;

    /**
     * Checks if a string ends with any of the specified suffixes
     * @param suffixes The suffixes to check
     * @returns True if the string ends with any of the specified suffixes
     * @example "hello".endsWithAny(["lo", "la"]) => true
     */
    endsWithAny(suffixes: string[]): boolean;

    /**
     * Checks if a string contains any of the specified substrings
     * @param substrings The substrings to check
     * @param caseSensitive Whether the search should be case-sensitive (default: true)
     * @returns True if the string contains any of the specified substrings
     * @example "hello".containsAny(["he", "ha"]) => true
     */
    containsAny(substrings: string[], caseSensitive?: boolean): boolean;

    /**
     * Checks if a string contains all of the specified substrings
     * @param substrings The substrings to check
     * @param caseSensitive Whether the search should be case-sensitive (default: true)
     * @returns True if the string contains all of the specified substrings
     * @example "hello".containsAll(["he", "lo"]) => true
     */
    containsAll(substrings: string[], caseSensitive?: boolean): boolean;

    /**
     * Removes the specified prefix from a string if it exists
     * @param prefix The prefix to remove
     * @returns A new string with the prefix removed
     * @example "prefixText".removePrefix("prefix") => "Text"
     */
    removePrefix(prefix: string): string;

    /**
     * Removes the specified suffix from a string if it exists
     * @param suffix The suffix to remove
     * @returns A new string with the suffix removed
     * @example "textSuffix".removeSuffix("Suffix") => "text"
     */
    removeSuffix(suffix: string): string;

    /**
     * Masks a portion of a string with the specified character
     *
     * Useful for hiding sensitive information like credit card numbers, passwords,
     * or personal identifiers while preserving some context.
     *
     * @param start The starting index to begin masking (default: 0)
     * @param end The ending index to stop masking (default: string length)
     * @param maskChar The character to use for masking (default: "*")
     * @returns A new string with the specified portion masked
     * @category Security
     * @example
     * ```typescript
     * "1234567890".mask(4, 8); // Returns "1234****90"
     * "<EMAIL>".mask(5, 12); // Returns "user@*******com"
     * "password".mask(); // Returns "********" (masks entire string)
     * "1234567890".mask(4); // Returns "1234******" (masks from index 4 to end)
     * ```
     */
    mask(start?: number, end?: number, maskChar?: string): string;
  }
}

// ===================================================
// Implementation of String prototype extension methods
// ===================================================

/**
 * Capitalize the first letter of a string
 */
String.prototype.capitalize = function(): string {
  if (this.length === 0) return this.toString();
  return this.charAt(0).toUpperCase() + this.slice(1);
};

/**
 * Convert a string to camelCase
 */
String.prototype.toCamelCase = function(): string {
  return this.replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
    .replace(/^[A-Z]/, (chr) => chr.toLowerCase());
};

/**
 * Convert a string to PascalCase
 */
String.prototype.toPascalCase = function(): string {
  return this.replace(/\w+/g, (word) =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).replace(/[^a-zA-Z0-9]/g, '');
};

/**
 * Convert a string to snake_case
 */
String.prototype.toSnakeCase = function(): string {
  return this
    .replace(/([A-Z])/g, '_$1')
    .replace(/\s+/g, '_')
    .replace(/^_/, '')
    .toLowerCase();
};

/**
 * Convert a string to kebab-case
 */
String.prototype.toKebabCase = function(): string {
  return this
    .replace(/([A-Z])/g, '-$1')
    .replace(/\s+/g, '-')
    .replace(/^-/, '')
    .toLowerCase();
};

// Truncate a string to a specified length
String.prototype.truncate = function(length: number, ellipsis = '...') {
  if (this.length <= length) return this.toString();
  return this.substring(0, length) + ellipsis;
};

// Check if a string is blank (empty or whitespace only)
String.prototype.isBlank = function(): boolean {
  return this.trim().length === 0;
};

// Check if a string is not blank
String.prototype.isNotBlank = function(): boolean {
  return !this.isBlank();
};

// Check if a string is a valid email
String.prototype.isEmail = function(): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(this.toString());
};

// Check if a string is a valid URL
String.prototype.isUrl = function(): boolean {
  try {
    new URL(this.toString());
    return true;
  } catch {
    return false;
  }
};

// Check if a string contains only numeric characters
String.prototype.isNumeric = function(): boolean {
  return /^\d+$/.test(this.toString());
};

// Check if a string contains only alphabetic characters
String.prototype.isAlpha = function(): boolean {
  return /^[a-zA-Z]+$/.test(this.toString());
};

// Check if a string contains only alphanumeric characters
String.prototype.isAlphanumeric = function(): boolean {
  return /^[a-zA-Z0-9]+$/.test(this.toString());
};

// Remove all HTML tags from a string
String.prototype.stripHtml = function(): string {
  return this.replace(/<[^>]*>/g, '');
};

// Escape HTML special characters
String.prototype.escapeHtml = function(): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  };
  return this.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
};

// Unescape HTML special characters
String.prototype.unescapeHtml = function(): string {
  const htmlUnescapes: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'"
  };
  return this.replace(/&(amp|lt|gt|quot|#39);/g, (match) => htmlUnescapes[match]);
};

// Remove special characters from a string
String.prototype.removeSpecialChars = function(): string {
  return this.replace(/[^\w\s]/gi, '');
};

// Convert a string to a slug (URL-friendly string)
String.prototype.toSlug = function(): string {
  return this
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

// Pad a string on both sides
String.prototype.padBoth = function(length: number, padChar = ' ') {
  const padLength = length - this.length;
  if (padLength <= 0) return this.toString();

  const padStart = Math.floor(padLength / 2);
  const padEnd = padLength - padStart;

  return padChar.repeat(padStart) + this + padChar.repeat(padEnd);
};

// Reverse a string
String.prototype.reverse = function(): string {
  return this.split('').reverse().join('');
};

// Count occurrences of a substring
String.prototype.countSubstring = function(substring: string, caseSensitive = true) {
  if (substring.length === 0) return 0;

  const source = caseSensitive ? this.toString() : this.toLowerCase();
  const target = caseSensitive ? substring : substring.toLowerCase();

  let count = 0;
  let position = source.indexOf(target);

  while (position !== -1) {
    count++;
    position = source.indexOf(target, position + 1);
  }

  return count;
};

// Extract all numbers from a string
String.prototype.extractNumbers = function(): string {
  return this.replace(/[^0-9]/g, '');
};

// Extract all letters from a string
String.prototype.extractLetters = function(): string {
  return this.replace(/[^a-zA-Z]/g, '');
};

// Convert a string to title case
String.prototype.toTitleCase = function(): string {
  return this.replace(/\w\S*/g, (word) =>
    word.charAt(0).toUpperCase() + word.substring(1).toLowerCase()
  );
};

// Sanitize a string to prevent common injection attacks
String.prototype.sanitize = function(): string {
  return this
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/\b(on\w+)=/gi, '')
    .replace(/('|--|#|%27|%23)/gi, '')
    .trim();
};

// Format a string with named placeholders
String.prototype.format = function(params: Record<string, unknown>) {
  return this.replace(/{([^{}]*)}/g, (match, key) => {
    const value = params[key];
    return value !== undefined ? String(value) : match;
  });
};

// Check if a string matches a pattern
String.prototype.matches = function(pattern: RegExp): boolean {
  return pattern.test(this.toString());
};

// Convert a string to a boolean
String.prototype.toBoolean = function(): boolean {
  const value = this.toLowerCase().trim();
  return value === 'true' || value === 'yes' || value === '1' || value === 'on';
};

// Convert a string to a number
String.prototype.toNumber = function(): number {
  return Number(this);
};

// Convert a JSON string to an object
String.prototype.toJson = function<T = unknown>() {
  try {
    return JSON.parse(this.toString()) as T;
  } catch {
    return null;
  }
};

// Check if a string is valid JSON
String.prototype.isJson = function(): boolean {
  try {
    JSON.parse(this.toString());
    return true;
  } catch {
    return false;
  }
};

// Wrap a string with another string
String.prototype.wrap = function(wrapper: string): string {
  return wrapper + this + wrapper;
};

// Unwrap a string by removing a wrapper from both ends
String.prototype.unwrap = function(wrapper: string): string {
  let result = this.toString();
  if (result.startsWith(wrapper)) {
    result = result.substring(wrapper.length);
  }
  if (result.endsWith(wrapper)) {
    result = result.substring(0, result.length - wrapper.length);
  }
  return result;
};

// Check if a string starts with any of the specified prefixes
String.prototype.startsWithAny = function(prefixes: string[]): boolean {
  return prefixes.some(prefix => this.startsWith(prefix));
};

// Check if a string ends with any of the specified suffixes
String.prototype.endsWithAny = function(suffixes: string[]): boolean {
  return suffixes.some(suffix => this.endsWith(suffix));
};

// Check if a string contains any of the specified substrings
String.prototype.containsAny = function(substrings: string[], caseSensitive = true) {
  const source = caseSensitive ? this.toString() : this.toLowerCase();
  return substrings.some(substring => {
    const target = caseSensitive ? substring : substring.toLowerCase();
    return source.includes(target);
  });
};

// Check if a string contains all of the specified substrings
String.prototype.containsAll = function(substrings: string[], caseSensitive = true) {
  const source = caseSensitive ? this.toString() : this.toLowerCase();
  return substrings.every(substring => {
    const target = caseSensitive ? substring : substring.toLowerCase();
    return source.includes(target);
  });
};

// Remove a prefix from a string if it exists
String.prototype.removePrefix = function(prefix: string): string {
  if (this.startsWith(prefix)) {
    return this.substring(prefix.length);
  }
  return this.toString();
};

// Remove a suffix from a string if it exists
String.prototype.removeSuffix = function(suffix: string): string {
  if (this.endsWith(suffix)) {
    return this.substring(0, this.length - suffix.length);
  }
  return this.toString();
};

// Mask a portion of a string
String.prototype.mask = function(start = 0, end?: number, maskChar = '*') {
  const str = this.toString();
  const endIndex = end !== undefined ? end : str.length;

  if (start < 0) start = 0;
  if (endIndex > str.length) end = str.length;
  if (start >= endIndex) return str;

  const visibleStart = str.substring(0, start);
  const visibleEnd = str.substring(endIndex);
  const masked = maskChar.repeat(endIndex - start);

  return visibleStart + masked + visibleEnd;
};

/**
 * Export an empty object to make this a module
 * This is required for TypeScript to recognize this file as a module
 * rather than a script file.
 */
export {};
