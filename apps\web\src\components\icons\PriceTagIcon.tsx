import React from 'react';

interface PriceTagIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const PriceTagIcon: React.FC<PriceTagIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative", 
  width = 14, 
  height = 14 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <g clipPath="url(#clip0_166_123)">
        <ellipse
          cx="0.875"
          cy="0.875"
          rx="0.875"
          ry="0.875"
          transform="matrix(1 0 0 -1 9.33337 4.66681)"
          stroke="#73787E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.61832 6.50065C1.03315 7.15419 1.02056 8.1402 1.55762 8.83383C2.62334 10.2103 3.78978 11.3767 5.16621 12.4424C5.85984 12.9795 6.84585 12.9669 7.49939 12.3817C9.27379 10.793 10.8988 9.13261 12.467 7.30799C12.622 7.1276 12.719 6.9065 12.7407 6.66964C12.837 5.62218 13.0347 2.60441 12.2152 1.78487C11.3956 0.965327 8.37786 1.16305 7.3304 1.2593C7.09354 1.28106 6.87244 1.37805 6.69205 1.53309C4.86743 3.10129 3.20707 4.72625 1.61832 6.50065Z"
          stroke="#73787E"
          strokeWidth="1.5"
        />
        <path
          d="M4.08337 8.16681L5.83337 9.91681"
          stroke="#73787E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_166_123">
          <rect width={14} height={14} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
