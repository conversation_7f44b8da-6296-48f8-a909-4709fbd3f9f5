import { useApiQuery } from './use-api-query';
import { IUser } from '@c-visitor/types';

// Types based on actual User entity
export interface GetUsersParams {
  page?: number;
  pageSize?: number;
  search?: string;
  active?: boolean;
  email?: string;
}

export interface GetUsersResponse {
  users: IUser[];
  total?: number;
  page?: number;
  pageSize?: number;
}

export function useUser() {
  // Get users with filters (simplified - no actual API endpoint exists)
  const useGetUsers = (params?: GetUsersParams) => {
    const queryParams: Record<string, any> = {};

    if (params?.page) queryParams.page = params.page;
    if (params?.pageSize) queryParams.pageSize = params.pageSize;
    if (params?.search) queryParams.search = params.search;
    if (params?.active !== undefined) queryParams.active = params.active;
    if (params?.email) queryParams.email = params.email;

    // Create a stable query key
    const queryKey: string[] = ['users'];
    if (params?.page) queryKey.push(`page:${params.page}`);
    if (params?.pageSize) queryKey.push(`pageSize:${params.pageSize}`);
    if (params?.search) queryKey.push(`search:${params.search}`);
    if (params?.active !== undefined) queryKey.push(`active:${params.active}`);
    if (params?.email) queryKey.push(`email:${params.email}`);

    return useApiQuery<GetUsersResponse>(queryKey, '/api/users', queryParams);
  };

  // Get user by ID
  const useGetUser = (id: string) => {
    return useApiQuery<IUser>(
      ['user', id],
      `/api/users/${id}`,
      undefined,
      {
        enabled: !!id,
      }
    );
  };

  // Search users by email
  const useSearchUsersByEmail = (email: string) => {
    return useApiQuery<IUser[]>(
      ['users', 'search', email],
      `/api/users/search`,
      { email },
      {
        enabled: !!email,
      }
    );
  };

  return {
    useGetUsers,
    useGetUser,
    useSearchUsersByEmail,
  };
}
