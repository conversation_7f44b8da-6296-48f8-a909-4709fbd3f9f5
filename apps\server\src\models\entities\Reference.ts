import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { ReferenceAttributes } from '@c-visitor/types';

// Reference interface for Mongoose document
export interface ReferenceDocument extends Omit<ReferenceAttributes, 'id'>, Document {}

// Reference schema for Mongoose
const ReferenceSchema = new Schema<ReferenceDocument>(
  {
    mappings: { type: [String], required: true },
    fullName: { type: String, required: true, trim: true },
    email: { type: String, required: true, trim: true },
    phone: { type: String, required: true, trim: true },
    unit: { type: String, required: true, trim: true },
    cardIdNumber: { type: String, required: true, trim: true },
    cardIdFront: { type: String, required: true, trim: true },
    cardIdBack: { type: String, required: true, trim: true },
    avatar: { type: String, required: true, trim: true },
    requestId: { type: String, required: true, ref: 'Request' },
    unitId: { type: String, ref: 'Unit' },
  },
  { timestamps: true, collection: 'references' },
);

// Create indexes for frequently queried fields
ReferenceSchema.index({ requestId: 1 });
ReferenceSchema.index({ email: 1 });
ReferenceSchema.index({ phone: 1 });
ReferenceSchema.index({ cardIdNumber: 1 });

// Create the Reference model
const ReferenceModel = mongoose.model<ReferenceDocument>('Reference', ReferenceSchema);

export default ReferenceModel;
