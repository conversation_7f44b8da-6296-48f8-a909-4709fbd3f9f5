name: Build and Deploy

on:
  push:
    branches: [main, master, release]
  pull_request:
    branches: [main, master, release]
  workflow_dispatch:  # Allow manual triggering

env:
  REGISTRY: ghcr.io
  WEB_IMAGE_NAME: ${{ github.repository_owner }}/c-visitor-web
  SERVER_IMAGE_NAME: ${{ github.repository_owner }}/c-visitor-server

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          # Remove npm cache since we're using pnpm

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      # Step 1: Build web app
      - name: Build web application
        run: pnpm run build:prod:web

      # Step 2: Build server app
      - name: Build server application
        run: pnpm run build:prod:server

      # Log in to GitHub Container Registry
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Extract metadata for Docker images
      - name: Extract Docker metadata for web image
        id: meta-web
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.WEB_IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
            type=raw,value=latest,enable=${{ contains(fromJSON('["refs/heads/main", "refs/heads/master", "refs/heads/release"]'), github.ref) }}

      - name: Extract Docker metadata for server image
        id: meta-server
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.SERVER_IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
            type=raw,value=latest,enable=${{ contains(fromJSON('["refs/heads/main", "refs/heads/master", "refs/heads/release"]'), github.ref) }}

      # Set up QEMU for multi-platform builds
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true

      # Build and push Docker images
      - name: Build and push web Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./apps/web/Dockerfile
          push: true
          tags: ${{ steps.meta-web.outputs.tags }}
          labels: ${{ steps.meta-web.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push server Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./apps/server/Dockerfile
          push: true
          tags: ${{ steps.meta-server.outputs.tags }}
          labels: ${{ steps.meta-server.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Summary
        run: |
          echo "✅ Build and deployment completed successfully!"
          echo "📦 Web image: ${{ env.REGISTRY }}/${{ env.WEB_IMAGE_NAME }}:latest"
          echo "📦 Server image: ${{ env.REGISTRY }}/${{ env.SERVER_IMAGE_NAME }}:latest"
          echo "🔗 GitHub Container Registry: https://github.com/${{ github.repository_owner }}?tab=packages"
