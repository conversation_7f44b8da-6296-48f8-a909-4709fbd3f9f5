import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { BucketAttributes } from '@c-visitor/types';

// Bucket interface for Mongoose document
export interface BucketDocument extends Omit<BucketAttributes, 'id'>, Document {}

// Bucket schema for Mongoose
const BucketSchema = new Schema<BucketDocument>(
  {
    name: { type: String, required: true, trim: true },
    region: { type: String, required: true, trim: true },
  },
  { timestamps: true, collection: 'buckets' },
);

// Create indexes for frequently queried fields
BucketSchema.index({ name: 1 }, { unique: true });

// Create the Bucket model
const BucketModel = mongoose.model<BucketDocument>('Bucket', BucketSchema);

export default BucketModel;
