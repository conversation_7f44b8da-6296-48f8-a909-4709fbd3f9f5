import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { DataTable } from '@/components/shared/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { IRequest, IReference } from '@c-visitor/types';
import { ReferenceDetailsDrawer } from '@/components/reference-details-drawer';
import { getImageUrl } from '@/utils/file-utils';

interface RequestInfoTabProps {
  selectedRequest: IRequest | null;
  references: IReference[];
  totalPages?: number;
}

/**
 * Tab component that displays detailed information about the request
 */
export function RequestInfoTab({
  selectedRequest,
  references,
  totalPages,
}: RequestInfoTabProps) {
  // State for reference details drawer
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedReference, setSelectedReference] = useState<IReference | null>(
    null,
  );
  const [recognitionPageIndex, setRecognitionPageIndex] = useState(1);
  const [recognitionPageSize, setRecognitionPageSize] = useState(5);
  const [recognitionPageCount, setRecognitionPageCount] = useState(
    totalPages || 1,
  );

  // Handle view details for a reference
  const handleViewReferenceDetails = (reference: IReference) => {
    setSelectedReference(reference);
    setIsDrawerOpen(true);
  };

  // Define columns for the references table
  const columns: ColumnDef<IReference>[] = useMemo(
    () => [
      {
        accessorKey: 'fullName',
        header: 'Visitor',
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
              <div className="flex justify-start items-center flex-grow gap-2">
                {!item.avatar ? (
                  <div className="flex items-center">
                    <div className="bg-[#008FD31A] text-[#008FD3] flex items-center justify-center rounded-full px-1.5 py-1.5 mr-3 min-w-[32px] max-h-[32px]">
                      <span className="text-[12px] font-bold">
                        {(() => {
                          const words =
                            item.fullName?.split(' ').filter(Boolean) || [];
                          if (words.length === 1) {
                            return words[0].slice(0, 2).toUpperCase();
                          } else if (words.length > 1) {
                            return (
                              words[0][0] + words[words.length - 1][0]
                            ).toUpperCase();
                          }
                          return 'U';
                        })()}
                      </span>
                    </div>
                  </div>
                ) : (
                  <img
                    src={getImageUrl(item.avatar)}
                    alt={item.fullName}
                    className="w-8 h-8 rounded-full mr-3"
                  />
                )}
                <div className="flex flex-col">
                  <p className="text-sm font-medium text-[#1f2329]">
                    {item.fullName}
                  </p>
                  <p className="text-xs text-[#73787e]">{item.email}</p>
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
        cell: ({ row }) => (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="text-sm text-[#1f2329]">{row.original.phone}</p>
          </div>
        ),
      },
      {
        accessorKey: 'unit',
        header: 'Unit',
        cell: ({ row }) => (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="text-sm text-[#1f2329]">{row.original.unit}</p>
          </div>
        ),
      },
      {
        accessorKey: 'cardIdNumber',
        header: 'ID Number (CCCD)',
        cell: ({ row }) => (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="text-sm text-[#1f2329]">
              {row.original.cardIdNumber}
            </p>
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2 gap-2">
            <button
              className="cursor-pointer font-semibold text-[#008FD3] hover:text-[#006ba3]"
              onClick={() => handleViewReferenceDetails(row.original)}
            >
              Detail
            </button>
          </div>
        ),
      },
    ],
    [handleViewReferenceDetails],
  );

  // Handle pagination change
  const handleRecognitionPaginationChange = (
    newPageIndex: number,
    newPageSize: number,
  ) => {
    // If page size changes, reset to first page
    if (newPageSize !== recognitionPageSize) {
      setRecognitionPageIndex(1);
    } else {
      setRecognitionPageIndex(newPageIndex + 1); // API uses 1-based indexing
    }

    setRecognitionPageSize(newPageSize);
  };

  // Update page count when totalPages changes
  useEffect(() => {
    if (totalPages) {
      setRecognitionPageCount(totalPages);
    }
  }, [totalPages]);

  return (
    <TabsContent value="info" className="mt-0 space-y-0">
      <Card className="w-full">
        <CardContent className="p-0 px-6">
          <div className="space-y-6">
            {selectedRequest?.status === 'APPROVED' ? (
              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3 px-4 py-3 rounded-lg bg-[#e8fff6] border border-[#02b875]">
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1">
                  <svg
                    width={24}
                    height={24}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="flex-grow-0 flex-shrink-0 w-6 h-6 relative"
                    preserveAspectRatio="xMidYMid meet"
                  >
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z"
                      fill="#02B875"
                    />
                  </svg>
                  <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#02b875]">
                    Access registration has been approved
                  </p>
                </div>
                {/* Display user who approved */}
                {selectedRequest.updatedByUser ? (
                  <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                    <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                      Approved by:
                    </p>
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest.updatedByUser.fullName} -{' '}
                      {selectedRequest.updatedByUser.email}
                    </p>
                  </div>
                ) : null}
                {selectedRequest.updatedAt && (
                  <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                    <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                      Time:
                    </p>
                    <p className="text-sm text-[#1f2329]">
                      {new Date(selectedRequest.updatedAt)
                        .toLocaleString('en-GB', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: false,
                        })
                        .replace(',', '')}
                    </p>
                  </div>
                )}
              </div>
            ) : selectedRequest?.status === 'REJECTED' ? (
              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3 px-4 py-3 rounded-lg bg-[#fff2f2] border border-[#e03e59]">
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1">
                  <svg
                    width={24}
                    height={24}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="flex-grow-0 flex-shrink-0 w-6 h-6 relative"
                    preserveAspectRatio="xMidYMid meet"
                  >
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM15.5 16.5L12 13L8.5 16.5L7.5 15.5L11 12L7.5 8.5L8.5 7.5L12 11L15.5 7.5L16.5 8.5L13 12L16.5 15.5L15.5 16.5Z"
                      fill="#E03E59"
                    />
                  </svg>
                  <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#e03e59]">
                    Access registration has been rejected
                  </p>
                </div>
                {selectedRequest.reason && (
                  <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                    <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                      REASON:
                    </p>
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest.reason}
                    </p>
                  </div>
                )}
                {/* Display user who rejected */}
                {selectedRequest.updatedByUser ? (
                  <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                    <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                      Rejected by:
                    </p>
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest.updatedByUser.fullName} -{' '}
                      {selectedRequest.updatedByUser.email}
                    </p>
                  </div>
                ) : null}
                {selectedRequest.updatedAt && (
                  <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                    <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                      Time:
                    </p>
                    <p className="text-sm text-[#1f2329]">
                      {new Date(selectedRequest.updatedAt)
                        .toLocaleString('en-GB', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: false,
                        })
                        .replace(',', '')}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="hidden"></div>
            )}

            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-[#8f959e] uppercase tracking-wide">
                GENERAL INFORMATION
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <div className="w-40 flex-shrink-0">
                    <p className="text-sm text-[#53637a]">Purpose of Visit:</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest?.purpose}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-40 flex-shrink-0">
                    <p className="text-sm text-[#53637a]">Contact Person:</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest?.contactName}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-40 flex-shrink-0">
                    <p className="text-sm text-[#53637a]">Department:</p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest?.contactDepartment}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-[#8f959e] uppercase tracking-wide">
                ACCESS DETAILS
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <div className="w-40 flex-shrink-0">
                    <p className="text-sm text-[#53637a]">
                      Devices Brought In:
                    </p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest?.deviceInformationBroughtIn || '-'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-40 flex-shrink-0">
                    <p className="text-sm text-[#53637a]">
                      Other Requirements:
                    </p>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-[#1f2329]">
                      {selectedRequest?.otherRequest || '-'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-[#8f959e] uppercase tracking-wide">
                  VISITOR LIST
                </h3>
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium text-[#646a73]">
                    Total:
                  </span>
                  <span className="text-xs font-semibold text-[#1f2329]">
                    {references?.length || 0} people
                  </span>
                </div>
              </div>

              {references && references.length > 0 ? (
                <DataTable
                  columns={columns}
                  data={references}
                  sizeChanger={true}
                  pageSize={5}
                  pageCount={recognitionPageCount}
                  pageIndex={recognitionPageIndex - 1} // API uses 1-based indexing, DataTable uses 0-based
                  maxHeight="max-h-[300px]"
                />
              ) : (
                <div className="text-center py-8 border border-dashed border-gray-200 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    No visitors added
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reference Details Drawer */}
      <ReferenceDetailsDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        referenceData={selectedReference}
      />
    </TabsContent>
  );
}
