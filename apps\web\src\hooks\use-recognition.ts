import { useApiQuery } from './use-api-query';
import { GetRecognitionsResponse } from '@c-visitor/types';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/configs/axios';

// Types based on actual recognition API
export interface GetRecognitionsParams {
  referenceId?: string;
  requestId?: string;
  pageIndex?: number;
  pageSize?: number;
  fromDate?: Date;
  toDate?: Date;
}

export function useRecognition() {
  // Get recognitions by reference ID
  const useGetReferenceRecognitions = (params: GetRecognitionsParams) => {
    const queryParams: Record<string, any> = {};

    if (params.pageIndex) queryParams.pageIndex = params.pageIndex;
    if (params.pageSize) queryParams.pageSize = params.pageSize;
    if (params.fromDate) queryParams.fromDate = params.fromDate.toISOString();
    if (params.toDate) queryParams.toDate = params.toDate.toISOString();

    // Create a stable query key
    const queryKey: string[] = ['recognitions', 'reference'];
    if (params.referenceId) queryKey.push(params.referenceId);
    if (params.pageIndex) queryKey.push(`pageIndex:${params.pageIndex}`);
    if (params.pageSize) queryKey.push(`pageSize:${params.pageSize}`);
    if (params.fromDate) queryKey.push(`fromDate:${params.fromDate.toISOString()}`);
    if (params.toDate) queryKey.push(`toDate:${params.toDate.toISOString()}`);

    return useApiQuery<GetRecognitionsResponse>(
      queryKey,
      `/api/recognition/reference/${params.referenceId}`,
      queryParams,
      {
        enabled: !!params.referenceId,
      }
    );
  };

  // Get recognitions by request ID
  const useGetRequestRecognitions = (params: GetRecognitionsParams) => {
    const queryParams: Record<string, any> = {};

    if (params.referenceId) queryParams.referenceId = params.referenceId;
    if (params.pageIndex) queryParams.pageIndex = params.pageIndex;
    if (params.pageSize) queryParams.pageSize = params.pageSize;
    if (params.fromDate) queryParams.fromDate = params.fromDate.toISOString();
    if (params.toDate) queryParams.toDate = params.toDate.toISOString();

    // Create a stable query key
    const queryKey: string[] = ['recognitions', 'request'];
    if (params.requestId) queryKey.push(params.requestId);
    if (params.referenceId) queryKey.push(`referenceId:${params.referenceId}`);
    if (params.pageIndex) queryKey.push(`pageIndex:${params.pageIndex}`);
    if (params.pageSize) queryKey.push(`pageSize:${params.pageSize}`);
    if (params.fromDate) queryKey.push(`fromDate:${params.fromDate.toISOString()}`);
    if (params.toDate) queryKey.push(`toDate:${params.toDate.toISOString()}`);

    return useApiQuery<GetRecognitionsResponse>(
      queryKey,
      `/api/request/${params.requestId}/recognitions`,
      queryParams,
      {
        enabled: !!params.requestId,
      }
    );
  };

  // Get recognitions by user ID using POST request
  const useGetUserRecognitions = (params: GetRecognitionsParams & { userId: string }) => {
    // Create request body
    const requestBody = {
      userId: params.userId,
      pageIndex: params.pageIndex,
      pageSize: params.pageSize,
      fromDate: params.fromDate?.toISOString(),
      toDate: params.toDate?.toISOString(),
    };

    // Create a stable query key
    const queryKey: string[] = ['recognitions', 'user'];
    if (params.userId) queryKey.push(params.userId);
    if (params.pageIndex) queryKey.push(`pageIndex:${params.pageIndex}`);
    if (params.pageSize) queryKey.push(`pageSize:${params.pageSize}`);
    if (params.fromDate) queryKey.push(`fromDate:${params.fromDate.toISOString()}`);
    if (params.toDate) queryKey.push(`toDate:${params.toDate.toISOString()}`);

    return useQuery<{
      recognitions: any[];
      total: number;
      pageIndex: number;
      pageSize: number;
      totalPages: number;
    }>({
      queryKey,
      queryFn: async () => {
        try {
          // Use POST request with body
          return await api.post<{
            recognitions: any[];
            total: number;
            pageIndex: number;
            pageSize: number;
            totalPages: number;
          }>('/api/request/recognition/user', requestBody);
        } catch (error) {
          console.error('Error fetching user recognitions:', error);
          throw error;
        }
      },
      enabled: !!params.userId,
    });
  };

  return {
    useGetReferenceRecognitions,
    useGetRequestRecognitions,
    useGetUserRecognitions,
  };
}
