import { Express } from 'express';
import { ApplicationBuilder, IApplicationBuilder } from './application.js';
import { IServiceCollection, IServiceProvider } from './type.js';
import { ServiceCollection } from './container.js';
import { logger } from './logger.js';

/**
 * Application host interface
 */
export interface IApplicationHost {
  ConfigureServices(
    configureServices: (services: IServiceCollection) => void
  ): IApplicationHost;
  Configure(configure: (app: IApplicationBuilder) => void): IApplicationHost;
  Build(): Express;
  Run(port?: number): Promise<void>;
}

/**
 * Application host implementation
 */
export class ApplicationHost implements IApplicationHost {
  private readonly _services: IServiceCollection;
  private _serviceProvider: IServiceProvider | null = null;
  private _configureApp: ((app: IApplicationBuilder) => void) | null = null;

  constructor() {
    this._services = new ServiceCollection();
  }

  /**
   * Configure services
   */
  public ConfigureServices(
    configureServices: (services: IServiceCollection) => void
  ): IApplicationHost {
    configureServices(this._services);
    return this;
  }

  /**
   * Configure application
   */
  public Configure(
    configure: (app: IApplicationBuilder) => void
  ): IApplicationHost {
    this._configureApp = configure;
    return this;
  }

  /**
   * Build the application
   */
  public Build(): Express {
    if (!this._configureApp) {
      throw new Error('Application configuration is required');
    }

    // Build service provider
    this._serviceProvider = this._services.BuildServiceProvider();

    // Create application builder
    const appBuilder = new ApplicationBuilder(this._serviceProvider);

    // Configure application
    this._configureApp(appBuilder);

    // Build application
    return appBuilder.build();
  }

  /**
   * Run the application
   */
  public async Run(port = 3000): Promise<void> {
    const app = this.Build();

    return new Promise<void>((resolve) => {
      app.listen(port, () => {
        logger.info(`Server is running on port ${port}`);
        resolve();
      });
    });
  }
}
