# c-visitor Framework

A comprehensive framework for building Node.js applications with Express, MongoDB, and more.

## Features

- Express integration with middleware support
- Dependency injection container
- Controller-based routing with decorators
- MongoDB integration with Mongoose
- Logging with Winston
- CORS configuration
- Error handling

## Building

Run `nx build framework` to build the library.

## MongoDB Integration

The framework includes a MongoDB integration that provides:

- Connection management
- Model and repository base classes
- Decorators for models and repositories
- Schema utilities
- Dependency injection support

See the [MongoDB Integration Documentation](./docs/mongodb-integration.md) for more details.

## Usage

```typescript
import { ApplicationHost, Container, RouterFactory } from '@c-visitor/framework';

// Create container
const container = new Container();

// Register services
container.register(MyService, (c) => new MyService());

// Create router factory
const routerFactory = new RouterFactory(container);

// Create application host
const host = new ApplicationHost(routerFactory);

// Add controllers
host.AddController(MyController);

// Run application
host.Run(3000).then(() => {
  console.log('Server running on port 3000');
});
```
