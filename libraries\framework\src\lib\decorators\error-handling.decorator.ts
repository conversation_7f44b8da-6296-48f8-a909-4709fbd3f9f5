import 'reflect-metadata';

// Metadata key
export const ERROR_HANDLER_METADATA = 'error:handlers';

// Interface cho error handler metadata
export interface ErrorHandlerMetadata {
  errorType: Function;
  handlerName: string | symbol;
}

/**
 * Decorator để đăng ký handler x<PERSON> lý lỗi cụ thể
 */
export function ErrorHandler(errorType: Function) {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    // L<PERSON>y danh sách error handlers hiện có
    const errorHandlers: ErrorHandlerMetadata[] =
      Reflect.getMetadata(ERROR_HANDLER_METADATA, target.constructor) || [];

    // Thêm handler mới
    errorHandlers.push({
      errorType,
      handlerName: propertyKey,
    });

    // Lưu lại metadata
    Reflect.defineMetadata(
      ERROR_HANDLER_METADATA,
      errorHandlers,
      target.constructor
    );

    return descriptor;
  };
}
