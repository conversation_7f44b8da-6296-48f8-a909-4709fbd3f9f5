import { memo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ApproveDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isSubmitting: boolean;
  onApprove: () => void;
}

/**
 * Dialog component for approving a request
 */
const ApproveDialogComponent = ({
  open,
  onOpenChange,
  isSubmitting,
  onApprove
}: ApproveDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[90vw] max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Confirm Access Registration</DialogTitle>
        </DialogHeader>
        <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-0.5'>
          <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1'>
            <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
              <span className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
             The system will update the access registration status to →{' '}
              </span>
              <span className='flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#23262f]'>
                Approved
              </span>
            </p>
          </div>
          <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1'>
            <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
              Are you sure you want to proceed?
            </p>
          </div>
        </div>
        <DialogFooter className='flex-col sm:flex-row gap-2'>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            className='w-full sm:w-auto'
          >
            Cancel
          </Button>
          <Button onClick={onApprove} disabled={isSubmitting} className='w-full sm:w-auto'>
            {isSubmitting ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Memoized version of ApproveDialog to prevent unnecessary re-renders
 * Only re-renders when props change
 */
export const ApproveDialog = memo(ApproveDialogComponent, (prevProps, nextProps) => {
  // Return true if passing nextProps to render would return
  // the same result as passing prevProps to render,
  // otherwise return false
  return (
    prevProps.open === nextProps.open &&
    prevProps.isSubmitting === nextProps.isSubmitting
    // We don't compare function props like onOpenChange and onApprove
    // because they should be wrapped in useCallback by the parent component
  )
});
