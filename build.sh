#!/bin/bash

# Display a message indicating the build process has started
echo "Starting build process..."

# Clean up previous build files
echo "Cleaning up previous build files..."
if [ -d "./apps/web/dist" ]; then
    echo "Removing web build directory..."
    rm -rf ./apps/web/dist
fi
if [ -d "./apps/server/dist" ]; then
    echo "Removing server build directory..."
    rm -rf ./apps/server/dist
fi
echo "Clean up completed"

# Step 1: Build web app
echo "Building web application..."
pnpm run build:prod:web
if [ $? -ne 0 ]; then
    echo "Error: Web application build failed"
    exit 1
fi
echo "Web application build completed successfully"

# Step 2: Build server app
echo "Building server application..."
pnpm run build:prod:server
if [ $? -ne 0 ]; then
    echo "Error: Server application build failed"
    exit 1
fi
echo "Server application build completed successfully"

# Step 3: Build Docker image for web app
echo "Building Docker image for web application..."
docker build -f ./apps/web/Dockerfile . -t nguyendkn/c-visitor-web:latest
if [ $? -ne 0 ]; then
    echo "Error: Docker image build for web application failed"
    exit 1
fi
echo "Docker image for web application built successfully"

# Step 4: Build Docker image for server app
echo "Building Docker image for server application..."
docker build -f ./apps/server/Dockerfile . -t nguyendkn/c-visitor-server:latest
if [ $? -ne 0 ]; then
    echo "Error: Docker image build for server application failed"
    exit 1
fi
echo "Docker image for server application built successfully"

echo "Build process completed successfully!"
