import mongoose from '@/configs/mongoose';
import Request, { RequestDocument } from '@/models/entities/Request';
import Reference, { ReferenceDocument } from '@/models/entities/Reference';
import User from '@/models/entities/User';
import { Service } from '@c-visitor/framework';
import { RequestStatus, IRequest } from '@c-visitor/types';
import { FilterQuery, ClientSession } from 'mongoose';
import { ListRequestQuery } from '@/models/queries/ListRequestQuery';
import { UpsertRequestCommand } from '@/models/commands/upsert-request.command';

/**
 * Repository for Request entity
 */
@Service()
export class RequestRepository {
  /**
   * Find a request by ID
   * @param id Request ID
   * @returns Request or null if not found
   */
  async findById(id: string): Promise<RequestDocument | null> {
    return Request.findById(id).exec();
  }

  /**
   * Find a request by code
   * @param code Request code
   * @returns Request or null if not found
   */
  async findByCode(code: number): Promise<RequestDocument | null> {
    return Request.findOne({ code }).exec();
  }

  /**
   * Find requests by company name
   * @param companyName Company name
   * @returns Array of requests for the specified company
   */
  async findByCompanyName(companyName: string): Promise<RequestDocument[]> {
    return Request.find({ companyName }).exec();
  }

  /**
   * Find requests by company ID
   * @param companyId Company ID
   * @returns Array of requests for the specified company
   */
  async findByCompanyId(companyId: string): Promise<RequestDocument[]> {
    return Request.find({ companyId }).exec();
  }

  /**
   * Find requests by status
   * @param status Request status
   * @returns Array of requests with the specified status
   */
  async findByStatus(status: RequestStatus): Promise<RequestDocument[]> {
    return Request.find({ status }).exec();
  }

  /**
   * Find requests by supervisor email
   * @param supervisorEmail Supervisor email
   * @returns Array of requests with the specified supervisor email
   */
  async findBySupervisorEmail(
    supervisorEmail: string,
  ): Promise<RequestDocument[]> {
    return Request.find({ supervisorEmail }).exec();
  }

  /**
   * Find all requests
   * @param options Query options
   * @returns Array of requests
   */
  async findAll(
    options: {
      limit?: number;
      skip?: number;
      sort?: Record<string, 1 | -1>;
      status?: RequestStatus;
    } = {},
  ): Promise<RequestDocument[]> {
    const { limit, skip, sort, status } = options;
    let query = Request.find();

    if (status) {
      query = query.find({ status });
    }

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new request
   * @param requestData Request data
   * @returns Created request
   */
  async create(
    requestData: Partial<RequestDocument>,
  ): Promise<RequestDocument> {
    const request = new Request(requestData);
    return request.save();
  }

  /**
   * Update a request
   * @param id Request ID
   * @param requestData Request data to update
   * @returns Updated request or null if not found
   */
  async update(
    id: string,
    requestData: Partial<RequestDocument>,
  ): Promise<RequestDocument | null> {
    return Request.findByIdAndUpdate(id, requestData, { new: true }).exec();
  }

  /**
   * Update request status
   * @param id Request ID
   * @param status New status
   * @param changeStatusBy User who changed the status
   * @returns Updated request or null if not found
   */
  async updateStatus(
    id: string,
    status: RequestStatus,
    changeStatusBy: string | object,
  ): Promise<RequestDocument | null> {
    return Request.findByIdAndUpdate(
      id,
      { status, changeStatusBy },
      { new: true },
    ).exec();
  }

  /**
   * Delete a request
   * @param id Request ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await Request.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Find a request by ID with populated references and user information
   * @param id Request ID
   * @returns Request with references and populated user data or null if not found
   */
  async findByIdWithReferences(id: string): Promise<RequestDocument | null> {
    const request = await Request.findById(id).exec();

    if (!request) {
      return null;
    }

    // Find references for this request
    const references = await Reference.find({ requestId: id }).exec();

    // Convert to plain object to add references
    const requestObj = request.toObject();

    // Map MongoDB _id to id for the request
    requestObj.id = (requestObj._id as mongoose.Types.ObjectId).toString();

    // Map references with proper id field
    requestObj.references = references.map((ref) => {
      const refObj = ref.toObject();
      return {
        ...refObj,
        id: (refObj._id as mongoose.Types.ObjectId).toString(),
      };
    });

    // Note: changeStatusBy field is now legacy, we focus on updatedBy field

    // Populate user information for updatedBy field
    // if (requestObj.updatedBy && typeof requestObj.updatedBy === 'string') {
    //   try {
    //     const user = await User.findById(requestObj.updatedBy).select('fullName email emailVerified active').exec();
    //     if (user) {
    //       // Create a new object with user info instead of replacing the string field
    //       (requestObj as any).updatedByUser = {
    //         id: (user._id as mongoose.Types.ObjectId).toString(),
    //         fullName: user.fullName,
    //         email: user.email,
    //         emailVerified: user.emailVerified,
    //         active: user.active,
    //       };
    //     }
    //   } catch (error) {
    //     console.error('Error populating updatedBy user:', error);
    //     // Keep the original string ID if user lookup fails
    //   }
    // }

    if (requestObj.updatedBy) {
      try {
        const user = await User.findById(requestObj.updatedBy)
          .select('fullName email emailVerified active')
          .exec();

        if (user) {
          requestObj.updatedByUser = {
            id: (user._id as mongoose.Types.ObjectId).toString(),
            fullName: user.fullName,
            email: user.email,
            emailVerified: user.emailVerified,
            active: user.active,
          };
        }
      } catch (error) {
        console.error('Error populating updatedBy user:', error);
      }
    }

    // Populate user information for createdBy field
    if (requestObj.createdBy) {
      try {
        const user = await User.findById(requestObj.createdBy)
          .select('fullName email emailVerified active')
          .exec();

        if (user) {
          requestObj.createdByUser = {
            id: (user._id as mongoose.Types.ObjectId).toString(),
            fullName: user.fullName,
            email: user.email,
            emailVerified: user.emailVerified,
            active: user.active,
          };
        }
      } catch (error) {
        console.error('Error populating createdBy user:', error);
      }
    }

    return requestObj;
  }

  /**
   * Get requests with pagination and filtering
   * @param params Query parameters
   * @returns Paginated requests
   */
  async getRequests(params: ListRequestQuery) {
    // Build filter query
    const filter: FilterQuery<RequestDocument> = {};

    // Add keyword search if provided (legacy support)
    if (params.keyword) {
      filter.$or = [
        { companyName: { $regex: params.keyword, $options: 'i' } },
        { supervisorName: { $regex: params.keyword, $options: 'i' } },
        { supervisorEmail: { $regex: params.keyword, $options: 'i' } },
        { contactName: { $regex: params.keyword, $options: 'i' } },
      ];
    }

    // Add new search functionality (supports code and supervisorName)
    if (params.search && params.search.trim()) {
      const searchTerm = params.search.trim();
      const searchConditions: any[] = [
        { supervisorName: { $regex: searchTerm, $options: 'i' } },
        { companyName: { $regex: searchTerm, $options: 'i' } },
        { contactName: { $regex: searchTerm, $options: 'i' } },
        { supervisorEmail: { $regex: searchTerm, $options: 'i' } },
        { purpose: { $regex: searchTerm, $options: 'i' } },
      ];

      // Handle code search separately since it's a number field
      // Check if search term is a number or contains numbers
      const codeNumber = parseInt(searchTerm, 10);
      if (!isNaN(codeNumber)) {
        // If it's a valid number, search for exact match or partial match
        searchConditions.push({ code: codeNumber });

        // Also search for codes that start with this number (for partial matches)
        // Convert to string and use regex on the string representation
        searchConditions.push({
          $expr: {
            $regexMatch: {
              input: { $toString: '$code' },
              regex: `^${codeNumber}`,
              options: 'i',
            },
          },
        });
      }

      filter.$or = searchConditions;
    }

    // Add status filter if provided
    if (params.status) {
      filter.status = params.status;
    }

    // Add date filters if provided (legacy support)
    if (params.timeIn || params.timeOut) {
      if (params.timeIn) {
        // Convert Date to ISO string for comparison with stored string values
        filter.timeIn = {
          $gte:
            params.timeIn instanceof Date
              ? params.timeIn.toISOString()
              : params.timeIn,
        };
      }

      if (params.timeOut) {
        // Convert Date to ISO string for comparison with stored string values
        filter.timeOut = {
          $lte:
            params.timeOut instanceof Date
              ? params.timeOut.toISOString()
              : params.timeOut,
        };
      }
    }

    // Add new clear date range filter (startDate/endDate)
    if (params.startDate || params.endDate) {
      const dateConditions: any[] = [];

      // Create date filter for timeIn (convert to ISO string for comparison)
      if (params.startDate || params.endDate) {
        const timeInFilter: any = {};
        if (params.startDate) {
          // Convert Date to ISO string for comparison with stored string values
          timeInFilter.$gte = params.startDate.toISOString();
        }
        if (params.endDate) {
          // Add 23:59:59 to the end date to include the entire day
          const endOfDay = new Date(params.endDate);
          endOfDay.setHours(23, 59, 59, 999);
          timeInFilter.$lte = endOfDay.toISOString();
        }
        dateConditions.push({ timeIn: timeInFilter });
      }

      // Create date filter for timeOut (convert to ISO string for comparison)
      if (params.startDate || params.endDate) {
        const timeOutFilter: any = {};
        if (params.startDate) {
          // Convert Date to ISO string for comparison with stored string values
          timeOutFilter.$gte = params.startDate.toISOString();
        }
        if (params.endDate) {
          // Add 23:59:59 to the end date to include the entire day
          const endOfDay = new Date(params.endDate);
          endOfDay.setHours(23, 59, 59, 999);
          timeOutFilter.$lte = endOfDay.toISOString();
        }
        dateConditions.push({ timeOut: timeOutFilter });
      }

      // Use $or to match either timeIn or timeOut within the date range
      if (filter.$or && filter.$or.length > 0) {
        // If there's already an $or condition (from search), combine them with $and
        filter.$and = [{ $or: filter.$or }, { $or: dateConditions }];
        delete filter.$or;
      } else {
        filter.$or = dateConditions;
      }
    }
    // Legacy date range filter (for backward compatibility)
    else if (params.timeInFrom || params.timeInTo) {
      const dateConditions: any[] = [];

      // Create date filter for timeIn (convert to ISO string for comparison)
      if (params.timeInFrom || params.timeInTo) {
        const timeInFilter: any = {};
        if (params.timeInFrom) {
          // Convert Date to ISO string for comparison with stored string values
          timeInFilter.$gte = params.timeInFrom.toISOString();
        }
        if (params.timeInTo) {
          // Add 23:59:59 to the end date to include the entire day
          const endOfDay = new Date(params.timeInTo);
          endOfDay.setHours(23, 59, 59, 999);
          timeInFilter.$lte = endOfDay.toISOString();
        }
        dateConditions.push({ timeIn: timeInFilter });
      }

      // Create date filter for timeOut (convert to ISO string for comparison)
      if (params.timeInFrom || params.timeInTo) {
        const timeOutFilter: any = {};
        if (params.timeInFrom) {
          // Convert Date to ISO string for comparison with stored string values
          timeOutFilter.$gte = params.timeInFrom.toISOString();
        }
        if (params.timeInTo) {
          // Add 23:59:59 to the end date to include the entire day
          const endOfDay = new Date(params.timeInTo);
          endOfDay.setHours(23, 59, 59, 999);
          timeOutFilter.$lte = endOfDay.toISOString();
        }
        dateConditions.push({ timeOut: timeOutFilter });
      }

      // Use $or to match either timeIn or timeOut within the date range
      if (filter.$or && filter.$or.length > 0) {
        // If there's already an $or condition (from search), combine them with $and
        filter.$and = [{ $or: filter.$or }, { $or: dateConditions }];
        delete filter.$or;
      } else {
        filter.$or = dateConditions;
      }
    }

    // Debug: Log the final filter
    console.log('MongoDB Filter:', JSON.stringify(filter, null, 2));
    console.log('Date Filter Params:', {
      startDate: params.startDate,
      endDate: params.endDate,
      startDateISO: params.startDate?.toISOString(),
      endDateISO: params.endDate?.toISOString(),
    });

    // Set up pagination options
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const skip = (pageIndex - 1) * pageSize;

    // Execute count query
    const countPromise = Request.countDocuments(filter).exec();

    // Execute find query with pagination
    const itemsPromise = Request.find(filter)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(pageSize)
      .exec();

    // Wait for both queries to complete
    const [total, items] = await Promise.all([countPromise, itemsPromise]);

    // Calculate total pages
    const totalPages = Math.ceil(total / pageSize);

    // Map MongoDB _id to id for all items
    const mappedItems = items.map((item) => {
      const itemObj = item.toObject();
      itemObj.id = (itemObj._id as mongoose.Types.ObjectId).toString();
      return itemObj;
    });

    // Return paginated result
    return {
      items: mappedItems,
      total,
      pageIndex,
      pageSize,
      totalPages,
    };
  }

  /**
   * Execute a transaction
   * @param callback Function to execute within the transaction
   * @returns Result of the callback function
   */
  async withTransaction<T>(
    callback: (session: ClientSession) => Promise<T>,
  ): Promise<T> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const result = await callback(session);
      await session.commitTransaction();
      return result;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Upsert a request with references (simplified for create-only operations)
   * @param command Request and references data
   * @param session Optional MongoDB session for transactions
   * @returns Created request with references
   */
  async upsertAsync(
    command: UpsertRequestCommand,
    session?: ClientSession,
  ): Promise<IRequest> {
    // Use transaction for data consistency
    const useSession = session
      ? async (callback: (session: ClientSession) => Promise<IRequest>) =>
          callback(session)
      : this.withTransaction.bind(this);

    return useSession(async (session) => {
      const { request, references } = command;

      // Prepare clean request data (remove any IDs to let MongoDB auto-generate)
      const requestData = {
        ...request,
        // Remove any ID fields - MongoDB will auto-generate _id
      };
      delete (requestData as any).id;
      delete (requestData as any)._id;

      // Create new request - MongoDB auto-generates _id
      const savedRequest = await Request.create([requestData], {
        session,
        ordered: true,
      }).then((docs) => docs[0]);

      // Process references if any
      const savedReferences: ReferenceDocument[] = [];

      if (references && references.length > 0) {
        // Prepare references data
        const referencesData = references.map((ref) => {
          const refData = {
            ...ref,
            requestId: (savedRequest._id as any).toString(), // Link to the created request
          };
          // Remove any ID fields - MongoDB will auto-generate _id
          delete (refData as any).id;
          delete (refData as any)._id;
          return refData;
        });

        // Create all references in batch with ordered: true for session support
        const createdReferences = await Reference.create(referencesData, {
          session,
          ordered: true,
        });
        savedReferences.push(...createdReferences);
      }

      // Convert to response format
      const result = savedRequest.toObject();
      result.id = (result._id as any).toString();
      result.references = savedReferences.map((ref) => {
        const refObj = ref.toObject();
        return {
          ...refObj,
          id: (refObj._id as any).toString(),
        };
      });

      return result as IRequest;
    });
  }
}
