{"name": "@c-visitor/server", "version": "0.0.1", "private": true, "scripts": {"dev": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/main.ts", "debug": "node --inspect --require ts-node/register --require tsconfig-paths/register src/main.ts", "start": "node dist/main.js", "seed": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/seed.ts seed", "seed:fresh": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/seed.ts fresh", "seed:reset": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/seed.ts reset", "test:migration": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/test-migration.ts", "test:server": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/test-server.ts", "test:services": "ts-node --project tsconfig.app.json -r tsconfig-paths/register src/scripts/test-services.ts"}, "nx": {"targets": {"serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@c-visitor/server:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@c-visitor/server:build:development"}, "production": {"buildTarget": "@c-visitor/server:build:production"}}}, "build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "apps/server/dist", "main": "apps/server/src/main.ts", "tsConfig": "apps/server/tsconfig.app.json", "format": ["cjs"], "declaration": false, "minify": false, "sourcemap": true}, "configurations": {"development": {"minify": false, "sourcemap": true}, "production": {"outputFile": "apps/server/dist/main.js", "minify": true, "sourcemap": false}}}}}, "dependencies": {"@c-visitor/framework": "workspace:^", "@c-visitor/shared": "workspace:^", "@c-visitor/types": "workspace:^", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "mongoose": "^8.15.0", "mqtt": "^5.13.0", "multer": "^2.0.0", "nodemailer": "^7.0.3", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "webpack-cli": "^6.0.1"}}