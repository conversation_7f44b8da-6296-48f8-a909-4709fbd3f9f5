import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { UserAttributes } from '@c-visitor/types';

// User interface for Mongoose document
export interface UserDocument extends Omit<UserAttributes, 'id'>, Document {}

// User schema for Mongoose
const UserSchema = new Schema<UserDocument>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      maxlength: 255,
    },
    fullName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 255,
    },
    password: {
      type: String,
      required: true,
      maxlength: 255,
    },
    emailVerified: {
      type: Boolean,
      required: true,
      default: false,
    },
    verificationToken: {
      type: String,
      maxlength: 255,
    },
    verificationTokenExpiry: {
      type: Date,
    },
    resetPasswordToken: {
      type: String,
      maxlength: 255,
    },
    resetPasswordTokenExpiry: {
      type: Date,
    },
    refreshToken: {
      type: String,
      maxlength: 255,
    },
    active: {
      type: Boolean,
      required: true,
      default: true,
    }
  },
  { timestamps: true, collection: 'users' },
);

// Create indexes for frequently queried fields
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ verificationToken: 1 });
UserSchema.index({ resetPasswordToken: 1 });
UserSchema.index({ active: 1 });

// Create the User model
const UserModel = mongoose.model<UserDocument>('User', UserSchema);

export default UserModel;
