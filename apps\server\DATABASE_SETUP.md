# Database Setup Guide

## 📋 Overview

C-Visitor sử dụng MongoDB với database name **"C-Visitor"** và có hệ thống seeding tự động để tạo dữ liệu mặc định.

## 🗄️ Database Structure

### Default Database: `C-Visitor`

### Collections:
- **roles** - <PERSON><PERSON> trò người dùng
- **users** - Thông tin người dùng  
- **user_roles** - Liên kết user và role

## 👥 Default Data

### Roles:
- **Admin** - Quản trị viên hệ thống
- **Staff** - Nhân viên
- **User** - Người dùng thông thường

### Admin User:
- **Email**: `<EMAIL>`
- **Password**: `PasS@W0rd`
- **Role**: Admin
- **Status**: Email verified, Active

## 🚀 Auto Seeding

Khi server khởi động, hệ thống sẽ tự động:

1. ✅ <PERSON>ể<PERSON> tra và tạo roles nếu chưa tồn tại
2. ✅ <PERSON>ểm tra và tạo admin user nếu chưa tồn tại  
3. ✅ Gán role Admin cho admin user
4. ✅ Không ghi đè dữ liệu đã tồn tại (safe seeding)

## 🛠️ Manual Seeding Commands

### Seed an toàn (không xóa dữ liệu cũ):
```bash
cd apps/server
npm run seed
```

### Seed fresh (XÓA TẤT CẢ dữ liệu và tạo mới):
```bash
cd apps/server
npm run seed:fresh
```

### Reset database (XÓA TẤT CẢ dữ liệu):
```bash
cd apps/server
npm run seed:reset
```

## 🔧 Configuration

### Environment Variables:

**Development:**
```bash
DEVELOPMENT_DB_CONNECTION=mongodb+srv://user:<EMAIL>/C-Visitor
```

**Production:**
```bash
PRODUCTION_DB_CONNECTION=mongodb+srv://user:<EMAIL>/C-Visitor
```

## 📝 Usage Examples

### 1. First Time Setup:
```bash
# Start server (auto-seeding will run)
npm run dev

# Or manual seed first
npm run seed
npm run dev
```

### 2. Development Reset:
```bash
# Reset and create fresh data
npm run seed:fresh

# Start development server
npm run dev
```

### 3. Production Deployment:
```bash
# Build application
npm run build

# Seed production database (if needed)
NODE_ENV=production npm run seed

# Start production server
NODE_ENV=production npm start
```

## 🔐 Security Notes

### Development:
- ✅ Default admin credentials are acceptable
- ✅ Auto-seeding is enabled

### Production:
- ⚠️  **CHANGE admin password immediately**
- ⚠️  Consider disabling auto-seeding
- ⚠️  Use strong database credentials
- ⚠️  Enable database authentication

## 🔍 Verification

### Check if seeding worked:

1. **Connect to MongoDB:**
```bash
# Using MongoDB Compass or CLI
mongodb://your-connection-string/C-Visitor
```

2. **Verify collections:**
```javascript
// Check roles
db.roles.find()

// Check admin user
db.users.findOne({email: "<EMAIL>"})

// Check user roles
db.user_roles.find()
```

3. **Test login:**
```bash
curl -X POST http://localhost:5000/api/identity/signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "PasS@W0rd"
  }'
```

## 🚨 Troubleshooting

### Common Issues:

1. **Connection Error:**
   - Check MongoDB connection string
   - Verify network access to MongoDB cluster
   - Check credentials

2. **Seeding Failed:**
   - Check database permissions
   - Verify collection names
   - Check for duplicate key errors

3. **Admin Login Failed:**
   - Verify user was created: `db.users.findOne({email: "<EMAIL>"})`
   - Check password hash
   - Verify email is marked as verified

### Debug Commands:
```bash
# Enable debug logging
NODE_ENV=development npm run dev

# Check database connection
npm run seed

# Reset and try again
npm run seed:fresh
```

## 📊 Database Schema

### Users Collection:
```javascript
{
  _id: ObjectId,
  email: String (unique),
  fullName: String,
  password: String (hashed),
  emailVerified: Boolean,
  active: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Roles Collection:
```javascript
{
  _id: ObjectId,
  name: String (unique),
  active: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### UserRoles Collection:
```javascript
{
  _id: ObjectId,
  userId: ObjectId (ref: User),
  roleId: ObjectId (ref: Role),
  active: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```
