import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import { toast } from 'sonner';
import { APIResponse } from '@c-visitor/types';
import { forceLogout } from '@/utils/token-validator';

// Get API URL from environment (Vite uses import.meta.env)
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

/**
 * Create a configured axios instance for API requests
 */
export const createAxiosInstance = (
  config?: AxiosRequestConfig,
): AxiosInstance => {
  const axiosInstance = axios.create({
    baseURL: API_URL,
    timeout: 30000, // 30 seconds
    headers: {
      'Content-Type': 'application/json',
    },
    ...config,
  });

  // Request interceptor
  axiosInstance.interceptors.request.use(
    (config) => {
      // Get token from localStorage if available
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );

  // Response interceptor with auto token refresh
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error: AxiosError<APIResponse>) => {
      const originalRequest = error.config as any;

      // Handle authentication errors
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Try to refresh token
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            const refreshResponse = await fetch(`${API_URL}/api/identity/refresh-token`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ refreshToken }),
            });

            if (refreshResponse.ok) {
              const data = await refreshResponse.json();

              // Update tokens in localStorage
              localStorage.setItem('auth_token', data.data.accessToken);
              localStorage.setItem('refresh_token', data.data.refreshToken);

              // Update the original request with new token
              if (originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${data.data.accessToken}`;
              }

              // Retry the original request
              return axiosInstance(originalRequest);
            } else {
              // Refresh failed, logout user
              forceLogout('Your session has expired. Please login again.');
              return Promise.reject(error);
            }
          } else {
            // No refresh token, logout user
            forceLogout('Your session has expired. Please login again.');
            return Promise.reject(error);
          }
        } catch (refreshError) {
          // Refresh failed, logout user
          forceLogout('Your session has expired. Please login again.');
          return Promise.reject(error);
        }
      }

      // Handle other API errors
      const errorResponse = error.response?.data;

      // Show toast notification for errors (but not for 401 that we handled above)
      if (error.response?.status !== 401) {
        if (errorResponse?.message) {
          toast.error(errorResponse.message);
        } else if (error.message) {
          toast.error(`Request failed: ${error.message}`);
        }
      }

      return Promise.reject(error);
    },
  );

  return axiosInstance;
};

// Create default axios instance
export const axiosClient = createAxiosInstance();

/**
 * Generic API request function with type safety
 */
export async function apiRequest<TData = any, TParams = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: TParams,
  config?: AxiosRequestConfig,
): Promise<TData> {
  try {
    let response: AxiosResponse<APIResponse<TData>>;

    switch (method) {
      case 'GET':
        response = await axiosClient.get<APIResponse<TData>>(url, {
          params: data,
          ...config,
        });
        break;
      case 'POST':
        response = await axiosClient.post<APIResponse<TData>>(
          url,
          data,
          config,
        );
        break;
      case 'PUT':
        response = await axiosClient.put<APIResponse<TData>>(url, data, config);
        break;
      case 'PATCH':
        response = await axiosClient.patch<APIResponse<TData>>(
          url,
          data,
          config,
        );
        break;
      case 'DELETE':
        response = await axiosClient.delete<APIResponse<TData>>(url, {
          data,
          ...config,
        });
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }

    // Return the data property from the API response
    return response.data.data as TData;
  } catch (error) {
    // Re-throw the error to be handled by the caller
    throw error;
  }
}

/**
 * Convenience methods for common API operations
 */
export const api = {
  get: <TData = any, TParams = any>(
    url: string,
    params?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('GET', url, params, config),

  post: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('POST', url, data, config),

  put: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('PUT', url, data, config),

  patch: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('PATCH', url, data, config),

  delete: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('DELETE', url, data, config),
};

export default api;
