import type { Request, Response } from 'express';
import { Service, ErrorHandler } from './decorators.js';
import {
  ForbiddenError,
  HttpError,
  NotFoundError,
  UnauthorizedError,
  ValidationError,
} from './http-error.js';
import { ValidationUtils } from './utilities/validation.js';
import { ResponseUtils } from './utilities/response.js';
import { ClaimsPrincipal } from './claims-principal.js';
import { logger } from './logger.js';

@Service()
export abstract class ControllerBase {
  /**
   * Xử lý lỗi HttpError
   */
  @ErrorHandler(HttpError)
  protected handleHttpError(error: HttpError, _req: Request, res: Response) {
    return ResponseUtils.sendError(
      res,
      error.message,
      error.statusCode,
      error.code,
      error.details
    );
  }

  /**
   * Xử lý lỗi ValidationError
   */
  @ErrorHandler(ValidationError)
  protected handleValidation(
    error: ValidationError,
    _req: Request,
    res: Response
  ) {
    return ResponseUtils.sendError(
      res,
      error.message,
      400,
      error.code,
      error.details
    );
  }

  /**
   * <PERSON><PERSON> lý lỗi chung
   */
  @ErrorHandler(Error)
  protected handleGenericError(error: Error, _req: Request, res: Response) {
    logger.error(`[Error] ${error.message}`, { stack: error.stack });
    return ResponseUtils.sendError(
      res,
      'An unexpected error occurred',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }

  /**
   * Phương thức tiện ích để tạo response thành công
   */
  protected text(res: Response, text?: string, statusCode = 200) {
    return ResponseUtils.sendText(res, text, statusCode);
  }

  /**
   * Phương thức tiện ích để tạo response thành công
   */
  protected success<T>(
    res: Response,
    data?: T,
    message = 'Success',
    statusCode = 200
  ) {
    return ResponseUtils.sendSuccess(res, data, message, statusCode);
  }

  protected badRequest(
    res: Response,
    message = 'Bad Request',
    statusCode = 400
  ) {
    return ResponseUtils.sendError(res, message, statusCode, 'BAD_REQUEST');
  }

  /**
   * Phương thức tiện ích để tạo response thành công với dữ liệu đã được tạo
   */
  protected created<T>(
    res: Response,
    data?: T,
    message = 'Resource created successfully'
  ) {
    return ResponseUtils.sendCreated(res, data, message);
  }

  /**
   * Phương thức tiện ích để tạo response lỗi
   */
  protected error(
    res: Response,
    message: string,
    statusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[]
  ) {
    return ResponseUtils.sendError(res, message, statusCode, code, details);
  }

  protected notFound(res: Response, message = 'Resource not found') {
    return ResponseUtils.sendError(res, message, 404, 'NOT_FOUND');
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi không tìm thấy tài nguyên
   */
  protected notFoundIf(condition: boolean, message = 'Resource not found') {
    if (condition) {
      throw new NotFoundError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi điều kiện xác thực không hợp lệ
   */
  protected unauthorizedIf(
    condition: boolean,
    message = 'Unauthorized access'
  ) {
    if (condition) {
      throw new UnauthorizedError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi không có quyền truy cập
   */
  protected forbiddenIf(condition: boolean, message = 'Forbidden access') {
    if (condition) {
      throw new ForbiddenError(message);
    }
  }

  /**
   * Phương thức tiện ích để kiểm tra và xử lý khi dữ liệu không hợp lệ
   */
  protected validateIf(
    condition: boolean,
    message = 'Validation failed',
    details?: any[]
  ) {
    if (condition) {
      throw new ValidationError(message, details);
    }
  }

  /**
   * Validate required fields in request data
   */
  protected validateRequiredFields(
    data: Record<string, any>,
    requiredFields: string[]
  ): void {
    ValidationUtils.validateRequiredFields(data, requiredFields);
  }

  /**
   * Sanitize request data to prevent injection attacks
   */
  protected sanitizeData<T extends Record<string, any>>(data: T): T {
    return ValidationUtils.sanitizeObject(data);
  }

  /**
   * Get the current authenticated user from the request
   * @param req Express request object
   * @returns The authenticated user or null if not authenticated
   */
  protected getCurrentUser(req: Request): any | null {
    return (req as any).user || null;
  }

  /**
   * Get the ClaimsPrincipal from the request
   * @param req Express request object
   * @returns The ClaimsPrincipal or null if not authenticated
   */
  protected getClaimsPrincipal(req: Request): ClaimsPrincipal | null {
    return (req as any).claimsPrincipal || null;
  }

  /**
   * Check if the current user has a specific claim
   * @param req Express request object
   * @param claimType The type of the claim to check
   * @param claimValue Optional value to compare against (if not provided, checks for existence)
   * @returns True if the user has the claim, false otherwise
   */
  protected userHasClaim(
    req: Request,
    claimType: string,
    claimValue?: string
  ): boolean {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return false;

    return principal.hasClaim(claimType, claimValue);
  }

  /**
   * Check if the current user has any of the specified roles
   * @param req Express request object
   * @param roles Array of role names to check
   * @returns True if the user has any of the roles, false otherwise
   */
  protected userHasRole(req: Request, roles: string[]): boolean {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return false;

    return roles.some((role) => principal.isInRole(role));
  }

  /**
   * Get a claim value from the current user
   * @param req Express request object
   * @param claimType The type of the claim to get
   * @returns The claim value or undefined if not found
   */
  protected getClaimValue(req: Request, claimType: string): string | undefined {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return undefined;

    const claim = principal.findFirst(claimType);
    return claim?.value;
  }

  /**
   * Get all claim values of a specific type from the current user
   * @param req Express request object
   * @param claimType The type of the claims to get
   * @returns Array of claim values
   */
  protected getClaimValues(req: Request, claimType: string): string[] {
    const principal = this.getClaimsPrincipal(req);
    if (!principal) return [];

    return principal.findAll(claimType).map((claim) => claim.value);
  }

  /**
   * Get the user ID from the current user
   * @param req Express request object
   * @returns The user ID or undefined if not authenticated
   */
  protected getUserId(req: Request): string | undefined {
    return this.getClaimValue(req, 'sub') || this.getClaimValue(req, 'id');
  }

  /**
   * Get the user name from the current user
   * @param req Express request object
   * @returns The user name or undefined if not authenticated
   */
  protected getUserName(req: Request): string | undefined {
    const principal = this.getClaimsPrincipal(req);
    if (!principal || !principal.identity) return undefined;

    return principal.identity.name;
  }
}
