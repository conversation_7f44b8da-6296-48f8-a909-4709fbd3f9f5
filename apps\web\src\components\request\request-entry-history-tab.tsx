import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/shared/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { ReferenceDetailsDrawer } from '@/components/reference-details-drawer';
import { getImageUrl } from '@/utils/file-utils';
import { IReference } from '@c-visitor/types';

interface ReferenceWithRecognitionStats {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  unit: string;
  cardIdNumber: string;
  cardIdFront: string;
  cardIdBack: string;
  avatar?: string;
  recognitionCount: number;
  firstCheckIn: Date | null;
  lastCheckOut: Date | null;
}

interface RequestEntryHistoryTabProps {
  references: IReference[];
  isLoadingRecognitions: boolean;
  totalPages?: number;
}

/**
 * Tab component that displays the entry history for all references in the request
 */
export function RequestEntryHistoryTab({
  references,
  isLoadingRecognitions,
  totalPages,
}: RequestEntryHistoryTabProps) {
  // State for pagination
  const [recognitionPageIndex, setRecognitionPageIndex] = useState(1);
  const [recognitionPageSize, setRecognitionPageSize] = useState(5);
  const [recognitionPageCount, setRecognitionPageCount] = useState(
    totalPages || 1,
  );

  // State for reference details drawer
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedReference, setSelectedReference] = useState<any | null>(null);

  // Transform IReference[] to ReferenceWithRecognitionStats[]
  const referencesWithStats: ReferenceWithRecognitionStats[] = references.map(
    (ref) => ({
      id: ref.id,
      fullName: ref.fullName,
      email: ref.email,
      phone: ref.phone,
      unit: ref.unit,
      avatar: ref.avatar,
      cardIdNumber: ref.cardIdNumber,
      cardIdFront: ref.cardIdFront,
      cardIdBack: ref.cardIdBack,
      recognitionCount: 0, // TODO: Get from API
      firstCheckIn: null, // TODO: Get from API
      lastCheckOut: null, // TODO: Get from API
    }),
  );

  // Handle view details for a reference
  const handleViewReferenceDetails = (reference: any) => {
    setSelectedReference(reference);
    setIsDrawerOpen(true);
  };

  // Format date time function
  const formatDateTime = (date: Date | null) => {
    if (!date) return '-';
    return new Date(date).toLocaleString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Define columns for the recognition history table (like the old version)
  const recognitionColumns: ColumnDef<ReferenceWithRecognitionStats>[] = [
    {
      accessorKey: 'fullName',
      header: 'Visitor',
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <div className="flex justify-start items-center flex-grow gap-2">
              {!item.avatar ? (
                <div className="flex items-center">
                  <div className="bg-[#008FD31A] text-[#008FD3] flex items-center justify-center rounded-full px-1.5 py-1.5 mr-3 min-w-[32px] max-h-[32px]">
                    <span className="text-[12px] font-bold">
                      {(() => {
                        const words =
                          item.fullName?.split(' ').filter(Boolean) || [];
                        if (words.length === 1) {
                          return words[0].slice(0, 2).toUpperCase();
                        } else if (words.length > 1) {
                          return (
                            words[0][0] + words[words.length - 1][0]
                          ).toUpperCase();
                        }
                        return '';
                      })()}
                    </span>
                  </div>
                </div>
              ) : (
                <img
                  src={getImageUrl(item.avatar)}
                  alt={item.fullName}
                  className="w-8 h-8 rounded-full mr-3"
                />
              )}
              <div className="flex flex-col justify-center items-start flex-grow relative">
                <p className="self-stretch flex-grow-0 flex-shrink-0 w-[148.6px] text-[13px] font-medium text-left text-[#26282c]">
                  {item.fullName}
                </p>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1.5">
                  <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]">
                    {item.phone}
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'recognitionCount',
      header: 'Number of Recognitions',
      cell: ({ row }) => {
        return (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              {row.original.recognitionCount}
            </p>
          </div>
        );
      },
    },
    {
      accessorKey: 'firstCheckIn',
      header: 'Check-in Time',
      cell: ({ row }) => {
        return (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              {formatDateTime(row.original.firstCheckIn)}
            </p>
          </div>
        );
      },
    },
    {
      accessorKey: 'lastCheckOut',
      header: 'Check-out Time',
      cell: ({ row }) => {
        return (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2">
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#1f2329]">
              {formatDateTime(row.original.lastCheckOut)}
            </p>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        return (
          <div className="flex justify-start items-center self-stretch flex-grow px-3 py-2 gap-2">
            <button
              className="cursor-pointer font-semibold text-[#008FD3] hover:text-[#006ba3]"
              onClick={() => handleViewReferenceDetails(row.original)}
            >
              Details
            </button>
          </div>
        );
      },
    },
  ];

  // Handle pagination change
  const handleRecognitionPaginationChange = (
    newPageIndex: number,
    newPageSize: number,
  ) => {
    // If page size changes, reset to first page
    if (newPageSize !== recognitionPageSize) {
      setRecognitionPageIndex(1);
    } else {
      setRecognitionPageIndex(newPageIndex + 1); // API uses 1-based indexing
    }

    setRecognitionPageSize(newPageSize);
  };

  // Update page count when totalPages changes
  useEffect(() => {
    if (totalPages) {
      setRecognitionPageCount(totalPages);
    }
  }, [totalPages]);

  return (
    <div>
      <Card className="w-full max-w-full">
        <CardContent className="w-full">
          <div className="flex flex-col justify-start items-start w-full overflow-hidden gap-9rounded-lg bg-white ">
            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative gap-2">
                <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#8f959e]">
                  VISITOR LIST
                </p>
              </div>

              <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-1">
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#646a73]">
                    Total:
                  </p>
                </div>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-semibold text-left text-[#1f2329]">
                    {references?.length || 0} people
                  </p>
                </div>
              </div>

              {isLoadingRecognitions ? (
                <div className="flex justify-center items-center py-8 w-full">
                  <p>Loading data...</p>
                </div>
              ) : referencesWithStats.length > 0 ? (
                <DataTable
                  columns={recognitionColumns}
                  data={referencesWithStats}
                  pageCount={recognitionPageCount}
                  pageSize={recognitionPageSize}
                  pageIndex={recognitionPageIndex - 1} // API uses 1-based indexing, DataTable uses 0-based
                  // onPaginationChange={handleRecognitionPaginationChange}
                  sizeChanger={true}
                />
              ) : (
                <div className="flex justify-center items-center py-8 w-full">
                  <p>No recognition history data available</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reference Details Drawer */}
      <ReferenceDetailsDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        referenceData={selectedReference || null}
      />
    </div>
  );
}
