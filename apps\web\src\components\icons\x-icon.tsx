interface XIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export function XIcon({ 
  className = "w-5 h-5", 
  width = 20, 
  height = 20 
}: XIconProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <path
        d="M17.3151 17.315C17.4713 17.1587 17.5591 16.9468 17.5591 16.7258C17.5591 16.5048 17.4713 16.2929 17.3151 16.1366L11.1784 9.99998L17.3151 3.86331C17.4669 3.70614 17.5509 3.49564 17.549 3.27714C17.5471 3.05864 17.4594 2.84963 17.3049 2.69513C17.1504 2.54062 16.9414 2.45298 16.7229 2.45108C16.5044 2.44918 16.2939 2.53318 16.1367 2.68498L10.0001 8.82164L3.8634 2.68498C3.70624 2.53318 3.49573 2.44918 3.27724 2.45108C3.05874 2.45298 2.84973 2.54062 2.69522 2.69513C2.54072 2.84963 2.45307 3.05864 2.45118 3.27714C2.44928 3.49564 2.53327 3.70614 2.68507 3.86331L8.82174 9.99998L2.68507 16.1366C2.60548 16.2135 2.54199 16.3055 2.49832 16.4071C2.45465 16.5088 2.43166 16.6182 2.4307 16.7288C2.42973 16.8395 2.45082 16.9492 2.49272 17.0516C2.53462 17.154 2.5965 17.2471 2.67474 17.3253C2.75299 17.4035 2.84603 17.4654 2.94844 17.5073C3.05086 17.5492 3.16059 17.5703 3.27124 17.5694C3.38189 17.5684 3.49124 17.5454 3.59291 17.5017C3.69458 17.4581 3.78653 17.3946 3.8634 17.315L10.0001 11.1783L16.1367 17.315C16.293 17.4712 16.5049 17.559 16.7259 17.559C16.9469 17.559 17.1588 17.4712 17.3151 17.315Z"
        fill="#646A73"
      />
    </svg>
  );
}
