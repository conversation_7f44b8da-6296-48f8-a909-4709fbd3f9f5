/**
 * @fileoverview Date prototype extensions to enhance date functionality
 *
 * This module extends the native Date prototype with additional utility methods
 * for common date manipulation tasks. These extensions make date operations
 * more concise and readable throughout the application.
 *
 * @module @c-visitor/shared/lib/prototypes/Date.prototype
 * <AUTHOR> Development Team
 * @version 1.0.0
 *
 * @example
 * // Import in your application's entry point
 * import '@c-visitor/shared';
 *
 * // Then use the extensions anywhere in your code
 * const formattedDate = new Date().toFormattedString('MM/dd/yyyy'); // "01/15/2023"
 * const isWeekend = new Date().isWeekend(); // true or false
 */

// Extend the Date interface to include our new methods
declare global {
  interface Date {
    /**
     * Formats the date according to the specified format string
     *
     * @param format The format string (e.g., 'MM/dd/yyyy', 'yyyy-MM-dd')
     * @param locale The locale to use for formatting (default: system locale)
     * @returns A formatted date string
     * @category Formatting
     * @example
     * ```typescript
     * new Date(2023, 0, 15).toFormattedString('MM/dd/yyyy'); // Returns "01/15/2023"
     * new Date(2023, 0, 15).toFormattedString('yyyy-MM-dd'); // Returns "2023-01-15"
     * new Date(2023, 0, 15).toFormattedString('MMMM d, yyyy'); // Returns "January 15, 2023"
     * ```
     */
    toFormattedString(format: string, locale?: string): string;

    /**
     * Checks if the date is a weekend (Saturday or Sunday)
     *
     * @returns True if the date is a weekend, false otherwise
     * @category Utility
     * @example
     * ```typescript
     * new Date(2023, 0, 14).isWeekend(); // Returns true (Saturday)
     * new Date(2023, 0, 16).isWeekend(); // Returns false (Monday)
     * ```
     */
    isWeekend(): boolean;

    /**
     * Checks if the date is a weekday (Monday through Friday)
     *
     * @returns True if the date is a weekday, false otherwise
     * @category Utility
     * @example
     * ```typescript
     * new Date(2023, 0, 16).isWeekday(); // Returns true (Monday)
     * new Date(2023, 0, 14).isWeekday(); // Returns false (Saturday)
     * ```
     */
    isWeekday(): boolean;

    /**
     * Adds the specified number of days to the date
     *
     * @param days The number of days to add
     * @returns A new Date object with the days added
     * @category Manipulation
     * @example
     * ```typescript
     * new Date(2023, 0, 15).addDays(5); // Returns a Date representing January 20, 2023
     * new Date(2023, 0, 15).addDays(-5); // Returns a Date representing January 10, 2023
     * ```
     */
    addDays(days: number): Date;

    /**
     * Adds the specified number of months to the date
     *
     * @param months The number of months to add
     * @returns A new Date object with the months added
     * @category Manipulation
     * @example
     * ```typescript
     * new Date(2023, 0, 15).addMonths(1); // Returns a Date representing February 15, 2023
     * new Date(2023, 0, 15).addMonths(-1); // Returns a Date representing December 15, 2022
     * ```
     */
    addMonths(months: number): Date;

    /**
     * Adds the specified number of years to the date
     *
     * @param years The number of years to add
     * @returns A new Date object with the years added
     * @category Manipulation
     * @example
     * ```typescript
     * new Date(2023, 0, 15).addYears(1); // Returns a Date representing January 15, 2024
     * new Date(2023, 0, 15).addYears(-1); // Returns a Date representing January 15, 2022
     * ```
     */
    addYears(years: number): Date;

    /**
     * Checks if the date is before another date
     *
     * @param date The date to compare with
     * @returns True if this date is before the specified date, false otherwise
     * @category Comparison
     * @example
     * ```typescript
     * new Date(2023, 0, 15).isBefore(new Date(2023, 0, 16)); // Returns true
     * new Date(2023, 0, 15).isBefore(new Date(2023, 0, 15)); // Returns false
     * new Date(2023, 0, 15).isBefore(new Date(2023, 0, 14)); // Returns false
     * ```
     */
    isBefore(date: Date): boolean;

    /**
     * Checks if the date is after another date
     *
     * @param date The date to compare with
     * @returns True if this date is after the specified date, false otherwise
     * @category Comparison
     * @example
     * ```typescript
     * new Date(2023, 0, 15).isAfter(new Date(2023, 0, 14)); // Returns true
     * new Date(2023, 0, 15).isAfter(new Date(2023, 0, 15)); // Returns false
     * new Date(2023, 0, 15).isAfter(new Date(2023, 0, 16)); // Returns false
     * ```
     */
    isAfter(date: Date): boolean;

    /**
     * Checks if the date is the same day as another date
     *
     * @param date The date to compare with
     * @returns True if this date is the same day as the specified date, false otherwise
     * @category Comparison
     * @example
     * ```typescript
     * new Date(2023, 0, 15, 9).isSameDay(new Date(2023, 0, 15, 14)); // Returns true
     * new Date(2023, 0, 15).isSameDay(new Date(2023, 0, 16)); // Returns false
     * ```
     */
    isSameDay(date: Date): boolean;

    /**
     * Gets the name of the day of the week
     *
     * @param locale The locale to use (default: system locale)
     * @param format The format of the day name ('long', 'short', or 'narrow')
     * @returns The name of the day of the week
     * @category Utility
     * @example
     * ```typescript
     * new Date(2023, 0, 15).getDayName(); // Returns "Sunday"
     * new Date(2023, 0, 15).getDayName('en-US', 'short'); // Returns "Sun"
     * new Date(2023, 0, 15).getDayName('es-ES', 'long'); // Returns "domingo"
     * ```
     */
    getDayName(locale?: string, format?: 'long' | 'short' | 'narrow'): string;

    /**
     * Gets the name of the month
     *
     * @param locale The locale to use (default: system locale)
     * @param format The format of the month name ('long', 'short', or 'narrow')
     * @returns The name of the month
     * @category Utility
     * @example
     * ```typescript
     * new Date(2023, 0, 15).getMonthName(); // Returns "January"
     * new Date(2023, 0, 15).getMonthName('en-US', 'short'); // Returns "Jan"
     * new Date(2023, 0, 15).getMonthName('es-ES', 'long'); // Returns "enero"
     * ```
     */
    getMonthName(locale?: string, format?: 'long' | 'short' | 'narrow'): string;

    /**
     * Gets the quarter of the year (1-4)
     *
     * @returns The quarter of the year (1-4)
     * @category Utility
     * @example
     * ```typescript
     * new Date(2023, 0, 15).getQuarter(); // Returns 1 (January is in Q1)
     * new Date(2023, 6, 15).getQuarter(); // Returns 3 (July is in Q3)
     * ```
     */
    getQuarter(): number;

    /**
     * Checks if the date is valid
     *
     * @returns True if the date is valid, false otherwise
     * @category Validation
     * @example
     * ```typescript
     * new Date(2023, 0, 15).isValid(); // Returns true
     * new Date('invalid date').isValid(); // Returns false
     * ```
     */
    isValid(): boolean;

    /**
     * Checks if the date is in the past
     *
     * @returns True if the date is in the past, false otherwise
     * @category Validation
     * @example
     * ```typescript
     * new Date(2020, 0, 1).isPast(); // Returns true (assuming current date is after 2020)
     * new Date(2030, 0, 1).isPast(); // Returns false (assuming current date is before 2030)
     * ```
     */
    isPast(): boolean;

    /**
     * Checks if the date is in the future
     *
     * @returns True if the date is in the future, false otherwise
     * @category Validation
     * @example
     * ```typescript
     * new Date(2030, 0, 1).isFuture(); // Returns true (assuming current date is before 2030)
     * new Date(2020, 0, 1).isFuture(); // Returns false (assuming current date is after 2020)
     * ```
     */
    isFuture(): boolean;

    /**
     * Checks if the date is between two other dates
     *
     * @param startDate The start date of the range
     * @param endDate The end date of the range
     * @param inclusive Whether to include the start and end dates in the comparison (default: true)
     * @returns True if the date is between the start and end dates, false otherwise
     * @category Comparison
     * @example
     * ```typescript
     * const date = new Date(2023, 0, 15);
     * date.isBetween(new Date(2023, 0, 1), new Date(2023, 0, 31)); // Returns true
     * date.isBetween(new Date(2023, 0, 15), new Date(2023, 0, 31), true); // Returns true
     * date.isBetween(new Date(2023, 0, 15), new Date(2023, 0, 31), false); // Returns false
     * ```
     */
    isBetween(startDate: Date, endDate: Date, inclusive?: boolean): boolean;
  }
}

// ===================================================
// Implementation of Date prototype extension methods
// ===================================================

/**
 * Format date according to the specified format string
 */
Date.prototype.toFormattedString = function(format: string, locale = 'en-US'): string {
  const date = this as Date;

  // Replace format tokens with actual values
  return format
    .replace(/yyyy/g, date.getFullYear().toString())
    .replace(/yy/g, date.getFullYear().toString().slice(-2))
    .replace(/MMMM/g, date.getMonthName(locale, 'long'))
    .replace(/MMM/g, date.getMonthName(locale, 'short'))
    .replace(/MM/g, (date.getMonth() + 1).toString().padStart(2, '0'))
    .replace(/M/g, (date.getMonth() + 1).toString())
    .replace(/dddd/g, date.getDayName(locale, 'long'))
    .replace(/ddd/g, date.getDayName(locale, 'short'))
    .replace(/dd/g, date.getDate().toString().padStart(2, '0'))
    .replace(/d/g, date.getDate().toString())
    .replace(/HH/g, date.getHours().toString().padStart(2, '0'))
    .replace(/H/g, date.getHours().toString())
    .replace(/hh/g, (date.getHours() % 12 || 12).toString().padStart(2, '0'))
    .replace(/h/g, (date.getHours() % 12 || 12).toString())
    .replace(/mm/g, date.getMinutes().toString().padStart(2, '0'))
    .replace(/m/g, date.getMinutes().toString())
    .replace(/ss/g, date.getSeconds().toString().padStart(2, '0'))
    .replace(/s/g, date.getSeconds().toString())
    .replace(/a/g, date.getHours() < 12 ? 'am' : 'pm')
    .replace(/A/g, date.getHours() < 12 ? 'AM' : 'PM');
};

/**
 * Check if date is a weekend
 */
Date.prototype.isWeekend = function(): boolean {
  const day = (this as Date).getDay();
  return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
};

/**
 * Check if date is a weekday
 */
Date.prototype.isWeekday = function(): boolean {
  return !(this as Date).isWeekend();
};

/**
 * Add days to date
 */
Date.prototype.addDays = function(days: number): Date {
  const date = new Date(this.valueOf());
  date.setDate(date.getDate() + days);
  return date;
};

/**
 * Add months to date
 */
Date.prototype.addMonths = function(months: number): Date {
  const date = new Date(this.valueOf());
  date.setMonth(date.getMonth() + months);
  return date;
};

/**
 * Add years to date
 */
Date.prototype.addYears = function(years: number): Date {
  const date = new Date(this.valueOf());
  date.setFullYear(date.getFullYear() + years);
  return date;
};

/**
 * Check if date is before another date
 */
Date.prototype.isBefore = function(date: Date): boolean {
  return (this as Date).getTime() < date.getTime();
};

/**
 * Check if date is after another date
 */
Date.prototype.isAfter = function(date: Date): boolean {
  return (this as Date).getTime() > date.getTime();
};

/**
 * Check if date is the same day as another date
 */
Date.prototype.isSameDay = function(date: Date): boolean {
  return (
    (this as Date).getFullYear() === date.getFullYear() &&
    (this as Date).getMonth() === date.getMonth() &&
    (this as Date).getDate() === date.getDate()
  );
};

/**
 * Get day name
 */
Date.prototype.getDayName = function(locale = 'en-US', format: 'long' | 'short' | 'narrow' = 'long'): string {
  return new Intl.DateTimeFormat(locale, { weekday: format }).format(this);
};

/**
 * Get month name
 */
Date.prototype.getMonthName = function(locale = 'en-US', format: 'long' | 'short' | 'narrow' = 'long'): string {
  return new Intl.DateTimeFormat(locale, { month: format }).format(this);
};

/**
 * Get quarter of the year
 */
Date.prototype.getQuarter = function(): number {
  return Math.floor((this as Date).getMonth() / 3) + 1;
};

/**
 * Check if date is valid
 */
Date.prototype.isValid = function(): boolean {
  return !isNaN((this as Date).getTime());
};

/**
 * Check if date is in the past
 */
Date.prototype.isPast = function(): boolean {
  return (this as Date).getTime() < new Date().getTime();
};

/**
 * Check if date is in the future
 */
Date.prototype.isFuture = function(): boolean {
  return (this as Date).getTime() > new Date().getTime();
};

/**
 * Check if date is between two dates
 */
Date.prototype.isBetween = function(startDate: Date, endDate: Date, inclusive = true): boolean {
  const time = (this as Date).getTime();

  if (inclusive) {
    return time >= startDate.getTime() && time <= endDate.getTime();
  } else {
    return time > startDate.getTime() && time < endDate.getTime();
  }
};

/**
 * Export an empty object to make this a module
 * This is required for TypeScript to recognize this file as a module
 * rather than a script file.
 */
export {};
