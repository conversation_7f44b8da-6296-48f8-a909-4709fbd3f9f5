import { UserRepository } from './user.repository';
import { RoleRepository } from './role.repository';
import { UserRoleRepository } from './user-role.repository';
import { Service, Inject } from '@c-visitor/framework';
import { IUser } from '@c-visitor/types';
import User, { UserDocument } from '../models/entities/User';
import { RoleDocument } from '../models/entities/Role';

@Service()
export class IdentityRepository {
  constructor(
    @Inject(UserRepository) private readonly userRepository: UserRepository,
    @Inject(RoleRepository) private readonly roleRepository: RoleRepository,
    @Inject(UserRoleRepository) private readonly userRoleRepository: UserRoleRepository,
  ) {}

  // User methods
  async findUserById(id: string): Promise<UserDocument | null> {
    return this.userRepository.findById(id);
  }

  async findUserByEmail(email: string): Promise<UserDocument | null> {
    return this.userRepository.findByEmail(email);
  }

  async findUserByEmailWithPassword(
    email: string,
  ): Promise<UserDocument | null> {
    const user = await this.userRepository.findByEmail(email);
    return user;
  }

  async findUserByVerificationToken(
    token: string,
  ): Promise<UserDocument | null> {
    // Use the User model directly since UserRepository might not have this method
    return User.findOne({ verificationToken: token }).exec();
  }

  async findUserByResetToken(token: string): Promise<UserDocument | null> {
    // Use the User model directly since UserRepository might not have this method
    return User.findOne({ resetPasswordToken: token }).exec();
  }

  async findUserByRefreshToken(token: string): Promise<UserDocument | null> {
    // Use the User model directly since UserRepository might not have this method
    return User.findOne({ refreshToken: token }).exec();
  }

  async createUser(user: Partial<UserDocument>): Promise<UserDocument> {
    return this.userRepository.create(user);
  }

  async updateUser(
    id: string,
    updates: Partial<UserDocument>,
  ): Promise<UserDocument | null> {
    return this.userRepository.update(id, updates);
  }

  // Role methods
  async findRoleById(id: string): Promise<RoleDocument | null> {
    return this.roleRepository.findById(id);
  }

  async findRoleByName(name: string): Promise<RoleDocument | null> {
    return this.roleRepository.findByName(name);
  }

  async findAllRoles(): Promise<RoleDocument[]> {
    return this.roleRepository.findAll();
  }

  async createRole(role: Partial<RoleDocument>): Promise<RoleDocument> {
    return this.roleRepository.create(role);
  }

  async getUserWithRoles(userId: string): Promise<IUser | undefined> {
    const user = await this.userRepository.findById(userId);
    if (!user) return undefined;

    // Fetch user roles from UserRole collection
    const userRoles = await this.userRoleRepository.findByUserId(userId);

    // Fetch role details for each user role
    const roleIds = userRoles.map(userRole => userRole.roleId);
    const roles = await Promise.all(
      roleIds.map(roleId => this.roleRepository.findById(roleId))
    );

    // Filter out any null roles
    const validRoles = roles.filter((role): role is RoleDocument => role !== null);

    return {
      id: user._id ? user._id.toString() : '',
      email: user.email,
      fullName: user.fullName,
      emailVerified: user.emailVerified,
      active: user.active,
      password: user.password,
      roles: validRoles,
      // Include other required fields from IUser
    } as unknown as IUser;
  }
}
