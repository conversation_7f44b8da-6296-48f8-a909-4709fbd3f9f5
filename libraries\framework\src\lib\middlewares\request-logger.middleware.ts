import { Request, Response, NextFunction } from 'express';
import { IMiddleware } from '../decorators/middleware.decorator.js';
import { logger } from '../logger.js';

/**
 * Options for the request logger middleware
 */
export interface RequestLoggerOptions {
  /**
   * Whether to log the request body
   * Default: false
   */
  logRequestBody?: boolean;

  /**
   * Whether to log the response body
   * Default: false
   */
  logResponseBody?: boolean;

  /**
   * Whether to use color coding for status codes
   * Default: true
   */
  useColors?: boolean;

  /**
   * Custom logger instance
   * Default: global logger
   */
  logger?: typeof logger;
}

/**
 * Default options for the request logger middleware
 */
const defaultOptions: RequestLoggerOptions = {
  logRequestBody: false,
  logResponseBody: false,
  useColors: true,
};

/**
 * Request logger middleware implementation
 * Logs request and response details with timing information
 */
export class RequestLoggerMiddleware implements IMiddleware {
  private readonly options: RequestLoggerOptions;
  private readonly loggerInstance: typeof logger;

  constructor(options: RequestLoggerOptions = {}) {
    this.options = { ...defaultOptions, ...options };
    this.loggerInstance = this.options.logger || logger;
  }

  /**
   * Handle request logging
   */
  public use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();

    // Log request start
    this.loggerInstance.info(`[${req.method}] Request started: ${req.url}`);

    // Log request body if enabled
    if (this.options.logRequestBody && req.body) {
      this.loggerInstance.debug(`Request body: ${JSON.stringify(req.body)}`);
    }

    // Capture original end method to intercept response
    const originalEnd = res.end;
    const responseBody: Buffer[] = [];

    // Override end method to capture response data
    res.end = ((
      chunk?: any,
      encoding?: BufferEncoding,
      callback?: () => void,
    ) => {
      // Restore original end method
      res.end = originalEnd;

      // Capture response body if enabled
      if (this.options.logResponseBody && chunk) {
        responseBody.push(Buffer.from(chunk));
      }

      // Calculate elapsed time
      const elapsedTime = Date.now() - startTime;

      // Get status code and determine color
      const statusCode = res.statusCode;
      let statusColor = '';
      let resetColor = '';

      if (this.options.useColors) {
        statusColor =
          statusCode >= 500
            ? '\x1b[31m' // Red for 5xx errors
            : statusCode >= 400
              ? '\x1b[33m' // Yellow for 4xx errors
              : statusCode >= 300
                ? '\x1b[36m' // Cyan for 3xx redirects
                : '\x1b[32m'; // Green for 2xx success
        resetColor = '\x1b[0m';
      }

      // Log response completion
      this.loggerInstance.info(
        `[${req.method}] ${statusColor}${statusCode}${resetColor} ${req.url} - ${elapsedTime}ms`,
      );

      // Log response body if enabled
      if (this.options.logResponseBody && responseBody.length > 0) {
        try {
          const body = Buffer.concat(responseBody).toString('utf8');
          this.loggerInstance.debug(`Response body: ${body}`);
        } catch (error) {
          this.loggerInstance.debug('Could not log response body');
        }
      }

      // Call original end method with proper arguments
      try {
        if (encoding) {
          return originalEnd.call(res, chunk, encoding, callback);
        } else {
          return originalEnd.call(res, chunk, 'utf8', callback);
        }
      } catch (error) {
        this.loggerInstance.error('Error in response end method:', error);
        return false;
      }
    }) as any;

    next();
  }
}

/**
 * Create request logger middleware with the specified options
 * @param options Request logger options
 * @returns Express middleware function
 */
export function createRequestLoggerMiddleware(
  options?: RequestLoggerOptions,
): (req: Request, res: Response, next: NextFunction) => void {
  const middleware = new RequestLoggerMiddleware(options);
  return (req: Request, res: Response, next: NextFunction) =>
    middleware.use(req, res, next);
}
