import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { DateTracking } from '@c-visitor/types';

// Role interface
export interface IRole extends DateTracking {
  id: string;
  name: string;
  active: boolean;
}

// Role interface for Mongoose document
export interface RoleDocument extends Omit<IRole, 'id'>, Document {}

// Role schema for Mongoose
const RoleSchema = new Schema<RoleDocument>(
  {
    name: { type: String, required: true, unique: true, maxlength: 50 },
    active: { type: Boolean, required: true, default: true },
  },
  { timestamps: true, collection: 'roles' },
);

// Create indexes for frequently queried fields
RoleSchema.index({ name: 1 });

// Create the Role model
const RoleModel = mongoose.model<RoleDocument>('Role', RoleSchema);

export default RoleModel;
