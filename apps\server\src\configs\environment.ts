import dotenv from 'dotenv';
import path from 'path';
import { logger } from '@c-visitor/framework';

// Load .env file from server directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

/**
 * Environment types
 */
type NodeEnv = 'development' | 'production' | 'test';

/**
 * Get environment variable with prefix based on NODE_ENV
 * Automatically tries {NODE_ENV}_{KEY} first, then falls back to {KEY}
 */
function getEnv(key: string, defaultValue?: any): any {
  const nodeEnv = process.env.NODE_ENV || 'development';
  const prefixedKey = `${nodeEnv.toUpperCase()}_${key}`;

  // Try prefixed key first, then fallback to base key, then default
  const value = process.env[prefixedKey] || process.env[key] || defaultValue;

  // Convert string values to appropriate types
  if (typeof defaultValue === 'number') return Number(value);
  if (typeof defaultValue === 'boolean') return value === 'true';

  return value;
}

/**
 * Environment configuration object
 * Automatically switches between DEVELOPMENT_* and PRODUCTION_* based on NODE_ENV
 */
export const environment = {
  // Node environment
  NODE_ENV: (process.env.NODE_ENV || 'development') as NodeEnv,

  // Server configuration
  PORT: getEnv('PORT', 3000),
  HOST: getEnv('HOST', 'localhost'),
  API_PREFIX: process.env.API_PREFIX || '/api',

  // Database configuration
  DB_CONNECTION: getEnv('DB_CONNECTION', ''),

  // JWT configuration
  JWT_SECRET: getEnv('JWT_SECRET', 'c-visitor-secret-key'),
  JWT_EXPIRATION: getEnv('JWT_EXPIRATION', 86400),

  // Logging
  LOG_LEVEL: getEnv('LOG_LEVEL', 'info'),

  // CORS configuration
  CORS_ALLOWED_ORIGINS: getEnv('CORS_ALLOWED_ORIGINS', '*'),

  // Request size limit
  MAX_REQUEST_SIZE: getEnv('MAX_REQUEST_SIZE', '50mb'),

  // Minio configuration
  MINIO_ENDPOINT: getEnv('MINIO_ENDPOINT', ''),
  MINIO_PORT: getEnv('MINIO_PORT', 9000),
  MINIO_USE_SSL: getEnv('MINIO_USE_SSL', false),
  MINIO_ACCESS_KEY: getEnv('MINIO_ACCESS_KEY', ''),
  MINIO_SECRET_KEY: getEnv('MINIO_SECRET_KEY', ''),
  MINIO_PUBLIC_URL: getEnv('MINIO_PUBLIC_URL', ''),

  // Civams Database
  CIVAMS_DB_HOST: getEnv('CIVAMS_DB_HOST', ''),
  CIVAMS_DB_PORT: getEnv('CIVAMS_DB_PORT', 5432),
  CIVAMS_DB_NAME: getEnv('CIVAMS_DB_NAME', ''),
  CIVAMS_DB_USER: getEnv('CIVAMS_DB_USER', ''),
  CIVAMS_DB_PASSWORD: getEnv('CIVAMS_DB_PASSWORD', ''),

  // MQTT
  MQTT_HOST: getEnv('MQTT_HOST', ''),
  MQTT_PORT: getEnv('MQTT_PORT', 1883),
  MQTT_PROTOCOL: getEnv('MQTT_PROTOCOL', 'mqtt'),
  MQTT_USERNAME: getEnv('MQTT_USERNAME', ''),
  MQTT_PASSWORD: getEnv('MQTT_PASSWORD', ''),
  MQTT_REJECT_UNAUTHORIZED: getEnv('MQTT_REJECT_UNAUTHORIZED', false),

  // Redis configuration
  REDIS_HOST: getEnv('REDIS_HOST', 'localhost'),
  REDIS_PORT: getEnv('REDIS_PORT', 6379),
  REDIS_PASSWORD: getEnv('REDIS_PASSWORD', ''),
  REDIS_DB: getEnv('REDIS_DB', 0),
  REDIS_KEY_PREFIX: getEnv('REDIS_KEY_PREFIX', 'cvisitor:'),

  // SMTP Mail
  SMTP_MAIL_HOST: getEnv('SMTP_MAIL_HOST', ''),
  SMTP_MAIL_PORT: getEnv('SMTP_MAIL_PORT', 587),
  SMTP_MAIL_SECURE: getEnv('SMTP_MAIL_SECURE', false),
  SMTP_MAIL_AUTH_USER: getEnv('SMTP_MAIL_AUTH_USER', ''),
  SMTP_MAIL_AUTH_PASS: getEnv('SMTP_MAIL_AUTH_PASS', ''),
  SMTP_MAIL_FROM: getEnv('SMTP_MAIL_FROM', ''),
};

/**
 * Validate environment configuration
 */
export function validateEnvironment(): void {
  if (environment.NODE_ENV === 'production') {
    if (environment.JWT_SECRET.includes('CHANGE_THIS')) {
      logger.warn('⚠️  Please change JWT_SECRET in production!');
    }
    if (environment.DB_CONNECTION.includes('CHANGE_THIS')) {
      logger.warn('⚠️  Please change DB_CONNECTION in production!');
    }
  }

  logger.info(`🚀 Environment loaded: ${environment.NODE_ENV}`);
  logger.info(`📡 Server will run on: ${environment.HOST}:${environment.PORT}`);
}
