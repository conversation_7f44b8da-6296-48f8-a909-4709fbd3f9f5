import React from 'react';
import { cn } from '@/lib/utils';

interface FinanceIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const FinanceIcon: React.FC<FinanceIconProps> = ({ 
  className, 
  color = "#141B34", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M12 10.5C12 11.1213 12.5037 11.625 13.125 11.625C13.7463 11.625 14.25 11.1213 14.25 10.5C14.25 9.87868 13.7463 9.375 13.125 9.375C12.5037 9.375 12 9.87868 12 10.5Z"
        stroke={color}
        strokeWidth="1.5"
      />
      <path
        d="M7.5 5.25H12C14.1213 5.25 15.182 5.25 15.841 5.90901C16.5 6.56802 16.5 7.62868 16.5 9.75V11.25C16.5 13.3713 16.5 14.432 15.841 15.091C15.182 15.75 14.1213 15.75 12 15.75H7.5C4.67157 15.75 3.25736 15.75 2.37868 14.8713C1.5 13.9926 1.5 12.5784 1.5 9.75V8.25C1.5 5.42157 1.5 4.00736 2.37868 3.12868C3.25736 2.25 4.67157 2.25 7.5 2.25H10.5C11.1975 2.25 11.5462 2.25 11.8323 2.32667C12.6088 2.53472 13.2153 3.1412 13.4233 3.91766C13.5 4.20378 13.5 4.55252 13.5 5.25"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default FinanceIcon;
