import Role, { RoleDocument } from '@/models/entities/Role';
import { Service } from '@c-visitor/framework';

/**
 * Repository for Role entity
 */
@Service()
export class RoleRepository {
  /**
   * Find a role by ID
   * @param id Role ID
   * @returns Role or null if not found
   */
  async findById(id: string): Promise<RoleDocument | null> {
    return Role.findById(id).exec();
  }

  /**
   * Find a role by name
   * @param name Role name
   * @returns Role or null if not found
   */
  async findByName(name: string): Promise<RoleDocument | null> {
    return Role.findOne({ name }).exec();
  }

  /**
   * Find all roles
   * @param options Query options
   * @returns Array of roles
   */
  async findAll(options: {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
    active?: boolean;
  } = {}): Promise<RoleDocument[]> {
    const { limit, skip, sort, active } = options;
    let query = Role.find();

    if (active !== undefined) {
      query = query.find({ active });
    }

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new role
   * @param roleData Role data
   * @returns Created role
   */
  async create(roleData: Partial<RoleDocument>): Promise<RoleDocument> {
    const role = new Role(roleData);
    return role.save();
  }

  /**
   * Update a role
   * @param id Role ID
   * @param roleData Role data to update
   * @returns Updated role or null if not found
   */
  async update(
    id: string,
    roleData: Partial<RoleDocument>,
  ): Promise<RoleDocument | null> {
    return Role.findByIdAndUpdate(id, roleData, { new: true }).exec();
  }

  /**
   * Delete a role
   * @param id Role ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await Role.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }
}
