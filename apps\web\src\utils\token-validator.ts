/**
 * Utility function to validate token before route loading
 * Returns true if token is valid, false if expired/invalid
 */
export const validateTokenSync = (): boolean => {
  // Skip if already logging out
  if (isLoggingOut) {
    return false;
  }

  const token = localStorage.getItem('auth_token');
  if (!token) {
    return false;
  }

  try {
    // Decode JWT token to check expiry (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired
    if (payload.exp && payload.exp < currentTime) {
      return false; // Don't clear tokens here, let forceLogout handle it
    }

    return true;
  } catch (error) {
    console.error('Error validating token:', error);
    return false; // Don't clear tokens here, let forceLogout handle it
  }
};

/**
 * Async token validation with server verification
 */
export const validateTokenAsync = async (): Promise<boolean> => {
  // Skip if already logging out
  if (isLoggingOut) {
    return false;
  }

  const token = localStorage.getItem('auth_token');
  if (!token) {
    return false;
  }

  try {
    const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000'}/api/request`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
    });

    if (response.status === 401) {
      console.log('Token invalid (server check)');
      return false; // Don't clear tokens here, let forceLogout handle it
    }

    return response.ok;
  } catch (error) {
    console.error('Token validation failed:', error);
    return false; // Assume invalid on network error
  }
};

/**
 * Check if token will expire soon (within specified minutes)
 */
export const isTokenExpiringSoon = (token: string, minutesThreshold = 5): boolean => {
  if (!token) {
    return false;
  }

  try {
    // Decode JWT token to check expiry (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = payload.exp - currentTime;

    // Check if token expires within the threshold
    return timeUntilExpiry < (minutesThreshold * 60);
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return false;
  }
};

/**
 * Check if token is valid (not expired)
 */
export const isTokenValid = (token: string): boolean => {
  if (!token) {
    return false;
  }

  try {
    // Decode JWT token to check expiry (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired
    if (payload.exp && payload.exp < currentTime) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};

// Global flag to prevent multiple logout attempts
let isLoggingOut = false;

/**
 * Force logout and redirect to login (with duplicate prevention)
 */
export const forceLogout = (message?: string) => {
  // Prevent multiple logout attempts
  if (isLoggingOut) {
    return;
  }

  isLoggingOut = true;

  localStorage.removeItem('auth_token');
  localStorage.removeItem('refresh_token');

  if (message && typeof window !== 'undefined') {
    alert(message);
  }

  window.location.href = '/auth/sign-in';
};
