import { ServiceResponse } from '@c-visitor/framework';

export class BaseService {
  success<T>(data: T, message?: string): ServiceResponse<T> {
    return {
      success: true,
      data,
      message,
    };
  }

  error<T>(message: string, error?: any): ServiceResponse<T> {
    console.error(message, error);
    return {
      success: false,
      message,
      data: null as any,
    };
  }
}
