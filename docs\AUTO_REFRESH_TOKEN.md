# Auto Refresh Token Implementation

## 🔄 Tổng quan

Hệ thống đã được cập nhật để tự động refresh access token khi hết hạn, sử dụng refresh token để duy trì phiên đăng nhập của người dùng.

## ⚙️ Cấu hình Token

### Backend (Server)
- **Access Token**: Thời gian hết hạn được cấu hình trong `.env`
  - Development: `DEVELOPMENT_JWT_EXPIRATION=86400` (24 giờ)
  - Production: `PRODUCTION_JWT_EXPIRATION=3600` (1 giờ)
- **Refresh Token**: `7d` (7 ngày) - hardcoded trong service

### Frontend (Web)
- **Auto-refresh threshold**: 5 phút trước khi access token hết hạn
- **Storage**: localStorage (`auth_token`, `refresh_token`)

## 🔧 Cách hoạt động

### 1. Axios Interceptor Auto-Refresh
```typescript
// apps/web/src/configs/axios.ts
// Khi nhận 401 response:
// 1. <PERSON><PERSON><PERSON> tra có refresh token không
// 2. Gọi API refresh token
// 3. Cập nhật token mới vào localStorage
// 4. Retry request gốc với token mới
// 5. Nếu refresh thất bại → logout user
```

### 2. Manual Refresh Check
```typescript
// apps/web/src/hooks/use-auth.ts
const { refreshTokenIfNeeded } = useAuth();

// Kiểm tra và refresh nếu token sắp hết hạn (< 5 phút)
const refreshed = await refreshTokenIfNeeded();
```

### 3. Token Validation Hook
```typescript
// apps/web/src/hooks/use-token-validation.ts
// Tự động kiểm tra và refresh token khi app khởi động
useTokenValidation();
```

## 📋 API Endpoints

### Refresh Token
```http
POST /api/identity/refresh-token
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "new-access-token",
    "refreshToken": "new-refresh-token"
  },
  "message": "Token refreshed successfully"
}
```

## 🧪 Testing

### Console Testing
```javascript
// Mở Developer Console và chạy:
testAutoRefresh();        // Test logic kiểm tra token expiry
simulateTokenRefresh();   // Simulate refresh scenario
```

### Manual Testing
1. Đăng nhập vào ứng dụng
2. Mở Developer Tools → Application → Local Storage
3. Thay đổi `auth_token` thành token sắp hết hạn
4. Thực hiện API call → Token sẽ được tự động refresh

## 🔒 Security Features

### 1. Refresh Token Rotation
- Mỗi lần refresh, cả access token và refresh token đều được tạo mới
- Refresh token cũ bị vô hiệu hóa

### 2. Database Validation
- Refresh token được lưu trong database
- Chỉ refresh token hợp lệ mới có thể tạo token mới

### 3. Token Type Validation
- Access token có `type: 'access'`
- Refresh token có `type: 'refresh'`
- Không thể dùng nhầm loại token

## 🚨 Error Handling

### 1. Network Errors
- Axios interceptor retry logic
- Không logout user khi có lỗi mạng

### 2. Invalid Refresh Token
- Tự động logout user
- Clear tất cả tokens khỏi localStorage
- Redirect về trang login

### 3. Server Errors
- Graceful fallback
- User-friendly error messages

## 📊 Monitoring

### Console Logs
```javascript
// Success
console.log('Token refreshed successfully');

// Failure
console.error('Token refresh failed:', error);

// Validation
console.log('Token validation failed with 401');
```

### Token Expiry Check
```javascript
import { isTokenExpiringSoon, isTokenValid } from '@/utils/token-validator';

const token = localStorage.getItem('auth_token');
console.log('Token valid:', isTokenValid(token));
console.log('Token expiring soon:', isTokenExpiringSoon(token, 5));
```

## 🔄 Flow Diagram

```
User Request → Axios Interceptor → Check Response
                                      ↓
                                   401 Error?
                                      ↓
                                 Try Refresh Token
                                      ↓
                              Refresh Success?
                                   ↓     ↓
                              Yes: Retry   No: Logout
                              Request      User
```

## ⚡ Performance

### 1. Lazy Refresh
- Chỉ refresh khi cần thiết (token sắp hết hạn)
- Không refresh định kỳ

### 2. Single Request
- Prevent multiple refresh requests đồng thời
- Sử dụng `_retry` flag trong axios interceptor

### 3. Efficient Storage
- localStorage cho token persistence
- Minimal memory footprint

## 🔧 Configuration

### Thay đổi thời gian hết hạn
```bash
# Development
DEVELOPMENT_JWT_EXPIRATION=86400  # 24 hours

# Production  
PRODUCTION_JWT_EXPIRATION=3600   # 1 hour
```

### Thay đổi refresh threshold
```typescript
// apps/web/src/hooks/use-auth.ts
// Thay đổi từ 5 phút thành 10 phút
if (isTokenExpiringSoon(token, 10)) {
  // refresh logic
}
```

## 📝 Notes

1. **Restart không cần thiết**: Thay đổi JWT expiration không cần restart app
2. **Backward Compatible**: Hệ thống vẫn hoạt động với token cũ
3. **Production Ready**: Đã test với cả development và production config
4. **User Experience**: Seamless - user không bị logout bất ngờ
