import {
  Controller,
  ControllerBase,
  HttpGet,
  HttpPost,
  Authorized,
  HttpContext,
  type IHttpContext,
} from '@c-visitor/framework';

/**
 * Example controller demonstrating the use of the @Authorized decorator
 */
@Controller('/api/secure')
export class SecureController extends ControllerBase {
  /**
   * Public endpoint that doesn't require authentication
   */
  @HttpGet('/public')
  async getPublicData(@HttpContext() context: IHttpContext) {
    return this.success(context.response, {
      message: 'This is public data that anyone can access',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Protected endpoint that requires authentication
   */
  @HttpGet('/protected')
  @Authorized()
  async getProtectedData(@HttpContext() context: IHttpContext) {
    const user = this.getCurrentUser(context.request);
    return this.success(context.response, {
      message: 'This is protected data that requires authentication',
      user: {
        id: user?.id,
        email: user?.email,
        name: user?.name,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Admin-only endpoint that requires specific role
   */
  @HttpGet('/admin')
  @Authorized({ roles: ['Admin'] })
  async getAdminData(@HttpContext() context: IHttpContext) {
    const user = this.getCurrentUser(context.request);
    return this.success(context.response, {
      message: 'This is admin-only data',
      user: {
        id: user?.id,
        email: user?.email,
        name: user?.name,
        roles: user?.roles,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Staff or Admin endpoint that requires specific roles
   */
  @HttpGet('/staff')
  @Authorized({ roles: ['Staff', 'Admin'] })
  async getStaffData(@HttpContext() context: IHttpContext) {
    const user = this.getCurrentUser(context.request);
    return this.success(context.response, {
      message: 'This is staff data accessible by Staff and Admin roles',
      user: {
        id: user?.id,
        email: user?.email,
        name: user?.name,
        roles: user?.roles,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * User profile endpoint - requires authentication but any role
   */
  @HttpGet('/profile')
  @Authorized()
  async getUserProfile(@HttpContext() context: IHttpContext) {
    const user = this.getCurrentUser(context.request);
    
    this.unauthorizedIf(!user, 'User not found');

    return this.success(context.response, {
      profile: {
        id: user.id,
        email: user.email,
        name: user.name,
        roles: user.roles,
        lastLogin: new Date().toISOString(),
      },
    });
  }

  /**
   * Update user preferences - requires authentication
   */
  @HttpPost('/preferences')
  @Authorized()
  async updatePreferences(@HttpContext() context: IHttpContext) {
    const user = this.getCurrentUser(context.request);
    const preferences = context.request.body;

    this.unauthorizedIf(!user, 'User not found');

    // Here you would typically save preferences to database
    // For demo purposes, we'll just return the received preferences

    return this.success(context.response, {
      message: 'Preferences updated successfully',
      userId: user.id,
      preferences,
      updatedAt: new Date().toISOString(),
    });
  }

  /**
   * System health check - admin only
   */
  @HttpGet('/health')
  @Authorized({ roles: ['Admin'] })
  async getSystemHealth(@HttpContext() context: IHttpContext) {
    return this.success(context.response, {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
    });
  }

  /**
   * Test endpoint to verify JWT claims
   */
  @HttpGet('/claims')
  @Authorized()
  async getClaims(@HttpContext() context: IHttpContext) {
    const principal = this.getClaimsPrincipal(context.request);
    const user = this.getCurrentUser(context.request);

    return this.success(context.response, {
      principal: principal ? {
        identity: principal.identity,
        roles: principal.roles,
      } : null,
      user,
      timestamp: new Date().toISOString(),
    });
  }
}
