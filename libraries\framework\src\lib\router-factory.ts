import {
  Request,
  Response,
  NextFunction,
  RequestHandler,
  Router,
} from 'express';
import { Container } from './container.js';
import {
  AUTHORIZED_METADATA,
  ERROR_HANDLER_METADATA,
  ErrorHandlerMetadata,
  IMiddleware,
  MIDDLEWARE_METADATA,
  ParamMetadata,
  PARAMS_METADATA,
  PATH_METADATA,
  RouteMetadata,
  ROUTES_METADATA,
} from './decorators.js';
import { asyncHandler } from './utilities/async-handler.js';
import { CreateAuthorizationMiddleware } from './authorization.js';
import { logger } from './logger.js';

export class RouterFactory {
  static createRoutes(register: () => void): Router {
    register();
    const router = Router();

    // Tìm tất cả các controller đã đăng ký
    const controllers = Container.filter((instance: any) =>
      Reflect.hasMetadata(PATH_METADATA, instance.constructor)
    );

    // <PERSON>ử lý từng controller
    controllers.forEach((controller) => {
      const controllerInstance = controller;
      const controllerClass = controller.constructor;

      // Lấy base path từ controller
      const basePath =
        Reflect.getMetadata(PATH_METADATA, controllerClass) || '';

      // Lấy tất cả các route từ controller
      const routes: RouteMetadata[] =
        Reflect.getMetadata(ROUTES_METADATA, controllerClass) || [];

      // Lấy middleware của controller
      const controllerMiddlewares =
        Reflect.getMetadata(MIDDLEWARE_METADATA, controllerClass) || [];

      // Lấy authorization metadata của controller
      const controllerAuthOptions = Reflect.getMetadata(
        AUTHORIZED_METADATA,
        controllerClass
      );

      // Nếu controller có @Authorized decorator, thêm authorization middleware
      if (controllerAuthOptions) {
        const authMiddleware = CreateAuthorizationMiddleware(
          controllerAuthOptions
        );
        controllerMiddlewares.push(authMiddleware);
      }

      // Lấy error handlers từ cả controller hiện tại và các lớp cơ sở
      const errorHandlers = getAllErrorHandlers(controllerClass);

      // Đăng ký từng route
      routes.forEach((route) => {
        const { path, method, handlerName } = route;
        const fullPath = `${basePath}${path}`;

        // Lấy middleware của method
        const methodMiddlewares =
          Reflect.getMetadata(
            MIDDLEWARE_METADATA,
            controllerInstance,
            handlerName
          ) || [];

        // Lấy authorization metadata của method
        const methodAuthOptions = Reflect.getMetadata(
          AUTHORIZED_METADATA,
          controllerInstance,
          handlerName
        );

        // Nếu method có @Authorized decorator, thêm authorization middleware
        if (methodAuthOptions) {
          const authMiddleware =
            CreateAuthorizationMiddleware(methodAuthOptions);
          methodMiddlewares.push(authMiddleware);
        }

        // Lấy parameter metadata
        const parameters: ParamMetadata[] =
          Reflect.getMetadata(
            PARAMS_METADATA,
            controllerInstance,
            handlerName
          ) || [];

        // Chuyển đổi middleware từ class sang function nếu cần
        const processedControllerMiddlewares = controllerMiddlewares.map(
          (middleware: IMiddleware | Function) => {
            if (typeof middleware === 'function') {
              return asyncHandler(middleware as RequestHandler);
            }
            return asyncHandler(
              (req: Request, res: Response, next: NextFunction) =>
                middleware.use(req, res, next)
            );
          }
        );

        const processedMethodMiddlewares = methodMiddlewares.map(
          (middleware: IMiddleware | Function) => {
            if (typeof middleware === 'function') {
              return asyncHandler(middleware as RequestHandler);
            }
            return asyncHandler(
              (req: Request, res: Response, next: NextFunction) =>
                middleware.use(req, res, next)
            );
          }
        );

        // Đăng ký route với Express, bọc handler bằng asyncHandler
        router[method](
          fullPath,
          [...processedControllerMiddlewares, ...processedMethodMiddlewares],
          (req: Request, res: Response, next: NextFunction) => {
            // Gắn controller và error handlers vào request để sử dụng trong error handling
            (req as any).controller = controllerInstance;
            (req as any).errorHandlers = errorHandlers;
            next();
          },
          asyncHandler((req: Request, res: Response, next: NextFunction) => {
            // Xử lý parameter decorators
            const args: any[] = [];
            const handler = controllerInstance[handlerName as string];

            // Nếu có parameter decorators
            if (parameters.length > 0) {
              // Sắp xếp parameters theo index
              const sortedParams = [...parameters].sort(
                (a, b) => a.index - b.index
              );

              // Tạo mảng tham số dựa trên decorators
              for (const param of sortedParams) {
                switch (param.type) {
                  case 'http_context':
                    args[param.index] = {
                      request: req,
                      response: res,
                      next,
                    };
                    break;
                  case 'body':
                    args[param.index] = param.validator
                      ? param.validator(req.body)
                      : req.body;
                    break;
                  case 'param':
                    args[param.index] = param.validator
                      ? param.validator(
                          param.name ? req.params[param.name] : req.params
                        )
                      : param.name
                      ? req.params[param.name]
                      : req.params;
                    break;
                  case 'query':
                    args[param.index] = param.validator
                      ? param.validator(
                          param.name ? req.query[param.name] : req.query
                        )
                      : param.name
                      ? req.query[param.name]
                      : req.query;
                    break;
                  case 'headers':
                    args[param.index] = param.validator
                      ? param.validator(
                          param.name
                            ? req.headers[param.name.toLowerCase()]
                            : req.headers
                        )
                      : param.name
                      ? req.headers[param.name.toLowerCase()]
                      : req.headers;
                    break;
                  case 'req':
                    args[param.index] = req;
                    break;
                  case 'res':
                    args[param.index] = res;
                    break;
                  case 'next':
                    args[param.index] = next;
                    break;
                  default:
                    args[param.index] = undefined;
                }
              }

              return handler.apply(controllerInstance, args);
            } else {
              // Nếu không có parameter decorators, sử dụng cách truyền thông thường
              return handler.apply(controllerInstance, [req, res, next]);
            }
          })
        );

        logger.info(`Registered route: [${method.toUpperCase()}] ${fullPath}`);
      });
    });

    return router;
  }
}

/**
 * Lấy tất cả error handlers từ một class và các lớp cơ sở của nó
 */
function getAllErrorHandlers(targetClass: any): ErrorHandlerMetadata[] {
  const errorHandlers: ErrorHandlerMetadata[] = [];
  let currentClass = targetClass;

  // Duyệt qua chuỗi kế thừa
  while (currentClass && currentClass.prototype) {
    const handlers =
      Reflect.getMetadata(ERROR_HANDLER_METADATA, currentClass) || [];
    errorHandlers.push(...handlers);

    // Di chuyển lên lớp cơ sở
    currentClass = Object.getPrototypeOf(currentClass);
  }

  return errorHandlers;
}
