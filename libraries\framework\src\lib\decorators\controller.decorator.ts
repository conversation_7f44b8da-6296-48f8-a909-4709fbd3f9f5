import 'reflect-metadata';
import { Service } from './service.decorator.js';

// Metadata key
export const PATH_METADATA = 'path';

/**
 * Decorator that marks a class as a controller with a base path
 */
export function Controller(basePath = ''): ClassDecorator {
  return function (target: any) {
    Reflect.defineMetadata(PATH_METADATA, basePath, target);

    // Đảm bảo class được đăng ký với DI container
    Service()(target);

    return target;
  };
}
