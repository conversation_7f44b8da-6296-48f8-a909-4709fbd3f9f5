import winston, { format, transports, Logger } from 'winston';
import path from 'path';

// Tạo logger với cấu hình format và transport
export const logger: Logger = winston.createLogger({
  format: format.combine(
    format.splat(),
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.colorize(),
    format.printf((log: winston.Logform.TransformableInfo) => {
      if (log.stack) return `[${log.timestamp}] [${log.level}] ${log.stack}`;
      return `[${log.timestamp}] [${log.level}] ${log.message}`;
    })
  ),
  transports: [
    new transports.Console(),
    new transports.File({
      level: 'error',
      filename: path.join(__dirname, 'errors.log'),
    }),
  ],
});
