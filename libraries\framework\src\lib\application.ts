import express, { Express, Request, Response, NextFunction } from 'express';
import { IServiceProvider } from './type.js';
import { CorsOptions, createCorsMiddleware } from './middlewares/cors.middleware.js';
import { RequestLoggerOptions, createRequestLoggerMiddleware } from './middlewares/request-logger.middleware.js';

export interface IApplicationBuilder {
  /**
   * Use middleware
   */
  UseMiddleware(
    middleware: (req: Request, res: Response, next: NextFunction) => void
  ): IApplicationBuilder;

  /**
   * Configure CORS
   */
  UseCors(options?: CorsOptions): IApplicationBuilder;

  /**
   * Configure request logger
   */
  UseRequestLogger(options?: RequestLoggerOptions): IApplicationBuilder;

  /**
   * Build the application
   */
  build(): Express;
}

export class ApplicationBuilder implements IApplicationBuilder {
  private readonly _app: Express;
  private readonly _serviceProvider: IServiceProvider;

  constructor(serviceProvider: IServiceProvider) {
    this._app = express();
    this._serviceProvider = serviceProvider;

    // Add service provider to request
    this._app.use((req: Request, _res: Response, next: NextFunction) => {
      (req as any).serviceProvider = this._serviceProvider;
      next();
    });
  }

  /**
   * Use middleware
   */
  public UseMiddleware(
    middleware: (req: Request, res: Response, next: NextFunction) => void
  ): IApplicationBuilder {
    this._app.use(middleware);
    return this;
  }

  /**
   * Configure CORS
   */
  public UseCors(options?: CorsOptions): IApplicationBuilder {
    const corsMiddleware = createCorsMiddleware(options);
    this._app.use(corsMiddleware);
    return this;
  }

  /**
   * Configure request logger
   */
  public UseRequestLogger(options?: RequestLoggerOptions): IApplicationBuilder {
    const requestLoggerMiddleware = createRequestLoggerMiddleware(options);
    this._app.use(requestLoggerMiddleware);
    return this;
  }

  /**
   * Build the application
   */
  public build(): Express {
    return this._app;
  }
}
