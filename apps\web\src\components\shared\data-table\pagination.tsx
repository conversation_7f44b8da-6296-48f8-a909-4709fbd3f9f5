import { Table } from '@tanstack/react-table';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import './pagination.scss';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  sizeChanger?: boolean;
}

export function DataTablePagination<TData>({
  table,
  sizeChanger = false,
}: DataTablePaginationProps<TData>) {
  // Get current page and total pages
  const currentPage = table.getState().pagination.pageIndex + 1;
  const totalPages = table.getPageCount();

  // Generate page numbers to display
  const generatePaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5; // Maximum number of page buttons to show

    // Always show first page
    items.push(
      <Button
        key="page-1"
        variant={currentPage === 1 ? 'default' : 'outline'}
        className="h-8 w-8 p-0 cursor-pointer"
        onClick={() => table.setPageIndex(0)}
      >
        1
      </Button>,
    );

    // Calculate start and end of visible pages
    let startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

    // Adjust if we're near the end
    if (endPage - startPage < maxVisiblePages - 3) {
      startPage = Math.max(2, endPage - (maxVisiblePages - 3));
    }

    // Show ellipsis if needed before middle pages
    if (startPage > 2) {
      items.push(
        <div key="ellipsis-1" className="px-2 cursor-default">
          ...
        </div>,
      );
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <Button
          key={`page-${i}`}
          variant={currentPage === i ? 'default' : 'outline'}
          className="h-8 w-8 p-0 cursor-pointer"
          onClick={() => table.setPageIndex(i - 1)}
        >
          {i}
        </Button>,
      );
    }

    // Show ellipsis if needed after middle pages
    if (endPage < totalPages - 1) {
      items.push(
        <div key="ellipsis-2" className="px-2 cursor-default">
          ...
        </div>,
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <Button
          key={`page-${totalPages}`}
          variant={currentPage === totalPages ? 'default' : 'outline'}
          className="h-8 w-8 p-0 cursor-pointer"
          onClick={() => table.setPageIndex(totalPages - 1)}
        >
          {totalPages}
        </Button>,
      );
    }

    return items;
  };

  return (
    <div className="flex items-center  justify-self-end pagination-component">
      {/* <div className='flex-1 text-sm text-muted-foreground'>
        {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
      </div> */}
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0 cursor-pointer"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          {/* Page number buttons */}
          <div className="flex items-center space-x-1">
            {generatePaginationItems()}
          </div>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 cursor-pointer"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        {sizeChanger && (
          <div className="flex items-center">
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px] cursor-pointer">
                <SelectValue
                  placeholder={table.getState().pagination.pageSize}
                />
              </SelectTrigger>
              <SelectContent side="top">
                {[2, 5, 10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem
                    key={pageSize}
                    value={`${pageSize}`}
                    className="cursor-pointer"
                  >
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  );
}
