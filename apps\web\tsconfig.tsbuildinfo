{"fileNames": [], "fileInfos": [], "root": [], "options": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "module": 199, "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": 9}, "version": "5.8.3"}