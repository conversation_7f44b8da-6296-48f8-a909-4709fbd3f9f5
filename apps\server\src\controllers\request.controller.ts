import { <PERSON>, ControllerB<PERSON>, Http<PERSON>et, <PERSON>ttp<PERSON><PERSON>, HttpContext, HttpPut, type IHttpContext, Inject, logger } from '@c-visitor/framework'
import { RequestService } from '@/services/request.service'
import { RecognitionService } from '@/services/recognition.service'
import { IRequest, GetRecognitionsResponse, RequestStatus } from '@c-visitor/types'
import { ChangeStatusExecute } from '@/models/executes/request.execute'
import { GetRequestRecognitions } from '@/models/queries/GetRequestRecognitions'
import { getCurrentUserId } from '@/middleware/auth.middleware'

// Define APIResponse interface if it's not available in the types package
interface APIResponse<T> {
  code: string;
  statusCode: number;
  success: boolean;
  data: T;
  message?: string;
}

@Controller('/api/request')
export class RequestController extends ControllerBase {
  constructor(
    @Inject(RequestService) private readonly requestService: RequestService,
    @Inject(RecognitionService) private readonly recognitionService: RecognitionService
  ) {
    super()
  }

  @HttpPost('/upsert')
  async upsertRequest(@HttpContext() context: IHttpContext) {
    try {
      const body = context.request.body
      logger.info('Received request data:', JSON.stringify(body, null, 2))

      // Validate that we have the required structure
      if (!body) {
        return this.error(context.response, 'Request data is required')
      }

      // Extract user ID from JWT token
      const userId = await getCurrentUserId(context)

      // Extract references from the flat structure
      const { references = [], ...requestFields } = body

      // Create the command structure expected by the service
      const command = {
        request: {
          ...requestFields,
          createdBy: userId || undefined, // Add createdBy field
        },
        references: references
      }

      logger.info('Processed command:', JSON.stringify(command, null, 2))

      const result = await this.requestService.upsertRequest(command)

      if (!result.success || !result.data) {
        return this.error(context.response, result.message || 'Failed to upsert request')
      }

      // Return only the request with embedded references (simplified structure)
      return this.success(context.response, result.data as IRequest, result.message || 'Request saved successfully')
    } catch (error) {
      logger.error('Error in upsertRequest controller', { error })
      return this.error(context.response, error instanceof Error ? error.message : 'Failed to create request')
    }
  }

  @HttpGet('/:id')
  async getRequestById(@HttpContext() context: IHttpContext) {
    const id = (context.request as any).params.id
    const result = await this.requestService.getRequestById(id)

    if (!result.success) {
      return this.notFound(context.response, result.message || 'Request not found')
    }

    // Return only the request with embedded references (simplified structure)
    return this.success(context.response, result.data as IRequest, result.message || 'Request found')
  }

  @HttpGet('/')
  async getRequests(@HttpContext() context: IHttpContext) {
    try {
      const queries = (context.request as any).query

      // Parse query parameters
      const pageIndex = queries.pageIndex ? parseInt(queries.pageIndex as string) : 1
      const pageSize = queries.pageSize ? parseInt(queries.pageSize as string) : 10
      const search = queries.search as string || ''
      const status = queries.status as string || ''

      // Parse date filters
      let timeInFrom: Date | undefined
      let timeInTo: Date | undefined
      let startDate: Date | undefined
      let endDate: Date | undefined

      // Legacy date filters (for backward compatibility)
      if (queries.timeInFrom && queries.timeInFrom !== 'undefined' && queries.timeInFrom !== 'null') {
        try {
          timeInFrom = new Date(queries.timeInFrom as string)
          if (isNaN(timeInFrom.getTime())) {
            timeInFrom = undefined
          }
        } catch (e) {
          timeInFrom = undefined
        }
      }

      if (queries.timeInTo && queries.timeInTo !== 'undefined' && queries.timeInTo !== 'null') {
        try {
          timeInTo = new Date(queries.timeInTo as string)
          if (isNaN(timeInTo.getTime())) {
            timeInTo = undefined
          }
        } catch (e) {
          timeInTo = undefined
        }
      }

      // New clear date range filters
      if (queries.startDate && queries.startDate !== 'undefined' && queries.startDate !== 'null') {
        try {
          startDate = new Date(queries.startDate as string)
          if (isNaN(startDate.getTime())) {
            startDate = undefined
          }
        } catch (e) {
          startDate = undefined
        }
      }

      if (queries.endDate && queries.endDate !== 'undefined' && queries.endDate !== 'null') {
        try {
          endDate = new Date(queries.endDate as string)
          if (isNaN(endDate.getTime())) {
            endDate = undefined
          }
        } catch (e) {
          endDate = undefined
        }
      }

      // Create filter object
      const filters: any = {
        pageIndex: isNaN(pageIndex) ? 1 : pageIndex,
        pageSize: isNaN(pageSize) ? 10 : pageSize,
        search: search.trim(),
        // Legacy date filters (for backward compatibility)
        timeInFrom,
        timeInTo,
        // New clear date range filters
        startDate,
        endDate
      }

      // Only add status if it's a valid RequestStatus
      if (status.trim() && ['PENDING', 'APPROVED', 'REJECTED'].includes(status.trim().toUpperCase())) {
        filters.status = status.trim().toUpperCase() as RequestStatus
      }

      const result = await this.requestService.getRequests(filters)
      const response: APIResponse<{ requests: IRequest[] }> = {
        code: result.success ? 'SUCCESS' : 'ERROR',
        statusCode: result.success ? 200 : 500,
        success: result.success,
        data: result.data as { requests: IRequest[] },
        message: result.message
      }
      return this.success(context.response, response)
    } catch (error) {
      logger.error('Error in getRequests:', error)
      return this.error(context.response, error instanceof Error ? error.message : 'Failed to get requests')
    }
  }

  @HttpPut('/changeStatus/:id')
  async updateRequestById(@HttpContext() context: IHttpContext) {
    try {
      const id = (context.request as any).params.id
      const body = context.request.body as ChangeStatusExecute

      // Extract user ID from JWT token 'sub' field
      const userId = await getCurrentUserId(context)

      // Validate rejection reason if status is REJECTED
      if (body.status === 'REJECTED' && !body.reason?.trim()) {
        return this.badRequest(context.response, 'Rejection reason is required when rejecting a request')
      }

      // Update request status with user information from JWT token
      const result = await this.requestService.updateStatus({
        ...body,
        id,
        updatedBy: userId || undefined, // Use user ID from JWT token 'sub' field
      })

      if (!result.success) {
        return this.notFound(context.response)
      }

      // Send email notification based on status change
      let emailNotificationResult = null;
      if (result.success && result.data) {
        try {
          console.log('Attempting to send email notifications for request:', result.data.id);
          emailNotificationResult = await this.requestService.sendStatusNotificationEmails(body.status, result.data);
          console.log('Email notification result:', emailNotificationResult);
        } catch (emailError) {
          console.error('Failed to send email notifications:', emailError);
          emailNotificationResult = {
            success: false,
            emailResults: [],
            templateUsed: 'NONE',
            error: emailError instanceof Error ? emailError.message : 'Unknown email error'
          };
        }
      }

      const response: APIResponse<IRequest & { emailNotification?: any }> = {
        code: result.success ? 'SUCCESS' : 'ERROR',
        statusCode: result.success ? 200 : 404,
        success: result.success,
        data: {
          ...(result.data as IRequest),
          emailNotification: emailNotificationResult
        },
        message: result.message
      }
      return this.success(context.response, response)
    } catch (error) {
      if (error instanceof Error) {
        return this.error(context.response, error.message)
      }
      return this.error(context.response, 'An unexpected error occurred')
    }
  }

  @HttpGet('/:id/recognitions')
  async getRecognitions(@HttpContext() context: IHttpContext) {
    const id = (context.request as any).params.id;
    try {
      const queries = (context.request as any).query

      // Parse and validate query parameters
      const pageIndex = queries.pageIndex ? parseInt(queries.pageIndex as string) : 1
      const pageSize = queries.pageSize ? parseInt(queries.pageSize as string) : 10

      // Create query object
      const queryParams: GetRequestRecognitions = {
        requestId: id,
        referenceId: queries.referenceId as string,
        pageIndex: isNaN(pageIndex) ? 1 : pageIndex,
        pageSize: isNaN(pageSize) ? 10 : pageSize
      }

      // Add optional date parameters only if they are valid
      if (queries.fromDate && queries.fromDate !== 'undefined' && queries.fromDate !== 'null') {
        try {
          queryParams.fromDate = new Date(queries.fromDate as string)
          // Check if date is valid
          if (isNaN(queryParams.fromDate.getTime())) {
            delete queryParams.fromDate
          }
        } catch (e) {
          // Invalid date format, ignore
        }
      }

      if (queries.toDate && queries.toDate !== 'undefined' && queries.toDate !== 'null') {
        try {
          queryParams.toDate = new Date(queries.toDate as string)
          // Check if date is valid
          if (isNaN(queryParams.toDate.getTime())) {
            delete queryParams.toDate
          }
        } catch (e) {
          // Invalid date format, ignore
        }
      }

      // Call service with validated parameters
      const result = await this.recognitionService.getHistories(queryParams)

      if (!result.success) {
        return this.notFound(context.response)
      }

      const response: APIResponse<GetRecognitionsResponse> = {
        code: result.success ? 'SUCCESS' : 'ERROR',
        statusCode: result.success ? 200 : 404,
        success: result.success,
        data: result.data || {
          references: [],
          recognitions: [],
          total: 0,
          pageIndex,
          pageSize,
          totalPages: 0
        },
        message: result.message
      }
      return this.success(context.response, response)
    } catch (error) {
      if (error instanceof Error) {
        return this.error(context.response, error.message)
      }
      return this.error(context.response, 'An unknown error occurred')
    }
  }

  @HttpPost('/recognition/user')
  async getRecognitionsByUser(@HttpContext() context: IHttpContext) {
    try {
      const body = context.request.body

      // Validate required parameter
      if (!body.userId) {
        return this.badRequest(context.response, 'User ID is required')
      }

      // Parse body parameters with defaults
      const userId = body.userId
      const pageIndex = body.pageIndex || 1
      const pageSize = body.pageSize || 10
      const fromDate = body.fromDate ? new Date(body.fromDate) : undefined
      const toDate = body.toDate ? new Date(body.toDate) : undefined

      // Validate pagination parameters
      if (pageIndex < 1) {
        return this.badRequest(context.response, 'Page index must be greater than 0')
      }

      if (pageSize < 1 || pageSize > 100) {
        return this.badRequest(context.response, 'Page size must be between 1 and 100')
      }

      // Validate date parameters
      if (fromDate && isNaN(fromDate.getTime())) {
        return this.badRequest(context.response, 'Invalid fromDate format')
      }

      if (toDate && isNaN(toDate.getTime())) {
        return this.badRequest(context.response, 'Invalid toDate format')
      }

      // Call service to get recognition records
      const result = await this.recognitionService.getRecognitionsByUser(
        userId,
        fromDate,
        toDate,
        pageIndex,
        pageSize
      )

      if (!result.success) {
        return this.error(context.response, result.message || 'Failed to retrieve recognition records')
      }

      const response: APIResponse<typeof result.data> = {
        code: 'SUCCESS',
        statusCode: 200,
        success: true,
        data: result.data,
        message: result.message
      }

      return this.success(context.response, response)
    } catch (error) {
      logger.error('Error in getRecognitionsByUser:', error)
      if (error instanceof Error) {
        return this.error(context.response, error.message)
      }
      return this.error(context.response, 'An unknown error occurred')
    }
  }
}
