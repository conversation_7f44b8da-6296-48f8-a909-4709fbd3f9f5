import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ProtectedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useState } from 'react';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/shared/data-table';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
// UUID only used for file names, not for MongoDB objects
import { AddReferenceDialog } from './createRequest/addVisitorDialog';
import { FormDateTimePicker } from '@/components/shared/datetime-picker/form-date-time-picker';
import { useRequest } from '@/hooks/use-request';
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

export const Route = createFileRoute('/app/create-request')({
  component: CreateRequestComponent,
});

// Define the visitor data type
export interface ReferencesData {
  id?: string;
  fullName: string;
  email: string;
  phone: string;
  unit: string;
  cardIdNumber: string;
  avatar: string;
  cardIdFront: string;
  cardIdBack: string;
}

// Zod schema for request form validation
const upsertRequestSchema = z.object({
  request: z.object({
    purpose: z.string().min(1, { message: 'Purpose is required' }),
    companyName: z.string().min(1, { message: 'Company name is required' }),
    contactName: z.string().min(1, { message: 'Contact name is required' }),
    contactDepartment: z
      .string()
      .min(1, { message: 'Contact department is required' }),
    supervisorName: z
      .string()
      .min(1, { message: 'Supervisor name is required' }),
    supervisorDepartment: z
      .string()
      .min(1, { message: 'Supervisor department is required' }),
    supervisorEmail: z
      .string()
      .email({ message: 'Supervisor email is required' }),
    supervisorPhone: z
      .string()
      .min(1, { message: 'Supervisor phone is required' }),
    timeIn: z.date({ required_error: 'Entry time is required' }),
    timeOut: z.date({ required_error: 'Exit time is required' }),
    deviceInformationBroughtIn: z.string().optional(),
    otherRequest: z.string().optional(),
    note: z.string().optional(),
  }),
  references: z
    .array(
      z.object({
        fullName: z.string().min(1, { message: 'Full name is required' }),
        email: z.string().email({ message: 'Valid email is required' }),
        phone: z.string().min(1, { message: 'Phone is required' }),
        unit: z.string().min(1, { message: 'Unit is required' }),
        cardIdNumber: z
          .string()
          .min(1, { message: 'Card ID number is required' }),
        cardIdFront: z
          .string()
          .min(1, { message: 'Card ID front image is required' }),
        cardIdBack: z
          .string()
          .min(1, { message: 'Card ID back image is required' }),
        avatar: z.string().optional(),
      }),
    )
    .min(1, { message: 'At least one visitor is required' }),
});

type RequestFormValues = z.infer<typeof upsertRequestSchema>;

function CreateRequestComponent() {
  const navigate = useNavigate();
  const [showAddVisitorDialog, setShowAddVisitorDialog] = useState(false);
  const [references, setReferences] = useState<ReferencesData[]>([]);
  const [selectedVisitor, setSelectedVisitor] = useState<ReferencesData | null>(
    null,
  );
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [checkTime, setCheckTime] = useState(false);

  // Get request hooks
  const { useUpsertRequest } = useRequest();
  const upsertRequestMutation = useUpsertRequest();

  const handleBack = () => {
    navigate({ to: '/app/requests' });
  };
  // Initialize form with validation
  const requestForm = useForm<RequestFormValues>({
    resolver: zodResolver(upsertRequestSchema),
    defaultValues: {
      request: {
        purpose: '',
        companyName: '',
        contactName: '',
        contactDepartment: '',
        supervisorName: '',
        supervisorDepartment: '',
        supervisorEmail: '',
        supervisorPhone: '',
        timeIn: undefined,
        timeOut: undefined,
        deviceInformationBroughtIn: '',
        otherRequest: '',
        note: '',
      },
      references: [],
    },
  });

  // Handle visitor dialog
  const handleDialogOpenChange = (open: boolean) => {
    setShowAddVisitorDialog(open);
    if (!open) {
      setSelectedVisitor(null);
    }
  };

  const handleAddReference = (referenceData: ReferencesData) => {
    if (selectedVisitor) {
      // Update existing visitor
      setReferences((prev) =>
        prev.map((ref) =>
          ref.id === selectedVisitor.id ? referenceData : ref,
        ),
      );
    } else {
      // Add new visitor - MongoDB will auto-generate _id
      const newReference = {
        ...referenceData,
        id: referenceData.id || Date.now().toString(),
      };
      setReferences((prev) => [...prev, newReference]);
    }

    // Update form value
    const updatedReferences = selectedVisitor
      ? references.map((ref) =>
          ref.id === selectedVisitor.id ? referenceData : ref,
        )
      : [...references, referenceData];

    // setReferences(updatedReferences);
    // const newPageCount = Math.ceil(updatedReferences.length / pageSize);
    // const newPageIndex =
    //   updatedReferences.length > 0 ? Math.min(pageIndex, newPageCount - 1) : 0;

    // setPageCount(newPageCount);
    // setPageIndex(newPageIndex);
    // setSelectedVisitor(null);

    requestForm.setValue('references', updatedReferences);
    setSelectedVisitor(null);
  };

  const handleEditVisitor = (visitor: ReferencesData) => {
    setSelectedVisitor(visitor);
    setShowAddVisitorDialog(true);
  };

  const handleDeleteVisitor = (visitorId: string) => {
    const updatedReferences = references.filter((ref) => ref.id !== visitorId);
    setReferences(updatedReferences);
    requestForm.setValue('references', updatedReferences);
  };

  const handleVistime = (
    timeIn: Date | undefined,
    timeOut: Date | undefined,
  ) => {
    if (!timeIn || !timeOut) {
      setCheckTime(true); // hoặc false tuỳ ý bạn, thường để true cho phép submit nếu chưa nhập đủ
      return;
    }
    setCheckTime(timeOut >= timeIn);
  };

  const timeIn = requestForm.watch('request.timeIn');
  const timeOut = requestForm.watch('request.timeOut');

  useEffect(() => {
    handleVistime(timeIn, timeOut);
  }, [timeIn, timeOut]);

  // Note: Image processing is handled by the backend RequestService.processReferenceImages
  // We just need to send the base64 data as-is and the backend will handle MinIO upload

  // Handle request submission
  const handleSubmitRequest = async (values: RequestFormValues) => {
    try {
      setIsSubmitting(true);
      setFormSubmitted(true);
      toast.loading('Processing images and submitting request...', {
        id: 'submit-request',
      });

      // Backend will handle image processing via RequestService.processReferenceImages

      // Prepare request data according to UpsertRequestInput interface
      const requestPayload = {
        ...values.request,
        timeIn: values.request.timeIn?.toISOString() || '',
        timeOut: values.request.timeOut?.toISOString(),
        references: values.references, // Send base64 data as-is, backend will process
      };

      console.log('Submitting request payload:', requestPayload);

      // Call API using the hook
      const result = await upsertRequestMutation.mutateAsync(requestPayload);

      if (result) {
        toast.success('Request submitted successfully!', {
          id: 'submit-request',
        });

        // Reset form
        requestForm.reset();
        setReferences([]);

        // Redirect to requests list
        navigate({ to: '/app/createRequest/createRequestSuccessfully' });
      }
    } catch (error: any) {
      console.error('Request submission error:', error);

      // Handle specific error messages
      if (error.message?.includes('upload')) {
        toast.error('Failed to upload images. Please try again.', {
          id: 'submit-request',
        });
      } else if (
        error.message?.includes('network') ||
        error.message?.includes('fetch')
      ) {
        toast.error('Network error. Please check your connection.', {
          id: 'submit-request',
        });
      } else {
        toast.error(
          error.message || 'Unable to submit request. Please try again.',
          { id: 'submit-request' },
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form error
  const onError = () => {
    setFormSubmitted(true);
  };

  // Define table columns for visitors
  const visitorColumns: ColumnDef<ReferencesData>[] = [
    {
      accessorKey: 'fullName',
      header: 'Full Name',
      cell: ({ row }) => (
        <div className="font-medium text-[#26282c]">
          {row.original.fullName}
        </div>
      ),
    },
    {
      accessorKey: 'email',
      header: 'Email',
      cell: ({ row }) => (
        <div className="text-sm text-[#73787e]">{row.original.email}</div>
      ),
    },
    {
      accessorKey: 'phone',
      header: 'Phone',
      cell: ({ row }) => (
        <div className="text-sm text-[#1f2329]">{row.original.phone}</div>
      ),
    },
    {
      accessorKey: 'unit',
      header: 'Unit',
      cell: ({ row }) => (
        <div className="text-sm text-[#1f2329]">{row.original.unit}</div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditVisitor(row.original)}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteVisitor(row.original.id!)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <ProtectedLayout className="bg-[#f5f6f7]">
      <Breadcrumb className="text-xs pb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              onClick={handleBack}
              className="text-[#8f959e] cursor-pointer"
            >
              Access Registration List
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="text-[#67718e]" />
          <BreadcrumbItem>
            <BreadcrumbLink className="text-[#1f2329] cursor-default">
              Create Access Registration
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Form {...requestForm}>
        <form
          onSubmit={requestForm.handleSubmit(handleSubmitRequest, onError)}
          className="flex flex-col bg-[#F5F6F7]"
        >
          <div className="grid gap-6 rounded-lg">
            {/* Header with title and action buttons */}
            <div className="flex justify-between items-center">
              <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#1f2329]">
                Visitor registration
              </p>
            </div>
            <div className="rounded-lg">
              <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 ">
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-8 rounded-lg bg-white">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                        <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                        <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                          <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                            General Information
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-9">
                        <div className="flex flex-col justify-start items-start flex-grow gap-4">
                          <div className="grid grid-cols-2 gap-4 w-full">
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Purpose of visit
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.purpose"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter purpose of visit"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Company
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.companyName"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter company name"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          {/* Contact Information */}
                          <div className="grid grid-cols-2 gap-4 w-full">
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Contact name
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.contactName"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter contact name"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Contact department
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.contactDepartment"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter contact department"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Supervisor Information Section */}
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-8 rounded-lg bg-white">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                        <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                        <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                          <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                            Supervisor Information
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-9">
                        <div className="flex flex-col justify-start items-start flex-grow gap-4">
                          <div className="grid grid-cols-2 gap-4 w-full">
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Supervisor name
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.supervisorName"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter supervisor name"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Supervisor department
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.supervisorDepartment"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter supervisor department"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 w-full">
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Supervisor email
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.supervisorEmail"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter supervisor email"
                                        type="email"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Supervisor phone
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormField
                                control={requestForm.control}
                                name="request.supervisorPhone"
                                render={({ field, fieldState }) => (
                                  <FormItem className="w-full">
                                    <FormControl>
                                      <Input
                                        placeholder="Enter supervisor phone"
                                        {...field}
                                        className={`w-full ${formSubmitted && fieldState.error ? 'border-red-500' : ''}`}
                                      />
                                    </FormControl>
                                    <div className="text-sm font-medium text-destructive mt-1 h-5">
                                      {formSubmitted &&
                                        fieldState.error?.message}
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Time Information Section */}
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-8 rounded-lg bg-white">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                        <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                        <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                          <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                            Visit Time
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-9">
                        <div className="flex flex-col justify-start items-start flex-grow gap-4">
                          <div className="grid grid-cols-2 gap-4 w-full">
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Entry time
                                  </p>
                                  <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                                    *
                                  </p>
                                </div>
                              </div>
                              <FormDateTimePicker
                                control={requestForm.control}
                                name="request.timeIn"
                                formSubmitted={formSubmitted}
                              />
                            </div>
                            <div className="flex flex-col justify-start items-start w-full">
                              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                    Exit time
                                  </p>
                                </div>
                              </div>
                              <FormDateTimePicker
                                control={requestForm.control}
                                name="request.timeOut"
                                formSubmitted={formSubmitted}
                              />
                            </div>
                          </div>
                          {!checkTime && (
                            <div className="text-sm text-red-500 mt-1">
                              Exit time must be greater than or equal to entry
                              time.
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Additional Information Section */}
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-8 rounded-lg bg-white">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                        <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                        <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                          <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                            Additional Information
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-9">
                        <div className="flex flex-col justify-start items-start flex-grow gap-4">
                          <div className="flex flex-col justify-start items-start w-full">
                            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                  Devices brought in
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={requestForm.control}
                              name="request.deviceInformationBroughtIn"
                              render={({ field }) => (
                                <FormItem className="w-full">
                                  <FormControl>
                                    <Textarea
                                      placeholder="List any devices or equipment being brought in"
                                      {...field}
                                      className="w-full"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex flex-col justify-start items-start w-full">
                            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                  Other requests
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={requestForm.control}
                              name="request.otherRequest"
                              render={({ field }) => (
                                <FormItem className="w-full">
                                  <FormControl>
                                    <Textarea
                                      placeholder="Any other special requests or requirements"
                                      {...field}
                                      className="w-full"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="flex flex-col justify-start items-start w-full">
                            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                                <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                                  Notes
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={requestForm.control}
                              name="request.note"
                              render={({ field }) => (
                                <FormItem className="w-full">
                                  <FormControl>
                                    <Textarea
                                      placeholder="Additional notes"
                                      {...field}
                                      className="w-full"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Visitors Section */}
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-8 rounded-lg bg-white">
                      <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                        <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative">
                          <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                            <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                              Visitors
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          onClick={() => setShowAddVisitorDialog(true)}
                          className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative gap-2 px-3 py-1.5 rounded-md bg-[#008fd3]"
                        >
                          <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-white">
                            Add Visitor
                          </p>
                        </Button>
                      </div>

                      <FormField
                        control={requestForm.control}
                        name="references"
                        render={({ fieldState }) => (
                          <FormItem className="w-full">
                            <FormControl>
                              <div>
                                {references.length > 0 ? (
                                  <DataTable
                                    columns={visitorColumns}
                                    data={references}
                                    pageCount={1}
                                    pageSize={10}
                                    pageIndex={0}
                                    onPaginationChange={() => {}}
                                    manualPagination={false}
                                    columnWidths={{
                                      fullName: 200,
                                      email: 250,
                                      phone: 150,
                                      unit: 180,
                                      actions: 150,
                                    }}
                                  />
                                ) : (
                                  <div
                                    className={`text-center py-8 rounded-lg border ${
                                      formSubmitted && fieldState.error
                                        ? 'text-red-500 border-red-500'
                                        : 'text-gray-500 border-gray-200'
                                    }`}
                                  >
                                    No visitors added yet. Click "Add Visitor"
                                    to add visitors.
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <div className="text-sm font-medium text-destructive mt-1 h-5">
                              {formSubmitted && fieldState.error?.message}
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative pt-2 align-self: self-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !checkTime}
                        className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-10 relative gap-2 px-6 py-2 rounded-md bg-[#008fd3] hover:bg-[#007bb8]"
                      >
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-white">
                          {isSubmitting ? 'Processing...' : 'Submit Request'}
                        </p>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Add Visitor Dialog */}
          <AddReferenceDialog
            open={showAddVisitorDialog}
            onOpenChange={handleDialogOpenChange}
            onAddReference={handleAddReference}
            referencesData={selectedVisitor}
          />
        </form>
      </Form>
    </ProtectedLayout>
  );
}
