import { logger } from '@c-visitor/framework';
import { hash } from 'bcryptjs';
import RoleModel from '@/models/entities/Role';
import UserModel from '@/models/entities/User';
import UserRoleModel from '@/models/entities/UserRole';
import { CivamsService } from '@/services/civams.service';

/**
 * Seed default roles: Admin, Staff, User
 */
async function seedRoles() {
  try {
    logger.info('🌱 Seeding roles...');

    const defaultRoles = [
      { name: 'Admin', active: true },
      { name: 'Staff', active: true },
      { name: 'User', active: true },
    ];

    for (const roleData of defaultRoles) {
      const existingRole = await RoleModel.findOne({ name: roleData.name });
      
      if (!existingRole) {
        const role = new RoleModel(roleData);
        await role.save();
        logger.info(`✅ Created role: ${roleData.name}`);
      } else {
        logger.info(`⏭️  Role already exists: ${roleData.name}`);
      }
    }

    logger.info('✅ Roles seeding completed');
  } catch (error) {
    logger.error('❌ Error seeding roles:', error);
    throw error;
  }
}

/**
 * Seed default admin user
 */
async function seedAdminUser() {
  try {
    logger.info('🌱 Seeding admin user...');

    const adminEmail = '<EMAIL>';
    const adminPassword = 'PasS@W0rd';
    const adminFullName = 'C-Visitor Administrator';

    // Check if admin user already exists
    const existingUser = await UserModel.findOne({ email: adminEmail });
    
    if (!existingUser) {
      // Hash password
      const hashedPassword = await hash(adminPassword, 10);

      // Create admin user
      const adminUser = new UserModel({
        email: adminEmail,
        fullName: adminFullName,
        password: hashedPassword,
        emailVerified: true, // Admin user is pre-verified
        active: true,
      });

      await adminUser.save();
      logger.info(`✅ Created admin user: ${adminEmail}`);

      // Assign Admin role to the user
      const adminRole = await RoleModel.findOne({ name: 'Admin' });
      if (adminRole) {
        const userRole = new UserRoleModel({
          userId: adminUser._id,
          roleId: adminRole._id,
          active: true,
        });
        await userRole.save();
        logger.info(`✅ Assigned Admin role to user: ${adminEmail}`);
      } else {
        logger.warn('⚠️  Admin role not found, cannot assign to user');
      }
    } else {
      logger.info(`⏭️  Admin user already exists: ${adminEmail}`);
      
      // Check if admin role is assigned
      const adminRole = await RoleModel.findOne({ name: 'Admin' });
      if (adminRole) {
        const existingUserRole = await UserRoleModel.findOne({
          userId: existingUser._id,
          roleId: adminRole._id,
        });
        
        if (!existingUserRole) {
          const userRole = new UserRoleModel({
            userId: existingUser._id,
            roleId: adminRole._id,
            active: true,
          });
          await userRole.save();
          logger.info(`✅ Assigned Admin role to existing user: ${adminEmail}`);
        }
      }
    }

    logger.info('✅ Admin user seeding completed');
  } catch (error) {
    logger.error('❌ Error seeding admin user:', error);
    throw error;
  }
}

/**
 * Seed PostgreSQL database with C-Access company and Visitor department
 */
async function seedPostgreSQLReferences() {
  try {
    logger.info('🌱 Seeding PostgreSQL references...');

    // Create CivamsService instance without MQTT service for seeding
    const civamsService = new CivamsService(undefined);

    // Seed C-Access company and Visitor department
    await civamsService.seedCAccessReferences();

    logger.info('✅ PostgreSQL references seeding completed');
  } catch (error) {
    logger.error('❌ Error seeding PostgreSQL references:', error);
    throw error;
  }
}

/**
 * Main seed function
 */
export async function seedDatabase() {
  try {
    logger.info('🚀 Starting database seeding...');

    // Seed MongoDB data first
    await seedRoles();
    await seedAdminUser();

    // Seed PostgreSQL references
    await seedPostgreSQLReferences();

    logger.info('🎉 Database seeding completed successfully!');
  } catch (error) {
    logger.error('💥 Database seeding failed:', error);
    throw error;
  }
}

/**
 * Reset database (for development only)
 */
export async function resetDatabase() {
  try {
    logger.warn('🗑️  Resetting database...');

    // Delete all data (use with caution!)
    await UserRoleModel.deleteMany({});
    await UserModel.deleteMany({});
    await RoleModel.deleteMany({});

    logger.info('✅ Database reset completed');
  } catch (error) {
    logger.error('❌ Error resetting database:', error);
    throw error;
  }
}

/**
 * Seed database with fresh data (reset + seed)
 */
export async function seedFreshDatabase() {
  try {
    logger.info('🔄 Seeding fresh database...');
    
    await resetDatabase();
    await seedDatabase();
    
    logger.info('🎉 Fresh database seeding completed!');
  } catch (error) {
    logger.error('💥 Fresh database seeding failed:', error);
    throw error;
  }
}
