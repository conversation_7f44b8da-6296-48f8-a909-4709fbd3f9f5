import { Response } from 'express';
import { type APIResponse } from '@c-visitor/types';

/**
 * Utility functions for handling API responses
 */
export class ResponseUtils {
  /**
   * Format a success response
   * @param data The data to include in the response
   * @param message Success message
   * @param statusCode HTTP status code
   * @returns Formatted API response object
   */
  static formatSuccess<T>(
    data?: T,
    message = 'Success',
    statusCode = 200,
  ): APIResponse<T> {
    return {
      code: 'SUCCESS',
      success: true,
      statusCode,
      message,
      data,
    };
  }

  /**
   * Format an error response
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code
   * @param details Additional error details
   * @returns Formatted API error response object
   */
  static formatError(
    message: string,
    statusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[],
  ): APIResponse {
    return {
      success: false,
      statusCode,
      message,
      code,
      errors: details,
    };
  }

  static sendText(res: Response, text?: string, statusCode = 200) {
    return res.status(statusCode).send(text);
  }

  /**
   * Send a success response
   * @param res Express response object
   * @param data Data to include in the response
   * @param message Success message
   * @param statusCode HTTP status code
   * @returns Express response
   */
  static sendSuccess<T>(
    res: Response,
    data?: T,
    message = 'Success',
    statusCode = 200,
  ): Response {
    const response = this.formatSuccess(data, message, statusCode);
    return res.status(statusCode).json(response);
  }

  /**
   * Send a created success response (201)
   * @param res Express response object
   * @param data Data to include in the response
   * @param message Success message
   * @returns Express response
   */
  static sendCreated<T>(
    res: Response,
    data?: T,
    message = 'Resource created successfully',
  ): Response {
    return this.sendSuccess(res, data, message, 201);
  }

  /**
   * Send an error response
   * @param res Express response object
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code
   * @param details Additional error details
   * @returns Express response
   */
  static sendError(
    res: Response,
    message: string,
    statusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[],
  ): Response {
    const response = this.formatError(message, statusCode, code, details);
    return res.status(statusCode).json(response);
  }

  /**
   * Transform data before sending response (remove sensitive fields)
   * @param data Data to transform
   * @returns Transformed data
   */
  static transformData<T>(data: T): T {
    if (data === null || data === undefined) {
      return data;
    }

    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'refreshToken'];

    if (Array.isArray(data)) {
      return data.map((item) => this.transformData(item)) as unknown as T;
    }

    if (typeof data === 'object' && data !== null) {
      const transformed = { ...data } as any;
      sensitiveFields.forEach((field) => {
        if (field in transformed) {
          delete transformed[field];
        }
      });
      return transformed as T;
    }

    return data;
  }
}
