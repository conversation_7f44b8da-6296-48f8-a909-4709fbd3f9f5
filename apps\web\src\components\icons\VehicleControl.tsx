import React from 'react';
import { cn } from '@/lib/utils';

interface VehicleControlIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const VehicleControlIcon: React.FC<VehicleControlIconProps> = ({ 
  className, 
  color = "#1F2329", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M1.54928 8.93485C1.63586 8.46255 2.01668 7.97966 2.63125 7.56289C3.73782 6.81243 5.00166 6.81552 6.26668 6.76868C6.68651 6.75312 7.8138 6.74799 9 6.75068C10.1862 6.74799 11.3135 6.75312 11.7333 6.76868C12.9983 6.81552 14.2622 6.81243 15.3688 7.56289C15.9833 7.97966 16.3641 8.46255 16.4507 8.93485C16.525 9.34023 16.5136 10.7313 16.4279 11.713C16.3826 12.2305 16.3366 12.4824 16.2679 12.5883C16.0607 12.9077 15.4625 13.1763 14.5824 13.3451C13.7565 13.5034 13.8793 13.5 9 13.5C4.1207 13.5 4.24351 13.5034 3.41762 13.3451C2.53754 13.1763 1.9393 12.9077 1.73205 12.5883C1.66338 12.4824 1.61737 12.2305 1.57215 11.713C1.48641 10.7313 1.47495 9.34023 1.54928 8.93485Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.375 6.75L4.35993 3.36758C4.4504 3.14257 4.62622 2.95464 4.87401 2.87037C7.30624 2.04321 10.6938 2.04321 13.126 2.87037C13.3738 2.95464 13.5496 3.14257 13.6401 3.36758L14.625 6.75"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.875 9L3 9.375"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.125 9L15 9.375"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 13.125L6.18425 12.6644C6.45826 11.9794 6.59526 11.6368 6.88095 11.4434C7.16665 11.25 7.53554 11.25 8.27332 11.25L9.72668 11.25C10.4645 11.25 10.8334 11.25 11.119 11.4434C11.4047 11.6368 11.5417 11.9794 11.8157 12.6644L12 13.125"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.25121 13.9217L5.25121 14.5233C5.25125 15.1791 5.22254 15.302 5.02457 15.4934C4.96664 15.5494 4.8489 15.6301 4.76296 15.6726C4.61249 15.7471 4.56202 15.75 3.39576 15.75C2.20329 15.75 2.18196 15.7487 1.99821 15.6657C1.89555 15.6193 1.76606 15.5266 1.71043 15.4596C1.5141 15.2234 1.50477 15.1537 1.50293 13.9094L1.50344 11.25"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M12.7488 13.9217V14.5233C12.7488 15.1791 12.7775 15.302 12.9754 15.4934C13.0334 15.5494 13.1511 15.6301 13.237 15.6726C13.3875 15.7471 13.438 15.75 14.6042 15.75C15.7967 15.75 15.818 15.7487 16.0018 15.6657C16.1044 15.6193 16.2339 15.5266 16.2896 15.4596C16.4859 15.2234 16.4952 15.1537 16.4971 13.9094L16.4966 11.25"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M15 6.75L16.125 6.375"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3 6.75L1.875 6.375"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default VehicleControlIcon;
