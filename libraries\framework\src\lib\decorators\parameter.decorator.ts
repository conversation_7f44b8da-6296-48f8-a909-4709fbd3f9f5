import 'reflect-metadata';

export const PARAMS_METADATA = 'parameters';

export function HttpContext() {
  return createParamDecorator('http_context');
}

export interface ParamMetadata {
  index: number;
  type:
    | 'http_context'
    | 'body'
    | 'param'
    | 'query'
    | 'headers'
    | 'req'
    | 'res'
    | 'next';
  name?: string;
  validator?: (value: any) => any;
}

/**
 * Parameter decorator factory
 */
function createParamDecorator(
  type: ParamMetadata['type'],
  name?: string,
  validator?: (value: any) => any
) {
  return function (
    target: any,
    propertyKey: string | symbol,
    parameterIndex: number
  ) {
    const parameters: ParamMetadata[] =
      Reflect.getMetadata(PARAMS_METADATA, target, propertyKey) || [];
    parameters.push({ index: parameterIndex, type, name, validator });
    Reflect.defineMetadata(PARAMS_METADATA, parameters, target, propertyKey);
  };
}
