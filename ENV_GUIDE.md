# Environment Configuration Guide

## 🚀 SIÊU ĐƠN GIẢN - Chỉ cần thay đổi NODE_ENV!

### 📁 File cấu hình:
- `apps/server/.env` - File duy nhất chứa tất cả config
- `apps/server/src/configs/environment.ts` - Logic load config (đã được đơn giản hóa)

### 🔄 Cách switch môi trường:

#### DEVELOPMENT (hiện tại)
```bash
NODE_ENV=development
```
→ Tự động sử dụng tất cả `DEVELOPMENT_*` variables

#### PRODUCTION
```bash
NODE_ENV=production
```
→ Tự động sử dụng tất cả `PRODUCTION_*` variables

### 🔧 Cách hoạt động:

1. **Load file .env**: `environment.ts` tự động load `apps/server/.env`
2. **Đọc NODE_ENV**: Xá<PERSON> định môi trường hiện tại
3. **Auto-prefix**: Tìm variable với prefix tương ứng
4. **Fallback**: Nếu không tìm thấy prefixed variable, dùng base variable

**Ví dụ:**
```bash
# NODE_ENV=development
PORT → tìm DEVELOPMENT_PORT (5000) ✅
API_PREFIX → tìm DEVELOPMENT_API_PREFIX → không có → dùng API_PREFIX (/api) ✅

# NODE_ENV=production
PORT → tìm PRODUCTION_PORT (3000) ✅
```

### ⚠️ QUAN TRỌNG cho Production:

Thay đổi các giá trị `CHANGE_THIS_*` trong section PRODUCTION:

```bash
PRODUCTION_JWT_SECRET=your-super-strong-secret-key-here
PRODUCTION_DB_CONNECTION=mongodb+srv://prod_user:<EMAIL>/prod_db
PRODUCTION_CORS_ALLOWED_ORIGINS=https://your-domain.com
# ... tất cả CHANGE_THIS_* khác
```

### 🔒 Security Checklist:
- [ ] JWT_SECRET đã thay đổi
- [ ] DB_CONNECTION sử dụng production database
- [ ] CORS chỉ cho phép domain thực tế
- [ ] Tất cả passwords đã thay đổi
- [ ] SSL enabled cho external services

### 💡 Ưu điểm của cấu trúc mới:
- ✅ **Siêu đơn giản**: 1 file .env, 1 dòng NODE_ENV
- ✅ **Code ngắn gọn**: environment.ts chỉ 103 dòng (từ 255 dòng)
- ✅ **Tự động**: Không cần comment/uncomment
- ✅ **Type-safe**: Tự động convert kiểu dữ liệu
- ✅ **Fallback**: Có giá trị mặc định cho mọi config
