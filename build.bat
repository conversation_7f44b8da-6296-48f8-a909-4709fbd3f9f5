@echo off
echo Starting build process...

REM Clean up previous build files
echo Cleaning up previous build files...
if exist .\apps\web\dist (
    echo Removing web dist directory...
    rmdir /s /q .\apps\web\dist
)
if exist .\apps\server\dist (
    echo Removing server build directory...
    rmdir /s /q .\apps\server\dist
)
echo Clean up completed

REM Step 1: Build web app
echo Building web application...
call pnpm run build:prod:web
if %ERRORLEVEL% neq 0 (
    echo Error: Web application build failed
    exit /b 1
)
echo Web application build completed successfully

REM Step 2: Build server app
echo Building server application...
call pnpm run build:prod:server
if %ERRORLEVEL% neq 0 (
    echo Error: Server application build failed
    exit /b 1
)
echo Server application build completed successfully

REM Step 3: Build Docker image for web app
echo Building Docker image for web application...
docker build -f .\apps\web\Dockerfile . -t nguyendkn/c-visitor-web:latest
if %ERRORLEVEL% neq 0 (
    echo Error: Docker image build for web application failed
    exit /b 1
)
echo Docker image for web application built successfully

REM Step 4: Build Docker image for server app
echo Building Docker image for server application...
docker build -f .\apps\server\Dockerfile . -t nguyendkn/c-visitor-server:latest
if %ERRORLEVEL% neq 0 (
    echo Error: Docker image build for server application failed
    exit /b 1
)
echo Docker image for server application built successfully

echo Build process completed successfully!
