import React from 'react';

interface ChevronDownIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const ChevronDownIcon: React.FC<ChevronDownIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0 w-4 h-4 relative", 
  width = 16, 
  height = 16 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <g clipPath="url(#clip0_174_4347)">
        <path
          d="M12 6L8 10L4 6"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_174_4347">
          <rect width={16} height={16} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
