#!/usr/bin/env ts-node

/**
 * Database Seeding Script
 * 
 * Usage:
 * - npm run seed              # Seed database (safe, won't overwrite existing data)
 * - npm run seed:fresh        # Reset and seed database (WARNING: deletes all data)
 * - npm run seed:reset        # Reset database only (WARNING: deletes all data)
 */

import dotenv from 'dotenv';
import path from 'path';
import { logger } from '@c-visitor/framework';
import { initializeModels } from '../models';
import { seedDatabase, seedFreshDatabase, resetDatabase } from '../configs/seed';

// Load environment variables
const envPath = path.resolve(__dirname, '../../.env');
dotenv.config({ path: envPath });

async function main() {
  try {
    // Get command line argument
    const command = process.argv[2] || 'seed';

    logger.info('🚀 Starting database seeding script...');
    logger.info(`📋 Command: ${command}`);

    // Initialize database connection
    await initializeModels();

    switch (command) {
      case 'seed':
        await seedDatabase();
        break;

      case 'fresh':
        logger.warn('⚠️  WARNING: This will delete ALL existing data!');
        logger.warn('⚠️  Press Ctrl+C to cancel, or wait 5 seconds to continue...');
        
        // Wait 5 seconds before proceeding
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        await seedFreshDatabase();
        break;

      case 'reset':
        logger.warn('⚠️  WARNING: This will delete ALL existing data!');
        logger.warn('⚠️  Press Ctrl+C to cancel, or wait 5 seconds to continue...');
        
        // Wait 5 seconds before proceeding
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        await resetDatabase();
        break;

      default:
        logger.error(`❌ Unknown command: ${command}`);
        logger.info('Available commands:');
        logger.info('  seed   - Seed database (safe)');
        logger.info('  fresh  - Reset and seed database (destructive)');
        logger.info('  reset  - Reset database only (destructive)');
        process.exit(1);
    }

    logger.info('🎉 Seeding script completed successfully!');
    process.exit(0);

  } catch (error) {
    logger.error('💥 Seeding script failed:', error);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  logger.info('👋 Seeding script interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('👋 Seeding script terminated');
  process.exit(0);
});

// Run the script
main();
