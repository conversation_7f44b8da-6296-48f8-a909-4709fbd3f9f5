import { Service, Inject, ServiceResponse, logger } from '@c-visitor/framework';
import { IRequest, IReference, RequestStatus } from '@c-visitor/types';
import { RequestRepository } from '@/repositories/request.repository';
import { RecognitionRepository } from '@/repositories/recognition.repository';
import { FileService } from './file.service';
import { MailService } from './mail.service';
import { BaseService } from './base.service';
import { RedisService } from './redis.service';
import { CivamsService } from './civams.service';
import { UpsertRequestCommand } from '@/models/commands/upsert-request.command';
import { ListRequestQuery } from '@/models/queries/ListRequestQuery';
import { ChangeStatusExecute } from '@/models/executes/request.execute';

// Email template types
type EmailTemplateType = 'APPROVED' | 'REJECTED' | 'PENDING';

@Service()
export class RequestService extends BaseService {
  constructor(
    @Inject(RequestRepository)
    private readonly requestRepository: RequestRepository,
    @Inject(RecognitionRepository)
    private readonly recognitionRepository: RecognitionRepository,
    @Inject(FileService) private readonly fileService: FileService,
    @Inject(MailService) private readonly mailService: MailService,
    @Inject(RedisService) private readonly redisService: RedisService,
    @Inject(CivamsService) private readonly civamsService: CivamsService,
  ) {
    super();
  }

  async updateStatus(
    execute: ChangeStatusExecute,
  ): Promise<ServiceResponse<IRequest>> {
    console.log('Updating request status with:', execute);

    // Prepare update data
    const updateData: any = {
      status: execute.status,
      note: execute.note,
      updatedBy: execute.updatedBy, // Track who updated the status using JWT 'sub' field
    };

    // Add reason field only if status is REJECTED and reason is provided
    if (execute.status === 'REJECTED' && execute.reason) {
      updateData.reason = execute.reason;
    } else if (execute.status !== 'REJECTED') {
      // Clear reason if status is not REJECTED
      updateData.reason = null;
    }

    // Update the request with the new status and related fields
    await this.requestRepository.update(execute.id, updateData);

    // Use findByIdWithReferences to get the request with the user object for changeStatusBy
    const request = await this.requestRepository.findByIdWithReferences(
      execute.id,
    );

    if (!request) {
      return this.error('Request not found');
    }

    console.log('Updated request:', JSON.stringify(request, null, 2));

    return this.success<IRequest>(
      request as IRequest,
      'Request status updated successfully',
    );
  }

  /**
   * Send status notification emails to all references in a request
   * @param status - Request status
   * @param requestData - Request data
   * @returns Email sending results
   */
  async sendStatusNotificationEmails(
    status: RequestStatus,
    requestData: IRequest,
  ): Promise<{
    success: boolean;
    emailResults: Array<{
      email: string;
      success: boolean;
      messageId?: string;
      error?: unknown;
    }>;
    templateUsed: string;
  }> {
    console.log('Starting email notification process for request:', requestData.id);

    // Validate request data
    if (!requestData) {
      console.error('Request data is null or undefined');
      return {
        success: false,
        emailResults: [],
        templateUsed: 'NONE',
      };
    }

    if (!requestData.references || requestData.references.length === 0) {
      console.log('No references found to send emails to');
      return {
        success: true,
        emailResults: [],
        templateUsed: 'NONE',
      };
    }

    // Validate required fields for email template
    const requiredFields = ['companyName', 'timeIn', 'timeOut'];
    const missingFields = requiredFields.filter(field => !requestData[field as keyof IRequest]);

    if (missingFields.length > 0) {
      console.error('Missing required fields for email template:', missingFields);
      return {
        success: false,
        emailResults: [],
        templateUsed: 'NONE',
      };
    }

    // Prepare common data for email templates
    const companyName = requestData.companyName;
    const requestCode = this.generateCode(
      'P',
      requestData.code?.toString() || '',
      6,
    );
    const supervisorEmail = requestData.supervisorEmail;
    const supervisorName = requestData.supervisorName || 'there';
    const supervisorPhone = requestData.supervisorPhone || 'there';
    const timeIn = requestData.timeIn;
    const timeOut = requestData.timeOut;
    const note = requestData.note || '';

    // Template data
    const templateData = {
      companyName,
      requestCode,
      timeIn,
      timeOut,
      supervisorEmail,
      supervisorPhone,
      supervisorName,
      note,
    };

    console.log('Template data prepared:', templateData);

    // Determine template type based on status
    const templateType = status as EmailTemplateType;

    // Generate email template
    let emailHtml: string;
    try {
      emailHtml = this.generateEmailTemplate(templateType, templateData);
      console.log('Email template generated successfully for type:', templateType);
    } catch (templateError) {
      console.error('Failed to generate email template:', templateError);
      return {
        success: false,
        emailResults: [],
        templateUsed: templateType,
      };
    }

    const emailResults: Array<{
      email: string;
      success: boolean;
      messageId?: string;
      error?: unknown;
    }> = [];

    // Send emails to all references
    for (const reference of requestData.references) {
      try {
        console.log(`Sending email to: ${reference.email}`);
        const emailResult = await this.mailService.sendEmail({
          to: reference.email,
          subject: `Thông báo trạng thái phiếu ra vào ${companyName} - ${requestCode}`,
          html: emailHtml,
        });

        emailResults.push({
          email: reference.email,
          success: emailResult.success,
          messageId: emailResult.messageId,
          error: emailResult.error,
        });

        if (!emailResult.success) {
          console.error(
            `Failed to send ${status.toLowerCase()} email to ${reference.email}:`,
            emailResult.error,
          );
        } else {
          console.log(`Email sent successfully to ${reference.email}, messageId: ${emailResult.messageId}`);
        }
      } catch (error) {
        console.error(`Error sending email to ${reference.email}:`, error);
        emailResults.push({
          email: reference.email,
          success: false,
          error: error,
        });
      }
    }

    const allEmailsSuccessful = emailResults.every(result => result.success);
    console.log('Email sending completed. All successful:', allEmailsSuccessful);

    return {
      success: allEmailsSuccessful,
      emailResults,
      templateUsed: templateType,
    };
  }

  /**
   * Process and save base64 images for a reference
   * @param reference Reference data with possible base64 images
   * @returns Processed reference with file IDs instead of base64 data
   */
  private async processReferenceImages(
    reference: IReference,
  ): Promise<IReference> {
    const processedReference = { ...reference };

    // Process cardIdFront if it's a base64 image
    if (reference.cardIdFront && reference.cardIdFront.startsWith('data:')) {
      try {
        const fileName = await this.fileService.saveBase64Image(
          reference.cardIdFront,
          `card_front_${Date.now()}.jpg`,
        );
        processedReference.cardIdFront = fileName;
      } catch (error) {
        console.error('Error saving cardIdFront image:', error);
      }
    }

    // Process cardIdBack if it's a base64 image
    if (reference.cardIdBack && reference.cardIdBack.startsWith('data:')) {
      try {
        const fileName = await this.fileService.saveBase64Image(
          reference.cardIdBack,
          `card_back_${Date.now()}.jpg`,
        );
        processedReference.cardIdBack = fileName;
      } catch (error) {
        console.error('Error saving cardIdBack image:', error);
      }
    }

    // Process avatar if it's a base64 image
    if (reference.avatar && reference.avatar.startsWith('data:')) {
      try {
        const fileName = await this.fileService.saveBase64Image(
          reference.avatar,
          `avatar_${Date.now()}.jpg`,
        );
        processedReference.avatar = fileName;
      } catch (error) {
        console.error('Error saving avatar image:', error);
      }
    }

    return processedReference;
  }

  /**
   * Create a new request with references (simplified)
   * @param command Request and references data
   * @returns Service response with the created request
   */
  async upsertRequest(
    command: UpsertRequestCommand,
  ): Promise<ServiceResponse<IRequest>> {
    try {
      logger.info('Creating new request...', {
        hasReferences: command.references?.length > 0,
        referenceCount: command.references?.length || 0
      });

      // Process base64 images in references if they exist
      let processedReferences = command.references || [];
      if (processedReferences.length > 0) {
        try {
          processedReferences = await Promise.all(
            processedReferences.map(async (ref) => {
              return await this.processReferenceImages(ref);
            }),
          );
        } catch (error) {
          logger.error('Error processing reference images', { error });
          return this.error('Failed to process reference images', error);
        }
      }

      // Generate request code if not provided
      let requestCode = command.request.code;
      if (!requestCode) {
        // Use auto-increment code
        requestCode = await this.generateAutoIncrementCode();
      }

      // Prepare clean command for repository
      const cleanCommand: UpsertRequestCommand = {
        request: {
          ...command.request,
          code: requestCode
        },
        references: processedReferences
      };

      // Create the request (repository handles MongoDB _id generation)
      const result = await this.requestRepository.upsertAsync(cleanCommand);

      logger.info('Request created successfully', {
        requestId: result.id,
        code: result.code,
        referencesCount: result.references?.length || 0
      });

      // Post-processing: Redis caching and MQTT face quality requests
      if (result.references && result.references.length > 0) {
        await this.postProcessReferences(result.references, command.request.companyId || '');
      }

      return this.success(result, 'Request created successfully');
    } catch (error) {
      logger.error('Failed to create request', { error });
      return this.error('Failed to create request', error);
    }
  }

  async getRequestById(id: string): Promise<ServiceResponse<IRequest>> {
    try {
      const request = await this.requestRepository.findByIdWithReferences(id);
      if (!request) {
        return this.error('Request not found');
      }

      // Log the request to see what we're getting
      console.log('Request from repository:', JSON.stringify(request, null, 2));

      // Check if changeStatusBy is a string or an object
      if (request.changeStatusBy) {
        console.log('changeStatusBy type:', typeof request.changeStatusBy);
        if (typeof request.changeStatusBy === 'string') {
          console.log(
            'changeStatusBy is still a string:',
            request.changeStatusBy,
          );
        } else {
          console.log(
            'changeStatusBy is an object:',
            JSON.stringify(request.changeStatusBy, null, 2),
          );
        }
      }

      // Add recognition stats directly to each reference
      if (request.references && request.references.length > 0) {
        // Get all mapping IDs from references
        const mappingIds: string[] = [];
        request.references.forEach(reference => {
          if (reference.mappings && reference.mappings.length > 0) {
            mappingIds.push(...reference.mappings);
          }
        });

        // Get recognition stats for all mapping IDs
        const recognitionStats = await this.recognitionRepository.getRecognitionStatsByIds(mappingIds);

        // Add stats to each reference
        request.references = request.references.map(reference => {
          const refStats: { recognitionCount: number; firstCheckIn: Date | null; lastCheckOut: Date | null } = {
            recognitionCount: 0,
            firstCheckIn: null,
            lastCheckOut: null
          };

          // Find stats for this reference by checking its mappings
          if (reference.mappings && reference.mappings.length > 0) {
            for (const mappingId of reference.mappings) {
              if (recognitionStats[mappingId]) {
                const stats = recognitionStats[mappingId];
                refStats.recognitionCount += stats.recognitionCount;

                // Update first check-in (earliest)
                if (stats.firstCheckIn && (!refStats.firstCheckIn || stats.firstCheckIn < refStats.firstCheckIn)) {
                  refStats.firstCheckIn = stats.firstCheckIn;
                }

                // Update last check-out (latest)
                if (stats.lastCheckOut && (!refStats.lastCheckOut || stats.lastCheckOut > refStats.lastCheckOut)) {
                  refStats.lastCheckOut = stats.lastCheckOut;
                }
              }
            }
          }

          return {
            ...reference,
            recognitionCount: refStats.recognitionCount,
            firstCheckIn: refStats.firstCheckIn,
            lastCheckOut: refStats.lastCheckOut,
          };
        });
      }

      return this.success(request as IRequest, 'Request found');
    } catch (error) {
      return this.error('Failed to get request', error);
    }
  }

  async getRequests(params: ListRequestQuery) {
    try {
      // Ensure params is an object with default values
      const queryParams: ListRequestQuery = {
        keyword: params.keyword,
        pageIndex: params.pageIndex
          ? parseInt(params.pageIndex as unknown as string)
          : 1,
        pageSize: params.pageSize
          ? parseInt(params.pageSize as unknown as string)
          : 10,
        timeIn: params.timeIn,
        timeOut: params.timeOut,
        status: params.status,
        // New search and filter fields
        search: params.search,
        // Legacy date filters (for backward compatibility)
        timeInFrom: params.timeInFrom,
        timeInTo: params.timeInTo,
        // New clear date range filters
        startDate: params.startDate,
        endDate: params.endDate,
      };

      // Get paginated results
      const paginatedResult =
        await this.requestRepository.getRequests(queryParams);

      return this.success(
        {
          items: paginatedResult.items,
          pagination: {
            total: paginatedResult.total,
            pageIndex: paginatedResult.pageIndex,
            pageSize: paginatedResult.pageSize,
            totalPages: paginatedResult.totalPages,
          },
        },
        'Requests retrieved successfully',
      );
    } catch (error) {
      return this.error('Failed to get requests', error);
    }
  }

  /**
   * Generate auto-increment request code using MongoDB findOneAndUpdate
   * @returns Auto-incremented request code like 1, 2, 3, etc.
   */
  private async generateAutoIncrementCode(): Promise<number> {
    try {
      // Import mongoose for direct model access
      const mongoose = require('mongoose');

      // Define counter schema if not exists
      const counterSchema = new mongoose.Schema({
        _id: { type: String, required: true },
        sequence: { type: Number, default: 0 }
      });

      // Get or create Counter model
      const Counter = mongoose.models.Counter || mongoose.model('Counter', counterSchema);

      // Simple auto-increment without complex initialization
      const counter = await Counter.findOneAndUpdate(
        { _id: 'request_code' },
        { $inc: { sequence: 1 } },
        { new: true, upsert: true }
      );

      logger.info('Generated auto-increment code:', counter.sequence);
      return counter.sequence;
    } catch (error) {
      logger.error('Error generating auto-increment code', { error });
      // Fallback to simple sequential number based on timestamp
      const simpleCode = Math.floor(Date.now() / 1000) % 100000; // Last 5 digits of timestamp
      return simpleCode;
    }
  }

  /**
   * Generate formatted code with prefix and padding
   * @param prefix Code prefix (e.g., 'REQ')
   * @param code Numeric code to format
   * @param contextLength Total length for padding
   * @returns Formatted code like 'REQ000001'
   */
  generateCode(prefix: string, code: string, contextLength: number) {
    // Input code sẽ là auto number ví dụ là 123456
    // Output code sẽ là REQ000001 với padding zeros
    const paddedCode = code.padStart(contextLength, '0');
    return `${prefix}${paddedCode}`;
  }

  /**
   * Get display code for a request (with REQ prefix)
   * @param requestCode Numeric request code
   * @returns Formatted display code like 'REQ000001'
   */
  getDisplayCode(requestCode: number): string {
    return this.generateCode('REQ', requestCode.toString(), 6);
  }

  /**
   * Generate email template for request status notifications
   * @param type - Template type (APPROVED, REJECTED, PENDING)
   * @param data - Data for the template
   * @returns HTML string for the email
   */
  private generateEmailTemplate(
    type: EmailTemplateType,
    data: {
      companyName: string;
      requestCode: string;
      timeIn: string;
      timeOut: string;
      supervisorEmail: string;
      supervisorPhone: string;
      supervisorName: string;
      note?: string;
    },
  ): string {
    const {
      requestCode,
      timeIn,
      timeOut,
      supervisorEmail,
      supervisorPhone,
      supervisorName,
      note,
    } = data;

    const baseStyles = {
      container:
        'display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start; position: relative; overflow: hidden; gap: 10px; padding: 24px; background: #fff;',
      paragraph:
        'flex-grow: 0; flex-shrink: 0; font-size: 12px; text-align: left; color: #000;',
      bold: 'flex-grow: 0; flex-shrink: 0; font-size: 12px; font-weight: 700; text-align: left; color: #000;',
      normal:
        'flex-grow: 0; flex-shrink: 0; font-size: 12px; font-weight: 500; text-align: left; color: #000;',
    };

    // Common header and intro
    let template = `<div style="${baseStyles.container}">
      <p style="${baseStyles.paragraph}">
        <span style="${baseStyles.bold}">Kính gửi</span>
        <span style="${baseStyles.normal}"> Quý khách hàng,</span>
        <br />
        <span style="${baseStyles.normal}">Chúng tôi xin thông báo về trạng thái phiếu đăng ký ra vào của Quý khách:</span>
        <br />
        <span style="${baseStyles.bold}">Mã phiếu</span>
        <span style="${baseStyles.normal}">: ${requestCode}</span>
        <br />
        <span style="${baseStyles.bold}">Thời gian dự kiến vào</span>
        <span style="${baseStyles.normal}">: ${timeIn}</span>
        <br />
        <span style="${baseStyles.bold}">Thời gian dự kiến ra</span>
        <span style="${baseStyles.normal}">: ${timeOut}</span>
        <br />
        <span style="${baseStyles.bold}">Trạng thái hiện tại</span>
        <span style="${baseStyles.normal}">: `;

    // Status-specific content
    switch (type) {
      case 'APPROVED':
        template += `Đã duyệt</span>
          <br />
          <span style="${baseStyles.normal}">Mọi thắc mắc, xin vui lòng liên hệ: ${supervisorPhone} hoặc ${supervisorEmail}</span>`;
        break;
      case 'REJECTED':
        template += `Từ chối</span>
          <br />
          <span style="${baseStyles.bold}">Lý do từ chối</span>
          <span style="${baseStyles.normal}">: ${note || 'Không có lý do cụ thể'}</span>`;
        break;
      case 'PENDING':
        template += `Chờ duyệt</span>
          <br />
          <span style="${baseStyles.normal}">Mọi thắc mắc, xin vui lòng liên hệ: ${supervisorName} hoặc ${supervisorEmail}</span>`;
        break;
    }

    // Common footer
    template += `
        <br />
        <span style="${baseStyles.normal}">Trân trọng,</span>
        <br />
      </p>
    </div>`;

    return template;
  }

  /**
   * Post-process references: PostgreSQL user creation, Redis caching and MQTT face quality requests
   */
  private async postProcessReferences(references: IReference[], companyId: string): Promise<void> {
    for (const reference of references) {
      try {
        logger.info('🔄 Post-processing reference...', {
          referenceId: reference.id,
          email: reference.email
        });

        // 1. Insert reference as user into PostgreSQL and update MongoDB mappings
        try {
          const { postgresUser, updatedReference } = await this.civamsService.processNewReference(reference);

          logger.info('✅ PostgreSQL user created and mappings updated', {
            referenceId: reference.id,
            postgresUserId: postgresUser.id,
            mappings: updatedReference?.mappings
          });
        } catch (postgresError) {
          logger.error('❌ Failed to process PostgreSQL user for reference', {
            referenceId: reference.id,
            error: postgresError
          });
          // Continue with other processing even if PostgreSQL fails
        }

        // 2. Cache reference in Redis
        await this.redisService.set(`reference:${reference.id}`, 1, 3600); // 1 hour TTL

        // 3. Get base64 image for face quality request
        if (reference.avatar) {
          const { fileName, base64 } = await this.fileService.getBase64ImageByFileName(reference.avatar);

          // Send face quality request via MQTT
          await this.civamsService.faceQualityRequest(fileName, base64, reference.id, companyId);

          // Subscribe to face quality acknowledgment
          await this.civamsService.faceQualityAck((message) => {
            try {
              const json = JSON.parse(message.payload.toString());
              logger.info('Received FaceQualityAck message', {
                referenceId: reference.id,
                message: json
              });
            } catch (error) {
              logger.error('Error parsing FaceQualityAck message', { error });
            }
          });
        }

        logger.info('✅ Reference post-processing completed', {
          referenceId: reference.id
        });
      } catch (error) {
        logger.error('❌ Error post-processing reference', {
          referenceId: reference.id,
          error
        });
      }
    }
  }
}
