import UserRole, { UserRoleDocument } from '@/models/entities/UserRole';
import { Service } from '@c-visitor/framework';

/**
 * Repository for UserRole entity
 */
@Service()
export class UserRoleRepository {
  /**
   * Find a user role by ID
   * @param id UserRole ID
   * @returns UserRole or null if not found
   */
  async findById(id: string): Promise<UserRoleDocument | null> {
    return UserRole.findById(id).exec();
  }

  /**
   * Find user roles by user ID
   * @param userId User ID
   * @returns Array of user roles for the specified user
   */
  async findByUserId(userId: string): Promise<UserRoleDocument[]> {
    return UserRole.find({ userId }).exec();
  }

  /**
   * Find user roles by role ID
   * @param roleId Role ID
   * @returns Array of user roles for the specified role
   */
  async findByRoleId(roleId: string): Promise<UserRoleDocument[]> {
    return UserRole.find({ roleId }).exec();
  }

  /**
   * Find a specific user role by user ID and role ID
   * @param userId User ID
   * @param roleId Role ID
   * @returns UserRole or null if not found
   */
  async findByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserRoleDocument | null> {
    return UserRole.findOne({ userId, roleId }).exec();
  }

  /**
   * Find all user roles
   * @param options Query options
   * @returns Array of user roles
   */
  async findAll(options: {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
  } = {}): Promise<UserRoleDocument[]> {
    const { limit, skip, sort } = options;
    let query = UserRole.find();

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new user role
   * @param userRoleData UserRole data
   * @returns Created user role
   */
  async create(userRoleData: Partial<UserRoleDocument>): Promise<UserRoleDocument> {
    const userRole = new UserRole(userRoleData);
    return userRole.save();
  }

  /**
   * Update a user role
   * @param id UserRole ID
   * @param userRoleData UserRole data to update
   * @returns Updated user role or null if not found
   */
  async update(
    id: string,
    userRoleData: Partial<UserRoleDocument>,
  ): Promise<UserRoleDocument | null> {
    return UserRole.findByIdAndUpdate(id, userRoleData, { new: true }).exec();
  }

  /**
   * Delete a user role
   * @param id UserRole ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await UserRole.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Delete user roles by user ID
   * @param userId User ID
   * @returns Number of deleted user roles
   */
  async deleteByUserId(userId: string): Promise<number> {
    const result = await UserRole.deleteMany({ userId }).exec();
    return result.deletedCount;
  }

  /**
   * Delete user roles by role ID
   * @param roleId Role ID
   * @returns Number of deleted user roles
   */
  async deleteByRoleId(roleId: string): Promise<number> {
    const result = await UserRole.deleteMany({ roleId }).exec();
    return result.deletedCount;
  }

  /**
   * Delete a specific user role by user ID and role ID
   * @param userId User ID
   * @param roleId Role ID
   * @returns True if deleted, false if not found
   */
  async deleteByUserIdAndRoleId(userId: string, roleId: string): Promise<boolean> {
    const result = await UserRole.deleteOne({ userId, roleId }).exec();
    return result.deletedCount > 0;
  }
}
