export interface SignInExecute {
  email: string;
  password: string;
}

export interface SignUpExecute {
  email: string;
  fullName: string;
  password: string;
}

export interface RefreshTokenExecute {
  refreshToken: string;
}

export interface VerifyEmailExecute {
  token: string;
}

export interface ForgotPasswordExecute {
  email: string;
}

export interface ResetPasswordExecute {
  token: string;
  password: string;
}

export interface AuthResponseExecute {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email: string;
    fullName: string;
    roles?: string[];
  };
}
