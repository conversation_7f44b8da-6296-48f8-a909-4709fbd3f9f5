import express from 'express';
import {
  ApplicationHost,
  RouterFactory,
  logger,
  Container,
} from '@c-visitor/framework';

import { environment, validateEnvironment } from './configs/environment';
import { initializeModels } from './models';
import { seedDatabase } from './configs/seed';
import { IdentityController } from './controllers/identity.controller';
import { RequestController } from './controllers/request.controller';
import { StorageController } from './controllers/storage.controller';
import { RecognitionController } from './controllers/recognition.controller';
import { SecureController } from './controllers/secure.controller';
import { Migrations } from './models/migrations';
import { CivamsMqttService } from './services/civams-mqtt.service';

import dotenv from 'dotenv';
import path from 'path';
import { initializeMinIO } from './configs/minio';

// Load environment variables from .env file
const currentDir = process.cwd();
const envPath = path.resolve(currentDir, 'apps/server/.env');
console.log(`Current working directory: ${currentDir}`);
console.log(`Loading environment from: ${envPath}`);
dotenv.config({ path: envPath });

// Create application host
const host = new ApplicationHost();
console.log(environment);

// Configure application
host.Configure((app) => {
  // Add JSON body parser middleware with configurable limit for file uploads
  app.UseMiddleware(express.json({
    limit: environment.MAX_REQUEST_SIZE,
    verify: (req: any, _res, buf) => {
      // Log request size for debugging
      if (req.url?.includes('/requests/create')) {
        logger.info(`Request size for ${req.url}: ${buf.length} bytes (${(buf.length / 1024 / 1024).toFixed(2)} MB)`);
      }
    }
  }));
  app.UseMiddleware(express.urlencoded({
    limit: environment.MAX_REQUEST_SIZE,
    extended: true
  }));

  // Configure CORS
  app.UseCors({
    origin:
      environment.NODE_ENV === 'development'
        ? '*'
        : environment.CORS_ALLOWED_ORIGINS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // Create router with registered controllers
  const router = RouterFactory.createRoutes(() => {
    Container.register(Migrations);

    // Register services
    Container.register(CivamsMqttService);

    // Register controllers
    Container.register(IdentityController);
    Container.register(RequestController);
    Container.register(StorageController);
    Container.register(RecognitionController);
    Container.register(SecureController);
  });

  // Add request logging middleware with timing
  app.UseRequestLogger({
    logger: logger,
    useColors: true,
    // Enable request/response body logging in development mode
    logRequestBody: environment.NODE_ENV === 'development',
    logResponseBody: false, // Set to true if you want to log response bodies
  });

  // Use the router as middleware
  app.UseMiddleware(router as any);

  // Add global error handling middleware (must be added after all other middleware)
  // We need to access the underlying Express app to add error handling middleware
  const expressApp = app.build();

  // Define an interface for errors to avoid using 'any'
  interface ErrorWithStatus extends Error {
    statusCode?: number;
    code?: string;
    details?: unknown[];
  }

  // Error handling middleware has a different signature than regular middleware
  (expressApp as any).use(
    (err: ErrorWithStatus, req: any, res: any, next: any) => {
      // Log the error with detailed information
      logger.error('Request error', {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        statusCode: err.statusCode || 500,
        errorCode: err.code || 'INTERNAL_SERVER_ERROR',
      });

      // If headers already sent, let Express handle it
      if (res.headersSent) {
        return next(err);
      }

      // Determine status code (default to 500 if not specified)
      const statusCode = err.statusCode || 500;

      // Send error response
      res.status(statusCode).json({
        success: false,
        message: err.message || 'An unexpected error occurred',
        code: err.code || 'INTERNAL_SERVER_ERROR',
        details: err.details || null,
      });
    },
  );
});

// Validate environment variables
validateEnvironment();

// Initialize models and start the server
const bootstrap = async () => {
  try {
    // Initialize MongoDB models
    await initializeModels();

    // Initialize MinIO buckets
    await initializeMinIO();

    // Seed database with default data
    await seedDatabase();

    // Run application
    await host.Run(environment.PORT);
   // logger.info(`${application.title} - ${application.description}`);
    logger.info(
      `Server running on ${environment.HOST}:${environment.PORT} in ${environment.NODE_ENV} mode`,
    );
  } catch (error) {
    if (error instanceof Error) {
      logger.error('Failed to start server', {
        error: error.message,
        stack: error.stack,
        name: error.name,
      });
    } else {
      logger.error('Failed to start server with unknown error', { error });
    }
    process.exit(1);
  }
};

logger.info(`Server is started on port ${environment.PORT}`);

// Debug breakpoint test
console.log('🚀 Starting C-Visitor Server...');

bootstrap();
