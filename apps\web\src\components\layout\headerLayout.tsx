import * as React from 'react';
import { cn } from '@/lib/utils';
import Logo from '@/components/icons/Logo';
import { NavUser } from '@/components/layout/nav-user';
interface HeaderLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  hideNavUser?: boolean;
}

export function HeaderLayout({
  className,
  title,
  hideNavUser = false,
  ...props
}: HeaderLayoutProps) {

  return (
    <header
      className={cn(
        'bg-background border-b h-16 w-full flex items-center px-4 relative',
        className,
      )}
      {...props}
    >
      <div className="flex justify-between items-center w-full">
        <div className="flex items-center gap-4">
          <Logo orientation="horizontal" />
          {title && (
            <div className="flex items-center">
              <div className="w-px h-6 bg-[#d0d3d6] mx-4" />
              <h1 className="text-lg font-semibold text-[#1f2329]">{title}</h1>
            </div>
          )}
        </div>
        <NavUser />
      </div>
    </header>
  );
}

export default HeaderLayout;
