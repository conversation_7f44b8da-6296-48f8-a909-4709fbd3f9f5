/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as AuthImport } from './routes/auth'
import { Route as AppImport } from './routes/app'
import { Route as AboutImport } from './routes/about'
import { Route as IndexImport } from './routes/index'
import { Route as AuthSignUpImport } from './routes/auth/sign-up'
import { Route as AuthSignInImport } from './routes/auth/sign-in'
import { Route as AppRequestsImport } from './routes/app/requests'
import { Route as AppCreateRequestImport } from './routes/app/create-request'
import { Route as AppRequestsIdImport } from './routes/app/requests/$id'
import { Route as AppCreateRequestCreateRequestSuccessfullyImport } from './routes/app/createRequest/createRequestSuccessfully'
import { Route as AppCreateRequestAddVisitorDialogImport } from './routes/app/createRequest/addVisitorDialog'

// Create/Update Routes

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const AppRoute = AppImport.update({
  id: '/app',
  path: '/app',
  getParentRoute: () => rootRoute,
} as any)

const AboutRoute = AboutImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthSignUpRoute = AuthSignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => AuthRoute,
} as any)

const AuthSignInRoute = AuthSignInImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => AuthRoute,
} as any)

const AppRequestsRoute = AppRequestsImport.update({
  id: '/requests',
  path: '/requests',
  getParentRoute: () => AppRoute,
} as any)

const AppCreateRequestRoute = AppCreateRequestImport.update({
  id: '/create-request',
  path: '/create-request',
  getParentRoute: () => AppRoute,
} as any)

const AppRequestsIdRoute = AppRequestsIdImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => AppRequestsRoute,
} as any)

const AppCreateRequestCreateRequestSuccessfullyRoute =
  AppCreateRequestCreateRequestSuccessfullyImport.update({
    id: '/createRequest/createRequestSuccessfully',
    path: '/createRequest/createRequestSuccessfully',
    getParentRoute: () => AppRoute,
  } as any)

const AppCreateRequestAddVisitorDialogRoute =
  AppCreateRequestAddVisitorDialogImport.update({
    id: '/createRequest/addVisitorDialog',
    path: '/createRequest/addVisitorDialog',
    getParentRoute: () => AppRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutImport
      parentRoute: typeof rootRoute
    }
    '/app': {
      id: '/app'
      path: '/app'
      fullPath: '/app'
      preLoaderRoute: typeof AppImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/app/create-request': {
      id: '/app/create-request'
      path: '/create-request'
      fullPath: '/app/create-request'
      preLoaderRoute: typeof AppCreateRequestImport
      parentRoute: typeof AppImport
    }
    '/app/requests': {
      id: '/app/requests'
      path: '/requests'
      fullPath: '/app/requests'
      preLoaderRoute: typeof AppRequestsImport
      parentRoute: typeof AppImport
    }
    '/auth/sign-in': {
      id: '/auth/sign-in'
      path: '/sign-in'
      fullPath: '/auth/sign-in'
      preLoaderRoute: typeof AuthSignInImport
      parentRoute: typeof AuthImport
    }
    '/auth/sign-up': {
      id: '/auth/sign-up'
      path: '/sign-up'
      fullPath: '/auth/sign-up'
      preLoaderRoute: typeof AuthSignUpImport
      parentRoute: typeof AuthImport
    }
    '/app/createRequest/addVisitorDialog': {
      id: '/app/createRequest/addVisitorDialog'
      path: '/createRequest/addVisitorDialog'
      fullPath: '/app/createRequest/addVisitorDialog'
      preLoaderRoute: typeof AppCreateRequestAddVisitorDialogImport
      parentRoute: typeof AppImport
    }
    '/app/createRequest/createRequestSuccessfully': {
      id: '/app/createRequest/createRequestSuccessfully'
      path: '/createRequest/createRequestSuccessfully'
      fullPath: '/app/createRequest/createRequestSuccessfully'
      preLoaderRoute: typeof AppCreateRequestCreateRequestSuccessfullyImport
      parentRoute: typeof AppImport
    }
    '/app/requests/$id': {
      id: '/app/requests/$id'
      path: '/$id'
      fullPath: '/app/requests/$id'
      preLoaderRoute: typeof AppRequestsIdImport
      parentRoute: typeof AppRequestsImport
    }
  }
}

// Create and export the route tree

interface AppRequestsRouteChildren {
  AppRequestsIdRoute: typeof AppRequestsIdRoute
}

const AppRequestsRouteChildren: AppRequestsRouteChildren = {
  AppRequestsIdRoute: AppRequestsIdRoute,
}

const AppRequestsRouteWithChildren = AppRequestsRoute._addFileChildren(
  AppRequestsRouteChildren,
)

interface AppRouteChildren {
  AppCreateRequestRoute: typeof AppCreateRequestRoute
  AppRequestsRoute: typeof AppRequestsRouteWithChildren
  AppCreateRequestAddVisitorDialogRoute: typeof AppCreateRequestAddVisitorDialogRoute
  AppCreateRequestCreateRequestSuccessfullyRoute: typeof AppCreateRequestCreateRequestSuccessfullyRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppCreateRequestRoute: AppCreateRequestRoute,
  AppRequestsRoute: AppRequestsRouteWithChildren,
  AppCreateRequestAddVisitorDialogRoute: AppCreateRequestAddVisitorDialogRoute,
  AppCreateRequestCreateRequestSuccessfullyRoute:
    AppCreateRequestCreateRequestSuccessfullyRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

interface AuthRouteChildren {
  AuthSignInRoute: typeof AuthSignInRoute
  AuthSignUpRoute: typeof AuthSignUpRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthSignInRoute: AuthSignInRoute,
  AuthSignUpRoute: AuthSignUpRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/app': typeof AppRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/app/create-request': typeof AppCreateRequestRoute
  '/app/requests': typeof AppRequestsRouteWithChildren
  '/auth/sign-in': typeof AuthSignInRoute
  '/auth/sign-up': typeof AuthSignUpRoute
  '/app/createRequest/addVisitorDialog': typeof AppCreateRequestAddVisitorDialogRoute
  '/app/createRequest/createRequestSuccessfully': typeof AppCreateRequestCreateRequestSuccessfullyRoute
  '/app/requests/$id': typeof AppRequestsIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/app': typeof AppRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/app/create-request': typeof AppCreateRequestRoute
  '/app/requests': typeof AppRequestsRouteWithChildren
  '/auth/sign-in': typeof AuthSignInRoute
  '/auth/sign-up': typeof AuthSignUpRoute
  '/app/createRequest/addVisitorDialog': typeof AppCreateRequestAddVisitorDialogRoute
  '/app/createRequest/createRequestSuccessfully': typeof AppCreateRequestCreateRequestSuccessfullyRoute
  '/app/requests/$id': typeof AppRequestsIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/app': typeof AppRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/dashboard': typeof DashboardRoute
  '/app/create-request': typeof AppCreateRequestRoute
  '/app/requests': typeof AppRequestsRouteWithChildren
  '/auth/sign-in': typeof AuthSignInRoute
  '/auth/sign-up': typeof AuthSignUpRoute
  '/app/createRequest/addVisitorDialog': typeof AppCreateRequestAddVisitorDialogRoute
  '/app/createRequest/createRequestSuccessfully': typeof AppCreateRequestCreateRequestSuccessfullyRoute
  '/app/requests/$id': typeof AppRequestsIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/app'
    | '/auth'
    | '/dashboard'
    | '/app/create-request'
    | '/app/requests'
    | '/auth/sign-in'
    | '/auth/sign-up'
    | '/app/createRequest/addVisitorDialog'
    | '/app/createRequest/createRequestSuccessfully'
    | '/app/requests/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/app'
    | '/auth'
    | '/dashboard'
    | '/app/create-request'
    | '/app/requests'
    | '/auth/sign-in'
    | '/auth/sign-up'
    | '/app/createRequest/addVisitorDialog'
    | '/app/createRequest/createRequestSuccessfully'
    | '/app/requests/$id'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/app'
    | '/auth'
    | '/dashboard'
    | '/app/create-request'
    | '/app/requests'
    | '/auth/sign-in'
    | '/auth/sign-up'
    | '/app/createRequest/addVisitorDialog'
    | '/app/createRequest/createRequestSuccessfully'
    | '/app/requests/$id'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  AppRoute: typeof AppRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
  DashboardRoute: typeof DashboardRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  AppRoute: AppRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
  DashboardRoute: DashboardRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/about",
        "/app",
        "/auth",
        "/dashboard"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/about": {
      "filePath": "about.tsx"
    },
    "/app": {
      "filePath": "app.tsx",
      "children": [
        "/app/create-request",
        "/app/requests",
        "/app/createRequest/addVisitorDialog",
        "/app/createRequest/createRequestSuccessfully"
      ]
    },
    "/auth": {
      "filePath": "auth.tsx",
      "children": [
        "/auth/sign-in",
        "/auth/sign-up"
      ]
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/app/create-request": {
      "filePath": "app/create-request.tsx",
      "parent": "/app"
    },
    "/app/requests": {
      "filePath": "app/requests.tsx",
      "parent": "/app",
      "children": [
        "/app/requests/$id"
      ]
    },
    "/auth/sign-in": {
      "filePath": "auth/sign-in.tsx",
      "parent": "/auth"
    },
    "/auth/sign-up": {
      "filePath": "auth/sign-up.tsx",
      "parent": "/auth"
    },
    "/app/createRequest/addVisitorDialog": {
      "filePath": "app/createRequest/addVisitorDialog.tsx",
      "parent": "/app"
    },
    "/app/createRequest/createRequestSuccessfully": {
      "filePath": "app/createRequest/createRequestSuccessfully.tsx",
      "parent": "/app"
    },
    "/app/requests/$id": {
      "filePath": "app/requests/$id.tsx",
      "parent": "/app/requests"
    }
  }
}
ROUTE_MANIFEST_END */
