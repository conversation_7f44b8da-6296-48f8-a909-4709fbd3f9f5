import File, { FileDocument } from '@/models/entities/File';
import { Service } from '@c-visitor/framework';

/**
 * Repository for File entity
 */
@Service()
export class FileRepository {
  /**
   * Find a file by ID
   * @param id File ID
   * @returns File or null if not found
   */
  async findById(id: string): Promise<FileDocument | null> {
    return File.findById(id).exec();
  }

  /**
   * Find a file by filename
   * @param filename Filename
   * @returns File or null if not found
   */
  async findByFilename(filename: string): Promise<FileDocument | null> {
    return File.findOne({ filename }).exec();
  }

  /**
   * Find files by bucket
   * @param bucket Bucket name
   * @returns Array of files in the specified bucket
   */
  async findByBucket(bucket: string): Promise<FileDocument[]> {
    return File.find({ bucket }).exec();
  }

  /**
   * Find files by uploader
   * @param uploadedBy User ID who uploaded the files
   * @returns Array of files uploaded by the specified user
   */
  async findByUploadedBy(uploadedBy: string): Promise<FileDocument[]> {
    return File.find({ uploadedBy }).exec();
  }

  /**
   * Find files by MIME type
   * @param mimeType MIME type
   * @returns Array of files with the specified MIME type
   */
  async findByMimeType(mimeType: string): Promise<FileDocument[]> {
    return File.find({ mimeType }).exec();
  }

  /**
   * Find all files
   * @param options Query options
   * @returns Array of files
   */
  async findAll(options: {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
  } = {}): Promise<FileDocument[]> {
    const { limit, skip, sort } = options;
    let query = File.find();

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new file
   * @param fileData File data
   * @returns Created file
   */
  async create(fileData: Partial<FileDocument>): Promise<FileDocument> {
    const file = new File(fileData);
    return file.save();
  }

  /**
   * Update a file
   * @param id File ID
   * @param fileData File data to update
   * @returns Updated file or null if not found
   */
  async update(
    id: string,
    fileData: Partial<FileDocument>,
  ): Promise<FileDocument | null> {
    return File.findByIdAndUpdate(id, fileData, { new: true }).exec();
  }

  /**
   * Delete a file
   * @param id File ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await File.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Delete files by bucket
   * @param bucket Bucket name
   * @returns Number of deleted files
   */
  async deleteByBucket(bucket: string): Promise<number> {
    const result = await File.deleteMany({ bucket }).exec();
    return result.deletedCount;
  }
}
