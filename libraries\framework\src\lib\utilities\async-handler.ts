import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * <PERSON><PERSON><PERSON> bọc (wrapper) gi<PERSON><PERSON> x<PERSON> lý async middleware và route handler
 * @param handler <PERSON><PERSON><PERSON> x<PERSON> lý request có thể là async
 * @returns Một RequestHandler có bắt lỗi tự động
 */
export function asyncHandler<
  P extends Record<string, string> = Record<string, string>, // Params (req.params)
  ResBody = unknown, // Dữ liệu trả về từ API (res.json)
  ReqBody = unknown, // Dữ liệu nhận vào từ request body (req.body)
  ReqQuery extends Record<string, string> = Record<string, string>, // Query params (req.query)
>(
  handler: Request<PERSON><PERSON><PERSON><P, ResBody, ReqBody, ReqQuery>,
): RequestHandler<P, ResBody, ReqBody, ReqQuery> {
  return (
    request: Request<P, ResBody, ReqBody, ReqQuery>,
    response: Response<ResBody>,
    next: NextFunction,
  ) => {
    Promise.resolve(handler(request, response, next)).catch(next);
  };
}
