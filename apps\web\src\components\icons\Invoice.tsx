import React from 'react';
import { cn } from '@/lib/utils';

interface InvoiceIconProps extends React.SVGProps<SVGSVGElement> {
    className?: string;
    color?: string;
}

export const InvoiceIcon: React.FC<InvoiceIconProps> = ({
    className,
    color = "#141B34",
    ...props
}) => {
    return (
        <svg
            width={48}
            height={48}
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={cn(className)}
            preserveAspectRatio="none"
            {...props}
        >
            <path
                d="M2 35.2916V14.1085C2 8.40051 2 5.5465 3.75736 3.77325C5.51472 2 8.34315 2 14 2H22C27.6569 2 30.4853 2 32.2426 3.77325C34 5.5465 34 8.40051 34 14.1085V35.2916C34 38.3149 34 39.8266 33.0761 40.4217C31.5663 41.3941 29.2321 39.3549 28.0583 38.6146C27.0883 38.0029 26.6033 37.697 26.0651 37.6793C25.4834 37.6602 24.9899 37.9536 23.9417 38.6146L20.12 41.0248C19.0891 41.6749 18.5736 42 18 42C17.4264 42 16.9109 41.6749 15.88 41.0248L12.0583 38.6146C11.0883 38.0029 10.6033 37.697 10.0651 37.6793C9.48344 37.6602 8.98985 37.9536 7.94175 38.6146C6.7679 39.3549 4.43375 41.3941 2.92391 40.4217C2 39.8266 2 38.3149 2 35.2916Z"
                stroke="#99C0E7"
                strokeWidth="3.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M26 10L10 10"
                stroke="#99C0E7"
                strokeWidth="3.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M14 18H10"
                stroke="#99C0E7"
                strokeWidth="3.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M23 17.75C21.3431 17.75 20 18.9253 20 20.375C20 21.8247 21.3431 23 23 23C24.6569 23 26 24.1753 26 25.625C26 27.0747 24.6569 28.25 23 28.25M23 17.75C24.3062 17.75 25.4175 18.4805 25.8293 19.5M23 17.75V16M23 28.25C21.6938 28.25 20.5825 27.5195 20.1707 26.5M23 28.25V30"
                stroke="#99C0E7"
                strokeWidth="3.5"
                strokeLinecap="round"
            />
        </svg>
    );
};

export default InvoiceIcon;
