import React from 'react';
import { cn } from '@/lib/utils';

interface LocationIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const LocationIcon: React.FC<LocationIconProps> = ({
  className,
  color = '#141B34',
  ...props
}) => {
  return (
    <svg
      width={14}
      height={14}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
      preserveAspectRatio="none"
    >
      <path
        d="M7.94369 12.464C7.6907 12.7009 7.35256 12.8333 7.00065 12.8333C6.64874 12.8333 6.31061 12.7009 6.05762 12.464C3.74093 10.2818 0.636278 7.844 2.15032 4.30476C2.96895 2.39113 4.93403 1.16663 7.00065 1.16663C9.06727 1.16663 11.0323 2.39113 11.851 4.30476C13.3631 7.83953 10.2661 10.2893 7.94369 12.464Z"
        stroke="#73787E"
        strokeWidth="1.5"
      />
      <path
        d="M9.04134 6.41667C9.04134 7.54425 8.12726 8.45833 6.99967 8.45833C5.87209 8.45833 4.95801 7.54425 4.95801 6.41667C4.95801 5.28909 5.87209 4.375 6.99967 4.375C8.12726 4.375 9.04134 5.28909 9.04134 6.41667Z"
        stroke="#73787E"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default LocationIcon;
