/**
 * Test utility to verify auto-refresh functionality
 * This is for development/testing purposes only
 */

import { isTokenExpiringSoon, isTokenValid } from './token-validator';

/**
 * Create a test JWT token with custom expiry time
 * @param expiresInSeconds Number of seconds until token expires
 * @returns Base64 encoded JWT token (for testing only)
 */
export const createTestToken = (expiresInSeconds: number): string => {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    sub: 'test-user-id',
    email: '<EMAIL>',
    type: 'access',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + expiresInSeconds
  };

  // Create fake JWT (for testing only - not cryptographically secure)
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const signature = 'fake-signature-for-testing';

  return `${encodedHeader}.${encodedPayload}.${signature}`;
};

/**
 * Test the auto-refresh logic
 */
export const testAutoRefresh = () => {
  console.log('🧪 Testing Auto-Refresh Functionality');

  // Test 1: Token expiring in 10 minutes (should not refresh)
  const token10min = createTestToken(10 * 60);
  console.log('Token expiring in 10 minutes:', {
    isValid: isTokenValid(token10min),
    isExpiringSoon: isTokenExpiringSoon(token10min, 5),
    shouldRefresh: isTokenExpiringSoon(token10min, 5)
  });

  // Test 2: Token expiring in 3 minutes (should refresh)
  const token3min = createTestToken(3 * 60);
  console.log('Token expiring in 3 minutes:', {
    isValid: isTokenValid(token3min),
    isExpiringSoon: isTokenExpiringSoon(token3min, 5),
    shouldRefresh: isTokenExpiringSoon(token3min, 5)
  });

  // Test 3: Expired token
  const expiredToken = createTestToken(-60);
  console.log('Expired token:', {
    isValid: isTokenValid(expiredToken),
    isExpiringSoon: isTokenExpiringSoon(expiredToken, 5),
    shouldRefresh: isTokenExpiringSoon(expiredToken, 5)
  });

  console.log('✅ Auto-refresh test completed');
};

/**
 * Simulate token refresh scenario
 * This function temporarily replaces the auth token to test refresh behavior
 */
export const simulateTokenRefresh = () => {
  console.log('🔄 Simulating token refresh scenario...');

  // Save current token
  const originalToken = localStorage.getItem('auth_token');

  // Set a token that expires in 2 minutes
  const shortLivedToken = createTestToken(2 * 60);
  localStorage.setItem('auth_token', shortLivedToken);

  console.log('Set short-lived token (expires in 2 minutes)');
  console.log('Token should be refreshed on next API call');

  // Restore original token after 5 seconds
  setTimeout(() => {
    if (originalToken) {
      localStorage.setItem('auth_token', originalToken);
    } else {
      localStorage.removeItem('auth_token');
    }
    console.log('Restored original token');
  }, 5000);
};

// Export for console testing
if (typeof window !== 'undefined') {
  // Make functions globally available
  (window as any).testAutoRefresh = testAutoRefresh;
  (window as any).simulateTokenRefresh = simulateTokenRefresh;
  (window as any).createTestToken = createTestToken;

  // Also import token validator functions
  import('./token-validator').then(module => {
    (window as any).isTokenValid = module.isTokenValid;
    (window as any).isTokenExpiringSoon = module.isTokenExpiringSoon;
    (window as any).validateTokenSync = module.validateTokenSync;
  });

  console.log('🔧 Auto-refresh test functions available:');
  console.log('- testAutoRefresh(): Test token expiry logic');
  console.log('- simulateTokenRefresh(): Simulate refresh scenario');
  console.log('- createTestToken(seconds): Create test token');
  console.log('- isTokenValid(token): Check if token is valid');
  console.log('- isTokenExpiringSoon(token, minutes): Check if expiring soon');
}
