import * as React from 'react';
import { cn } from '@/lib/utils';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Link, useLocation } from '@tanstack/react-router';
import { ChevronRightIcon, ChevronDownIcon } from 'lucide-react';
import {
  BuildingIcon,
  CustomerIcon,
  ServiceIcon,
  FinanceIcon,
  VehicleControlIcon,
  SupportRequestIcon,
  CollapseNavIcon,
} from '@/components/icons';
interface SiderLayoutProps extends React.HTMLAttributes<HTMLDivElement> {}

export function SiderLayout({ className, ...props }: SiderLayoutProps) {
  const { state } = useSidebar();
  const isCollapsed = state === 'collapsed';
  const location = useLocation();
  const pathname = location.pathname;

  // Màu dùng chung
  const COLOR_ACTIVE = 'text-[#008FD3]';
  const COLOR_BG_ACTIVE = 'bg-[#008FD3]/10';
  const COLOR_INACTIVE = 'text-[#1F2329]';

  interface ItemSider {
    title: string;
    url: string;
    icon?: any;
    children?: ItemSider[];
  }

  const items: ItemSider[] = [
    {
      title: 'Dịch vụ',
      url: '/app/requests',
      icon: ServiceIcon,
    },
  ];

  return (
    <Sidebar
      collapsible="icon"
      className={cn(
        'h-[calc(100vh-4rem)] flex flex-col overflow-hidden relative [&_[data-sidebar=sidebar]]:rounded-none [&_[data-sidebar=sidebar]]:border-0 [&_[data-sidebar=sidebar]]:shadow-none',
        className,
      )}
      {...props}
    >
      <SidebarContent className="bg-background flex-1 overflow-y-auto">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => {
                const hasChildren = !!item.children?.length;
                const isActiveGroup = hasChildren
                  ? item.children?.some((child) => child.url === pathname)
                  : item.url === pathname;

                const [open, setOpen] = React.useState(isActiveGroup);

                React.useEffect(() => {
                  if (hasChildren) {
                    const shouldOpen = item.children?.some(
                      (child) => child.url === pathname,
                    );
                    setOpen(shouldOpen);
                  }
                }, [pathname]);

                return hasChildren ? (
                  <Collapsible
                    key={item.title}
                    open={open}
                    onOpenChange={setOpen}
                    className="group/collapsible"
                  >
                    <SidebarMenuItem>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          className={cn(
                            'flex items-center justify-between px-4 py-2 w-full rounded group font-semibold text-[14px]',
                            isActiveGroup
                              ? `${COLOR_ACTIVE} hover:text-${COLOR_ACTIVE}`
                              : `${COLOR_INACTIVE} hover:bg-gray-100`,
                          )}
                        >
                          <div className="flex items-center gap-2">
                            {item.icon && (
                              <item.icon
                                className={cn(
                                  'w-4 h-4',
                                  isActiveGroup
                                    ? 'text-[#008FD3]'
                                    : 'text-[#1F2329]',
                                )}
                                color={isActiveGroup ? '#008FD3' : '#1F2329'}
                              />
                            )}
                            <span
                              className={cn(
                                isActiveGroup
                                  ? `${COLOR_ACTIVE} hover:text-${COLOR_ACTIVE}`
                                  : `${COLOR_INACTIVE} hover:bg-gray-100`,
                              )}
                            >
                              {item.title}
                            </span>
                          </div>
                          <ChevronDownIcon
                            size={16}
                            className={cn(
                              'transition-transform',
                              open ? 'rotate-180' : 'rotate-0',
                              isActiveGroup
                                ? 'text-[#008FD3]'
                                : 'text-[#1F2329]',
                            )}
                          />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                    </SidebarMenuItem>

                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.children?.map((sub) => {
                          const isSubActive = pathname === sub.url;
                          return (
                            <SidebarMenuSubItem key={sub.title}>
                              <Link
                                to={sub.url}
                                className={cn(
                                  'block px-4 py-2 ml-6 w-full rounded hover:bg-gray-100 font-semibold text-[14px]',
                                  isSubActive &&
                                    `${COLOR_BG_ACTIVE} ${COLOR_ACTIVE}`,
                                )}
                              >
                                {sub.title}
                              </Link>
                            </SidebarMenuSubItem>
                          );
                        })}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </Collapsible>
                ) : (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <Link
                        to={item.url}
                        className={cn(
                          'flex items-center gap-2 px-4 py-2 w-full rounded group font-semibold text-[14px]',
                          isActiveGroup
                            ? `${COLOR_BG_ACTIVE} ${COLOR_ACTIVE}`
                            : `${COLOR_INACTIVE} hover:bg-gray-100`,
                        )}
                      >
                        {item.icon && (
                          <item.icon
                            className={cn(
                              'w-4 h-4',
                              isActiveGroup
                                ? 'text-[#008FD3]'
                                : 'text-[#1F2329]',
                            )}
                            color={isActiveGroup ? '#008FD3' : '#1F2329'}
                          />
                        )}
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <div className="border-t p-2 flex justify-center flex-shrink-0">
        <SidebarTrigger className="p-2 rounded-full hover:bg-gray-100 flex items-center gap-2">
          {isCollapsed ? (
            <>
              <ChevronRightIcon />
              <span className="sr-only">Hiện điều hướng</span>
            </>
          ) : (
            <>
              <CollapseNavIcon />
              <span className="text-sm font-medium">Ẩn điều hướng</span>
            </>
          )}
        </SidebarTrigger>
      </div>
    </Sidebar>
  );
}

export default SiderLayout;
