import Reference, { ReferenceDocument } from '@/models/entities/Reference';
import { Service } from '@c-visitor/framework';

/**
 * Repository for Reference entity
 */
@Service()
export class ReferenceRepository {
  /**
   * Find a reference by ID
   * @param id Reference ID
   * @returns Reference or null if not found
   */
  async findById(id: string): Promise<ReferenceDocument | null> {
    return Reference.findById(id).exec();
  }

  /**
   * Find references by request ID
   * @param requestId Request ID
   * @returns Array of references for the specified request
   */
  async findByRequestId(requestId: string): Promise<ReferenceDocument[]> {
    return Reference.find({ requestId }).exec();
  }

  /**
   * Find references by email
   * @param email Email address
   * @returns Array of references with the specified email
   */
  async findByEmail(email: string): Promise<ReferenceDocument[]> {
    return Reference.find({ email }).exec();
  }

  /**
   * Find references by phone
   * @param phone Phone number
   * @returns Array of references with the specified phone
   */
  async findByPhone(phone: string): Promise<ReferenceDocument[]> {
    return Reference.find({ phone }).exec();
  }

  /**
   * Find references by card ID number
   * @param cardIdNumber Card ID number
   * @returns Array of references with the specified card ID number
   */
  async findByCardIdNumber(cardIdNumber: string): Promise<ReferenceDocument[]> {
    return Reference.find({ cardIdNumber }).exec();
  }

  /**
   * Find references by unit
   * @param unit Unit name
   * @returns Array of references with the specified unit
   */
  async findByUnit(unit: string): Promise<ReferenceDocument[]> {
    return Reference.find({ unit }).exec();
  }

  /**
   * Find references by unit ID
   * @param unitId Unit ID
   * @returns Array of references with the specified unit ID
   */
  async findByUnitId(unitId: string): Promise<ReferenceDocument[]> {
    return Reference.find({ unitId }).exec();
  }

  /**
   * Find all references
   * @param options Query options
   * @returns Array of references
   */
  async findAll(options: {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
  } = {}): Promise<ReferenceDocument[]> {
    const { limit, skip, sort } = options;
    let query = Reference.find();

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new reference
   * @param referenceData Reference data
   * @returns Created reference
   */
  async create(referenceData: Partial<ReferenceDocument>): Promise<ReferenceDocument> {
    const reference = new Reference(referenceData);
    return reference.save();
  }

  /**
   * Update a reference
   * @param id Reference ID
   * @param referenceData Reference data to update
   * @returns Updated reference or null if not found
   */
  async update(
    id: string,
    referenceData: Partial<ReferenceDocument>,
  ): Promise<ReferenceDocument | null> {
    return Reference.findByIdAndUpdate(id, referenceData, { new: true }).exec();
  }

  /**
   * Delete a reference
   * @param id Reference ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await Reference.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Delete references by request ID
   * @param requestId Request ID
   * @returns Number of deleted references
   */
  async deleteByRequestId(requestId: string): Promise<number> {
    const result = await Reference.deleteMany({ requestId }).exec();
    return result.deletedCount;
  }
}
