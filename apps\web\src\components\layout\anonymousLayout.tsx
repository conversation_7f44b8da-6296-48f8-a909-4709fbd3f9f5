import * as React from 'react';
import { Toaster } from 'sonner';

interface AnonymousLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function AnonymousLayout({ children, className, ...props }: AnonymousLayoutProps) {
  return (
    <>
      <Toaster position="top-right" />
      <div className={className} {...props}>
        {children}
      </div>
    </>

  );
}

export default AnonymousLayout;
