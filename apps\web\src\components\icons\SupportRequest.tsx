import React from 'react';
import { cn } from '@/lib/utils';

interface SupportRequestIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const SupportRequestIcon: React.FC<SupportRequestIconProps> = ({ 
  className, 
  color = "#141B34", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M11.25 9C11.25 8.17157 11.9216 7.5 12.75 7.5C14.4069 7.5 15.75 8.84315 15.75 10.5C15.75 12.1569 14.4069 13.5 12.75 13.5C11.9216 13.5 11.25 12.8284 11.25 12V9Z"
        stroke={color}
        strokeWidth="1.5"
      />
      <path
        d="M6.75 9C6.75 8.17157 6.07843 7.5 5.25 7.5C3.59315 7.5 2.25 8.84315 2.25 10.5C2.25 12.1569 3.59315 13.5 5.25 13.5C6.07843 13.5 6.75 12.8284 6.75 12V9Z"
        stroke={color}
        strokeWidth="1.5"
      />
      <path
        d="M2.25 10.5V8.25C2.25 4.52208 5.27208 1.5 9 1.5C12.7279 1.5 15.75 4.52208 15.75 8.25V11.8846C15.75 13.3909 15.75 14.144 15.4857 14.7312C15.1849 15.3997 14.6497 15.9349 13.9812 16.2357C13.394 16.5 12.6409 16.5 11.1346 16.5H9"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SupportRequestIcon;
