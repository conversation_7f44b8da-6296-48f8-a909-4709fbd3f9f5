import { Dialog, DialogContent } from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import React from 'react'

interface FormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  children: React.ReactNode
  footer?: React.ReactNode
  className?: string
}

interface FormDialogFieldProps {
  label: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

interface FormDialogActionsProps {
  children: React.ReactNode
  className?: string
}

interface FormDialogButtonProps {
  variant?: 'primary' | 'secondary'
  onClick?: () => void
  disabled?: boolean
  children: React.ReactNode
  className?: string
}

// Main FormDialog component
export function FormDialog({
  open,
  onOpenChange,
  title,
  children,
  footer,
  className,
}: FormDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn(
          'flex flex-col rounded-lg bg-white shadow-lg',
          'max-h-[90vh] overflow-hidden p-0',
          'sm:max-w-none', // Override default sm:max-w-lg
          className,
        )}
      >
        {/* Header */}
        <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 px-4 py-3 border-t-0 border-r-0 border-b border-l-0 border-[#d0d3d6]">
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative overflow-hidden gap-4">
            <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#1f2329]">
              {title}
            </p>
          </div>
        </div>

        {/* Content */}
        <form className="flex flex-col justify-start items-start flex-1 px-0 overflow-y-auto w-full min-h-0 px-4">
          {children}
        </form>

        {/* Footer */}
        {footer && (
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 px-6 py-2 border-t border-[#d0d3d6]">
            {footer}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

// Field component for form inputs
export function FormDialogField({
  label,
  required = false,
  children,
  className,
}: FormDialogFieldProps) {
  return (
    <div
      className={cn(
        'flex flex-col justify-start items-start w-full gap-2',
        className,
      )}
    >
      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
          <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
            {label}
          </p>
          {required && (
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
              *
            </p>
          )}
        </div>
      </div>
      <div className="w-full">
        {children}
      </div>
    </div>
  )
}

// Actions container for buttons
export function FormDialogActions({
  children,
  className,
}: FormDialogActionsProps) {
  return (
    <div
      className={cn(
        'flex justify-end items-start self-stretch flex-grow-0 flex-shrink-0',
        className,
      )}
    >
      <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3">
        {children}
      </div>
    </div>
  )
}

// Button component with predefined styles
export function FormDialogButton({
  variant = 'secondary',
  onClick,
  disabled = false,
  children,
  className,
}: FormDialogButtonProps) {
  const baseClasses =
    'flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative px-4 py-1 rounded-md transition-colors cursor-pointer'

  const variantClasses = {
    primary:
      'bg-[#008fd3] border border-[#008fd3] hover:bg-[#008fd3]/90 disabled:bg-gray-300 disabled:border-gray-300',
    secondary:
      'bg-white border border-[#d0d3d6] hover:bg-gray-50 disabled:bg-gray-100',
  }

  const textClasses = {
    primary: 'text-[13.78125px] font-medium text-center text-white',
    secondary: 'text-[13.671875px] font-medium text-center text-[#1f2329]',
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        disabled && 'cursor-not-allowed opacity-50',
        className,
      )}
    >
      <p className={cn(textClasses[variant])}>{children}</p>
    </button>
  )
}

// Message component for confirmation text
export function FormDialogMessage({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div
      className={cn(
        'flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-0.5',
        className,
      )}
    >
      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
        <div className="text-sm text-left text-[#23262f]">{children}</div>
      </div>
    </div>
  )
}
