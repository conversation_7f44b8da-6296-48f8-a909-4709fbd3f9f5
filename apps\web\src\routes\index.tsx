import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'

export const Route = createFileRoute('/')({
  component: HomeComponent,
})

function HomeComponent() {
  const navigate = useNavigate()

  useEffect(() => {
    // Redirect based on authentication status
    const token = localStorage.getItem('auth_token')
    if (token) {
      navigate({ to: '/app/requests' })
    } else {
      navigate({ to: '/auth/sign-in' })
    }
  }, [navigate])

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600"><PERSON><PERSON> chuyển hướng...</p>
      </div>
    </div>
  )
}
