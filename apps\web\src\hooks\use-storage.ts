import { useState, useEffect } from 'react';
import { useApiMutation, useApiQuery } from './use-api-query';

// Types based on actual storage API
export interface UploadFileResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  bucket: string;
  uploadedBy: string;
}

export interface Base64ConvertResponse {
  filename: string;
  mimeType: string;
  size: number;
  base64: string;
}

export interface PublicUrlResponse {
  url: string;
}

// Generic localStorage hook
export function useLocalStorage<T>(key: string, initialValue: T) {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Get from local storage by key
      if (typeof window === 'undefined') {
        return initialValue;
      }
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error also return initialValue
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to local storage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Generic sessionStorage hook
export function useSessionStorage<T>(key: string, initialValue: T) {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Get from session storage by key
      if (typeof window === 'undefined') {
        return initialValue;
      }
      const item = window.sessionStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error also return initialValue
      console.error(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to sessionStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to session storage
      if (typeof window !== 'undefined') {
        window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.error(`Error setting sessionStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Storage API hooks
export function useStorage() {
  // Upload file
  const useUploadFile = () => {
    return useApiMutation<UploadFileResponse, FormData>('/api/storage/upload', 'POST', {
      meta: {
        successMessage: 'File đã được tải lên thành công',
      },
    });
  };

  // Convert file to base64
  const useConvertToBase64 = () => {
    return useApiMutation<Base64ConvertResponse, FormData>('/api/storage/base64', 'POST', {
      meta: {
        successMessage: 'File đã được chuyển đổi thành công',
      },
    });
  };

  // Get public URL
  const useGetPublicUrl = (id: string, bucket?: string) => {
    const queryParams: Record<string, any> = {};
    if (bucket) queryParams.bucket = bucket;

    const queryKey: string[] = ['storage', 'public-url', id];
    if (bucket) queryKey.push(bucket);

    return useApiQuery<PublicUrlResponse>(
      queryKey,
      `/api/storage/public-url/${id}`,
      queryParams,
      {
        enabled: !!id,
      }
    );
  };

  // Delete file
  const useDeleteFile = () => {
    return useApiMutation<void, { filename: string }>('/api/storage/delete', 'DELETE', {
      meta: {
        successMessage: 'File đã được xóa thành công',
      },
    });
  };

  return {
    useUploadFile,
    useConvertToBase64,
    useGetPublicUrl,
    useDeleteFile,
  };
}

// Specific storage hooks for visitor app
export function useVisitorFormData() {
  return useLocalStorage('visitor-form-data', {
    visitorName: '',
    visitorPhone: '',
    visitorEmail: '',
    visitorCompany: '',
    hostName: '',
    hostPhone: '',
    hostDepartment: '',
    visitPurpose: '',
    visitDate: '',
    visitTime: '',
    expectedDuration: undefined,
    notes: '',
  });
}

export function useUserPreferences() {
  return useLocalStorage('user-preferences', {
    theme: 'light',
    language: 'vi',
    pageSize: 10,
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
  });
}

export function useRecentSearches() {
  return useLocalStorage<string[]>('recent-searches', []);
}

export function useRecentHosts() {
  return useLocalStorage<Array<{ name: string; phone: string; department?: string }>>('recent-hosts', []);
}

// Hook to manage storage cleanup
export function useStorageCleanup() {
  const clearAllStorage = () => {
    if (typeof window !== 'undefined') {
      window.localStorage.clear();
      window.sessionStorage.clear();
    }
  };

  const clearVisitorData = () => {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('visitor-form-data');
      window.localStorage.removeItem('recent-searches');
      window.localStorage.removeItem('recent-hosts');
    }
  };

  const clearUserData = () => {
    if (typeof window !== 'undefined') {
      window.localStorage.removeItem('auth_token');
      window.localStorage.removeItem('user-preferences');
    }
  };

  return {
    clearAllStorage,
    clearVisitorData,
    clearUserData,
  };
}

// Hook to check storage availability
export function useStorageAvailable() {
  const [isLocalStorageAvailable, setIsLocalStorageAvailable] = useState(false);
  const [isSessionStorageAvailable, setIsSessionStorageAvailable] = useState(false);

  useEffect(() => {
    // Check localStorage availability
    try {
      if (typeof window !== 'undefined') {
        const test = '__storage_test__';
        window.localStorage.setItem(test, test);
        window.localStorage.removeItem(test);
        setIsLocalStorageAvailable(true);
      }
    } catch (e) {
      setIsLocalStorageAvailable(false);
    }

    // Check sessionStorage availability
    try {
      if (typeof window !== 'undefined') {
        const test = '__storage_test__';
        window.sessionStorage.setItem(test, test);
        window.sessionStorage.removeItem(test);
        setIsSessionStorageAvailable(true);
      }
    } catch (e) {
      setIsSessionStorageAvailable(false);
    }
  }, []);

  return {
    isLocalStorageAvailable,
    isSessionStorageAvailable,
  };
}
