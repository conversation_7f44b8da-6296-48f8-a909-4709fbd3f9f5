# Route Migration from cvisitor_old to c-visitor

## Migration Mapping

| Old Path (Next.js) | New Path (TanStack Router) | Status | Notes |
|---------------------|----------------------------|--------|-------|
| `/` | `/` | ✅ Created | Home page |
| `/app` | `/app` | ✅ Created | App layout |
| `/app/createRequest` | `/app/createRequest` | ✅ Created | Create request page |
| `/app/createRequest/addVisitorDialog` | `/app/createRequest/addVisitorDialog` | ✅ Created | Add visitor dialog |
| `/app/createRequest/createRequestSuccessfully` | `/app/createRequest/createRequestSuccessfully` | ✅ Created | Success page |
| `/app/requests` | `/app/requests` | ✅ Created | Requests list |
| `/app/requests/[id]` | `/app/requests/$id` | ✅ Created | Request detail (dynamic route) |
| `/auth` | `/auth` | ✅ Created | Auth layout |
| `/auth/sign-in/[[...sign-in]]` | `/auth/sign-in` | ✅ Created | Sign in page |
| `/auth/sign-up/[[...sign-up]]` | `/auth/sign-up` | ✅ Created | Sign up page |
| `/dashboard` | `/dashboard` | ✅ Created | Dashboard page |

## Files Created

### Route Files
- `c-visitor/apps/web/src/routes/index.tsx` - Home page
- `c-visitor/apps/web/src/routes/app.tsx` - App layout
- `c-visitor/apps/web/src/routes/app/createRequest.tsx` - Create request
- `c-visitor/apps/web/src/routes/app/createRequest/addVisitorDialog.tsx` - Add visitor dialog
- `c-visitor/apps/web/src/routes/app/createRequest/createRequestSuccessfully.tsx` - Success page
- `c-visitor/apps/web/src/routes/app/requests.tsx` - Requests list
- `c-visitor/apps/web/src/routes/app/requests/$id.tsx` - Request detail
- `c-visitor/apps/web/src/routes/auth.tsx` - Auth layout
- `c-visitor/apps/web/src/routes/auth/sign-in.tsx` - Sign in
- `c-visitor/apps/web/src/routes/auth/sign-up.tsx` - Sign up
- `c-visitor/apps/web/src/routes/dashboard.tsx` - Dashboard

## Source Files to Migrate Content From

### Layouts
- `cvisitor_old/apps/client/src/app/layout.tsx` → Root layout
- `cvisitor_old/apps/client/src/app/app/layout.tsx` → App layout
- `cvisitor_old/apps/client/src/app/auth/layout.tsx` → Auth layout

### Pages
- `cvisitor_old/apps/client/src/app/page.tsx` → Home page
- `cvisitor_old/apps/client/src/app/dashboard/page.tsx` → Dashboard
- `cvisitor_old/apps/client/src/app/app/createRequest/page.tsx` → Create request
- `cvisitor_old/apps/client/src/app/app/createRequest/addVisitorDialog/page.tsx` → Add visitor dialog
- `cvisitor_old/apps/client/src/app/app/createRequest/createRequestSuccessfully/page.tsx` → Success page
- `cvisitor_old/apps/client/src/app/app/requests/page.tsx` → Requests list
- `cvisitor_old/apps/client/src/app/app/requests/[id]/page.tsx` → Request detail
- `cvisitor_old/apps/client/src/app/auth/sign-in/[[...sign-in]]/page.tsx` → Sign in
- `cvisitor_old/apps/client/src/app/auth/sign-up/[[...sign-up]]/page.tsx` → Sign up

## Next Steps

1. **Content Migration**: Migrate the actual component content from old files to new route files
2. **Styling**: Migrate CSS/styling from `cvisitor_old/apps/client/src/app/globals.css`
3. **Providers**: Migrate providers from `cvisitor_old/apps/client/src/app/providers.tsx`
4. **Assets**: Copy favicon and other assets
5. **Dependencies**: Ensure all required dependencies are installed in the new project
6. **Testing**: Test all routes work correctly with TanStack Router

## TanStack Router Notes

- Dynamic routes use `$param` syntax instead of `[param]`
- Catch-all routes use `$` instead of `[[...param]]`
- Each route file exports a `Route` object created with `createFileRoute()`
- Layout routes use `Outlet` component to render child routes
- Route parameters are accessed via `Route.useParams()`
