import { upload } from '@/configs/multer'
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpDelete,
  HttpGet,
  HttpPost,
  UseMiddleware,
  type IHttpContext,
  Inject
} from '@c-visitor/framework'
import { FileService } from '@/services/file.service'
import fs from 'fs'

@Controller('/api/storage')
export class StorageController extends ControllerBase {
  constructor(@Inject(FileService) private readonly fileService: FileService) {
    super()
  }

  @HttpPost('/base64')
  @UseMiddleware(upload.single('file') as any)
  async saveBase64Image(@HttpContext() context: IHttpContext) {
    try {
      // Cast request to any to access the file property added by multer
      const file = (context.request as any).file
      if (!file) {
        return this.badRequest(context.response, 'No file uploaded')
      }

      // Read the file as a buffer
      const fileBuffer = fs.readFileSync(file.path)

      // Convert buffer to base64
      const base64Data = fileBuffer.toString('base64')

      // Determine MIME type from file
      const mimeType = file.mimetype || 'application/octet-stream'

      // Format as data URI
      const dataUri = `data:${mimeType};base64,${base64Data}`

      // Clean up the temporary file
      fs.unlinkSync(file.path)

      // Return the base64 data
      return this.success(
        context.response,
        {
          filename: file.originalname,
          mimeType: mimeType,
          size: file.size,
          base64: dataUri
        },
        'File converted to base64 successfully'
      )
    } catch (error) {
      console.error('Error converting file to base64:', error)
      return this.error(context.response, 'Failed to convert file to base64')
    }
  }

  @HttpPost('/upload')
  @UseMiddleware(upload.single('file') as any)
  async uploadFile(@HttpContext() context: IHttpContext) {
    // Cast request to any to access the file property added by multer
    const file = (context.request as any).file
    if (!file) {
      return this.badRequest(context.response, 'No file uploaded')
    }
    const result = await this.fileService.uploadFile(file)
    return this.success(context.response, result, 'File uploaded successfully')
  }

  @HttpGet('/download/:id')
  async getFile(@HttpContext() context: IHttpContext) {
    try {
      const { id } = (context.request as any).params
      const { stream, file } = await this.fileService.getFile(id)

      ;(context.response as any).setHeader('Content-Type', file.mimeType)
      ;(context.response as any).setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`)
      stream.pipe(context.response as any)
      return new Promise<void>((resolve) => {
        stream.on('end', () => resolve());
      });
    } catch (error) {
      if (error instanceof Error) {
        return this.notFound(context.response, error.message)
      }
      return this.error(context.response, 'An unexpected error occurred')
    }
  }

  @HttpGet('/stream/:id')
  async streamFile(@HttpContext() context: IHttpContext) {
    try {
      const { id } = (context.request as any).params
      const { stream, file } = await this.fileService.getFile(id)

      ;(context.response as any).setHeader('Content-Type', file.mimeType)
      ;(context.response as any).setHeader('Access-Control-Allow-Origin', '*')
      ;(context.response as any).setHeader('Access-Control-Allow-Credentials', 'true') // if cookies are needed

      stream.pipe(context.response as any)
      return new Promise<void>((resolve) => {
        stream.on('end', () => resolve());
      });
    } catch (error) {
      if (error instanceof Error) {
        return this.notFound(context.response, error.message)
      }
      return this.error(context.response, 'An unexpected error occurred')
    }
  }

  @HttpGet('/stream/filename/:filename')
  async streamFileByFilename(@HttpContext() context: IHttpContext) {
    try {
      const { filename } = (context.request as any).params
      const bucket = (context.request as any).query.bucket || 'images'

      const { stream, mimeType } = await this.fileService.getFileByFilename(filename, bucket)

      ;(context.response as any).setHeader('Content-Type', mimeType)
      ;(context.response as any).setHeader('Access-Control-Allow-Origin', '*')
      ;(context.response as any).setHeader('Access-Control-Allow-Credentials', 'true')
      ;(context.response as any).setHeader('Cache-Control', 'public, max-age=31536000') // Cache for 1 year

      stream.pipe(context.response as any)
      return new Promise<void>((resolve) => {
        stream.on('end', () => resolve());
      });
    } catch (error) {
      if (error instanceof Error) {
        return this.notFound(context.response, error.message)
      }
      return this.error(context.response, 'An unexpected error occurred')
    }
  }

  @HttpDelete('/delete/:filename')
  async deleteFile(@HttpContext() context: IHttpContext) {
    try {
      const { filename } = (context.request as any).params
      await this.fileService.deleteFile(filename)
      return (context.response as any).status(204).send()
    } catch (error) {
      if (error instanceof Error) {
        return this.notFound(context.response, error.message)
      }
      return this.error(context.response, 'An unexpected error occurred')
    }
  }

  @HttpGet('/public-url/:id')
  async getPublicUrl(@HttpContext() context: IHttpContext) {
    try {
      const { id } = (context.request as any).params
      const bucket = (context.request as any).query.bucket as string | undefined

      const publicUrl = await this.fileService.getPublicUrl(id, bucket)

      return this.success(context.response, { url: publicUrl }, 'Public URL generated successfully')
    } catch (error) {
      if (error instanceof Error) {
        return this.notFound(context.response, error.message)
      }
      return this.error(context.response, 'Failed to generate public URL')
    }
  }
}
