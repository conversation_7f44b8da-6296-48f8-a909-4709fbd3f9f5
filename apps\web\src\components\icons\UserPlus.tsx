import React from 'react';
import { cn } from '@/lib/utils';

interface UserPlusIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const UserPlusIcon: React.FC<UserPlusIconProps> = ({
  className,
  color = '#141B34',
  ...props
}) => {
  return (
    <svg
      width={50}
      height={50}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      className={cn('w-[13.5px] h-[15px]', className)}
    >
      <path
        d="M25 12.8125C27.0396 12.8125 29.0334 13.4173 30.7293 14.5505C32.4252 15.6836 33.747 17.2942 34.5275 19.1786C35.308 21.0629 35.5123 23.1364 35.1144 25.1369C34.7164 27.1373 33.7343 28.9748 32.292 30.417C30.8498 31.8593 29.0123 32.8414 27.0119 33.2393C25.0114 33.6373 22.9379 33.433 21.0536 32.6525C19.1692 31.872 17.5586 30.5502 16.4255 28.8543C15.2923 27.1584 14.6875 25.1646 14.6875 23.125C14.6875 20.39 15.774 17.7669 17.708 15.833C19.6419 13.899 22.265 12.8125 25 12.8125V12.8125ZM36.25 10H40V13.75C40 14.2473 40.1975 14.7242 40.5492 15.0758C40.9008 15.4275 41.3777 15.625 41.875 15.625C42.3723 15.625 42.8492 15.4275 43.2008 15.0758C43.5525 14.7242 43.75 14.2473 43.75 13.75V10H47.5C47.9973 10 48.4742 9.80245 48.8258 9.45082C49.1775 9.09919 49.375 8.62228 49.375 8.125C49.375 7.62772 49.1775 7.1508 48.8258 6.79917C48.4742 6.44754 47.9973 6.25 47.5 6.25H43.75V2.5C43.75 2.00272 43.5525 1.5258 43.2008 1.17417C42.8492 0.822543 42.3723 0.624999 41.875 0.624999C41.3777 0.624999 40.9008 0.822543 40.5492 1.17417C40.1975 1.5258 40 2.00272 40 2.5V6.25H36.25C35.7527 6.25 35.2758 6.44754 34.9242 6.79917C34.5725 7.1508 34.375 7.62772 34.375 8.125C34.375 8.62228 34.5725 9.09919 34.9242 9.45082C35.2758 9.80245 35.7527 10 36.25 10ZM46.9375 19.6094C46.6931 19.6436 46.4581 19.7263 46.2462 19.8527C46.0342 19.9791 45.8497 20.1465 45.7034 20.3452C45.5571 20.5439 45.452 20.7699 45.3943 21.0098C45.3365 21.2497 45.3273 21.4987 45.3672 21.7422C45.5354 22.82 45.6216 23.9091 45.625 25C45.6319 30.0469 43.7796 34.9196 40.4219 38.6875C38.9041 36.5103 36.9483 34.6742 34.6797 33.2969C32.075 35.7923 28.6072 37.1853 25 37.1853C21.3928 37.1853 17.925 35.7923 15.3203 33.2969C13.0517 34.6742 11.0959 36.5103 9.57813 38.6875C6.94097 35.7149 5.21869 32.0437 4.61843 28.1155C4.01817 24.1873 4.56548 20.1693 6.19454 16.5448C7.8236 12.9202 10.465 9.84347 13.8012 7.68451C17.1374 5.52554 21.0262 4.3763 25 4.375C26.0909 4.3784 27.18 4.46459 28.2578 4.63281C28.7438 4.69942 29.2366 4.5729 29.6304 4.28041C30.0242 3.98791 30.2877 3.55273 30.3644 3.06821C30.441 2.58368 30.3247 2.08841 30.0405 1.68863C29.7562 1.28884 29.3266 1.01638 28.8438 0.929686C27.5731 0.722514 26.2875 0.620605 25 0.624999C20.0885 0.626704 15.2921 2.11211 11.2393 4.88657C7.18647 7.66102 4.06635 11.5951 2.28778 16.1733C0.509215 20.7514 0.155176 25.7601 1.27206 30.5429C2.38894 35.3257 4.92464 39.6596 8.54688 42.9766L8.85156 43.2578C13.3076 47.1989 19.0511 49.3744 25 49.3744C30.9489 49.3744 36.6924 47.1989 41.1484 43.2578L41.4531 42.9766C43.9519 40.6964 45.9471 37.9198 47.3112 34.8243C48.6753 31.7288 49.3782 28.3827 49.375 25C49.3794 23.7125 49.2775 22.4269 49.0703 21.1562C49.0333 20.914 48.9485 20.6815 48.8209 20.4722C48.6933 20.2629 48.5255 20.0811 48.3271 19.9372C48.1287 19.7933 47.9037 19.6902 47.6652 19.634C47.4266 19.5777 47.1793 19.5693 46.9375 19.6094V19.6094Z"
        fill="#99C0E7"
      />
    </svg>
  );
};

export default UserPlusIcon;
