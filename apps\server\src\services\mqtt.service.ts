import { Service, logger } from '@c-visitor/framework';
import * as mqtt from 'mqtt';
import type {
  IClientOptions,
  MqttClient,
  ISubscriptionGrant,
  IPublishPacket,
  IClientSubscribeOptions,
  IClientPublishOptions,
} from 'mqtt';

/**
 * Configuration options for MQTT client
 */
export interface MqttConfig {
  /** MQTT broker host */
  host: string;
  /** Client identifier */
  clientId: string;
  /** Username for authentication */
  username?: string;
  /** Password for authentication */
  password?: string;
  /** Port number */
  port?: number;
  /** Protocol to use */
  protocol?: 'mqtt' | 'mqtts' | 'ws' | 'wss';
  /** Keepalive interval in seconds */
  keepalive?: number;
  /** Reconnect period in milliseconds */
  reconnectPeriod?: number;
  /** Connect timeout in milliseconds */
  connectTimeout?: number;
  /** Whether to reject unauthorized SSL certificates */
  rejectUnauthorized?: boolean;
}

/**
 * Structure of an MQTT message
 */
export interface MqttMessage {
  /** The topic the message was received on */
  topic: string;
  /** The message payload */
  payload: Buffer | string;
  /** The full MQTT packet */
  packet: IPublishPacket;
}

/**
 * Handler function type for MQTT messages
 */
export type MessageHandler = (message: MqttMessage) => void | Promise<void>;

/**
 * Service for MQTT communication
 *
 * Provides methods for connecting to an MQTT broker, subscribing to topics,
 * publishing messages, and handling incoming messages.
 */
@Service()
export class MqttService {
  private client: MqttClient | null = null;
  private isConnected = false;
  private connectionPromise: Promise<void> | null = null;
  private topicHandlers: Map<string, Set<MessageHandler>> = new Map();
  private globalHandlers: Set<MessageHandler> = new Set();
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 10;
  private readonly defaultConnectTimeout = 30000; // 30 seconds

  /**
   * Initialize the MQTT client with the provided configuration
   *
   * @param config - MQTT client configuration
   */
  public setupClient(config: MqttConfig = {} as MqttConfig): void {
    try {
      const options: IClientOptions = {
        ...config,
        clean: true, // Start with a clean session
      };

      logger.info('Initializing MQTT client', {
        host: config.host,
        clientId: config.clientId,
        port: config.port,
        protocol: config.protocol
      });

      // Create MQTT client
      this.client = mqtt.connect(options);

      // Set up event handlers
      this.setupEventHandlers();
    } catch (error) {
      logger.error('Failed to initialize MQTT client', { error });
      this.isConnected = false;
      this.scheduleReconnect();
    }
  }

  /**
   * Set up event handlers for the MQTT client
   */
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      logger.info('MQTT client connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Resubscribe to all topics
      this.resubscribeToTopics();
    });

    this.client.on('reconnect', () => {
      logger.info('MQTT client reconnecting');
    });

    this.client.on('close', () => {
      logger.info('MQTT client connection closed');
      this.isConnected = false;
    });

    this.client.on('disconnect', () => {
      logger.info('MQTT client disconnected');
      this.isConnected = false;
    });

    this.client.on('offline', () => {
      logger.info('MQTT client is offline');
      this.isConnected = false;
    });

    this.client.on('error', (error) => {
      logger.error('MQTT client error', { error });
      this.isConnected = false;
      this.scheduleReconnect();
    });

    this.client.on('message', (topic, payload, packet) => {
      this.handleMessage(topic, payload, packet);
    });
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error(`Maximum reconnection attempts (${this.maxReconnectAttempts}) reached`);
      return;
    }

    this.reconnectAttempts++;
    // Exponential backoff with max 30 seconds
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      logger.info('Attempting to reconnect MQTT client');
      this.setupClient();
    }, delay);
  }

  /**
   * Resubscribe to all topics after reconnection
   */
  private resubscribeToTopics(): void {
    if (!this.client || !this.isConnected) return;

    const topics = Array.from(this.topicHandlers.keys());
    if (topics.length === 0) return;

    logger.info(`Resubscribing to ${topics.length} topics`);

    for (const topic of topics) {
      this.client.subscribe(topic, (err, granted) => {
        if (err) {
          logger.error(`Error resubscribing to topic ${topic}`, { error: err });
        } else if (granted) {
          logger.info(`Resubscribed to topics: ${granted.map(g => g.topic).join(', ')}`);
        }
      });
    }
  }

  /**
   * Handle incoming MQTT messages
   *
   * @param topic - The topic the message was received on
   * @param payload - The message payload
   * @param packet - The full MQTT packet
   */
  private handleMessage(
    topic: string,
    payload: Buffer,
    packet: IPublishPacket,
  ): void {
    const message: MqttMessage = { topic, payload, packet };

    logger.debug(`Received MQTT message on topic: ${topic}`);

    // Process topic-specific handlers
    this.processTopicHandlers(topic, message);

    // Process wildcard topic handlers
    this.processWildcardHandlers(topic, message);

    // Process global handlers
    this.processGlobalHandlers(message);
  }

  /**
   * Process handlers for a specific topic
   *
   * @param topic - The topic to process handlers for
   * @param message - The MQTT message
   */
  private processTopicHandlers(topic: string, message: MqttMessage): void {
    const handlers = this.topicHandlers.get(topic);
    if (!handlers) return;

    for (const handler of handlers) {
      try {
        handler(message);
      } catch (error) {
        logger.error(`Error in MQTT message handler for topic ${topic}`, { error });
      }
    }
  }

  /**
   * Process handlers for wildcard topics
   *
   * @param topic - The actual topic of the message
   * @param message - The MQTT message
   */
  private processWildcardHandlers(topic: string, message: MqttMessage): void {
    for (const [wildcardTopic, handlers] of this.topicHandlers.entries()) {
      // Skip if it's the exact topic (already processed) or doesn't match the wildcard
      if (wildcardTopic === topic || !this.topicMatches(wildcardTopic, topic)) {
        continue;
      }

      for (const handler of handlers) {
        try {
          handler(message);
        } catch (error) {
          logger.error(`Error in MQTT wildcard handler for ${wildcardTopic}`, { error });
        }
      }
    }
  }

  /**
   * Process global message handlers
   *
   * @param message - The MQTT message
   */
  private processGlobalHandlers(message: MqttMessage): void {
    for (const handler of this.globalHandlers) {
      try {
        handler(message);
      } catch (error) {
        logger.error('Error in MQTT global message handler', { error });
      }
    }
  }

  /**
   * Check if a topic matches a wildcard pattern
   *
   * @param pattern - The wildcard pattern
   * @param topic - The topic to check
   * @returns True if the topic matches the pattern
   */
  private topicMatches(pattern: string, topic: string): boolean {
    // Convert MQTT wildcards to regex
    const regexPattern = pattern
      .replace(/\+/g, '[^/]+') // + matches a single level
      .replace(/#/g, '.*');    // # matches multiple levels

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(topic);
  }

  /**
   * Ensure the MQTT client is connected
   *
   * @returns Promise that resolves when the client is connected
   * @throws Error if connection fails
   */
  private async ensureConnection(): Promise<void> {
    if (this.isConnected) return;

    if (!this.connectionPromise) {
      this.connectionPromise = new Promise<void>((resolve, reject) => {
        if (!this.client) {
          this.setupClient();
        }

        if (!this.client) {
          return reject(new Error('Failed to initialize MQTT client'));
        }

        const connectTimeout = this.defaultConnectTimeout;
        const timeoutId = setTimeout(() => {
          reject(new Error(`MQTT connection timeout after ${connectTimeout}ms`));
        }, connectTimeout);

        this.client.once('connect', () => {
          clearTimeout(timeoutId);
          this.isConnected = true;
          this.connectionPromise = null;
          resolve();
        });

        this.client.once('error', (err) => {
          clearTimeout(timeoutId);
          this.connectionPromise = null;
          reject(err);
        });
      });
    }

    return this.connectionPromise;
  }

  /**
   * Get the MQTT client instance
   *
   * @returns The MQTT client instance
   * @throws Error if client is not initialized
   */
  public getClient(): MqttClient {
    if (!this.client) {
      throw new Error('MQTT client not initialized');
    }
    return this.client;
  }

  /**
   * Subscribe to a topic
   *
   * @param topic - The topic to subscribe to
   * @param handler - The handler function to call when a message is received
   * @param options - Optional subscription options
   * @returns Promise that resolves with the granted subscriptions
   */
  public async subscribe(
    topic: string,
    handler: MessageHandler,
    options?: IClientSubscribeOptions,
  ): Promise<ISubscriptionGrant[]> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('MQTT client not initialized');
    }

    return new Promise<ISubscriptionGrant[]>((resolve, reject) => {
      if (!this.client) {
        return reject(new Error('MQTT client not initialized'));
      }

      this.client.subscribe(topic, options || { qos: 0 }, (err, granted) => {
        if (err) {
          logger.error(`Error subscribing to topic ${topic}`, { error: err });
          return reject(err);
        }

        // Register the handler
        if (!this.topicHandlers.has(topic)) {
          this.topicHandlers.set(topic, new Set());
        }

        const handlers = this.topicHandlers.get(topic);
        if (handlers) {
          handlers.add(handler);
        }

        logger.info(`Subscribed to topic: ${topic}`);
        resolve(granted || []);
      });
    });
  }

  /**
   * Unsubscribe from a topic
   *
   * @param topic - The topic to unsubscribe from
   * @param handler - Optional handler to remove. If not provided, all handlers for the topic are removed.
   * @returns Promise that resolves when unsubscribed
   */
  public async unsubscribe(
    topic: string,
    handler?: MessageHandler,
  ): Promise<void> {
    if (!this.client || !this.isConnected) {
      return;
    }

    // If a specific handler is provided, only remove that handler
    if (handler && this.topicHandlers.has(topic)) {
      const handlers = this.topicHandlers.get(topic);
      if (handlers) {
        handlers.delete(handler);

        // If there are still handlers for this topic, don't unsubscribe
        if (handlers.size > 0) {
          return;
        }
      }
    }

    // Remove all handlers for this topic
    this.topicHandlers.delete(topic);

    // Unsubscribe from the topic
    return new Promise<void>((resolve, reject) => {
      if (!this.client) {
        return resolve(); // Nothing to unsubscribe from
      }

      this.client.unsubscribe(topic, (err) => {
        if (err) {
          logger.error(`Error unsubscribing from topic ${topic}`, { error: err });
          reject(err);
        } else {
          logger.info(`Unsubscribed from topic: ${topic}`);
          resolve();
        }
      });
    });
  }

  /**
   * Register a global message handler that receives all messages
   *
   * @param handler - The handler function to call for all messages
   */
  public registerGlobalHandler(handler: MessageHandler): void {
    this.globalHandlers.add(handler);
    logger.debug('Registered global MQTT message handler');
  }

  /**
   * Unregister a global message handler
   *
   * @param handler - The handler function to remove
   */
  public unregisterGlobalHandler(handler: MessageHandler): void {
    this.globalHandlers.delete(handler);
    logger.debug('Unregistered global MQTT message handler');
  }

  /**
   * Publish a message to a topic
   *
   * @param topic - The topic to publish to
   * @param message - The message to publish
   * @param options - Optional publish options
   * @returns Promise that resolves when the message is published
   */
  public async publish(
    topic: string,
    message: string | Buffer,
    options?: IClientPublishOptions,
  ): Promise<void> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('MQTT client not initialized');
    }

    return new Promise<void>((resolve, reject) => {
      if (!this.client) {
        return reject(new Error('MQTT client not initialized'));
      }

      this.client.publish(
        topic,
        message,
        options || { qos: 0, retain: false },
        (err) => {
          if (err) {
            logger.error(`Error publishing to topic ${topic}`, { error: err });
            reject(err);
          } else {
            logger.debug(`Published message to topic: ${topic}`);
            resolve();
          }
        },
      );
    });
  }

  /**
   * Parse a JSON message payload
   *
   * @param message - The MQTT message
   * @returns The parsed JSON object or null if parsing fails
   */
  public parseJsonMessage<T = any>(message: MqttMessage): T | null {
    try {
      const payload = message.payload.toString();
      return JSON.parse(payload) as T;
    } catch (error) {
      logger.error('Error parsing JSON message', {
        error,
        topic: message.topic,
        payload: message.payload.toString().substring(0, 100) // Log first 100 chars
      });
      return null;
    }
  }

  /**
   * Check if MQTT client is connected
   *
   * @returns True if connected, false otherwise
   */
  public isClientConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Close the MQTT connection
   *
   * @returns Promise that resolves when the connection is closed
   */
  public async close(): Promise<void> {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (!this.client) {
      return;
    }

    return new Promise<void>((resolve) => {
      if (!this.client) {
        return resolve();
      }

      this.client.end(true, {}, () => {
        logger.info('MQTT connection closed');
        this.client = null;
        this.isConnected = false;
        this.topicHandlers.clear();
        this.globalHandlers.clear();
        resolve();
      });
    });
  }
}
