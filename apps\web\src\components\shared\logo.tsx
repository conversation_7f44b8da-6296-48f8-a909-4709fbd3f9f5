export const Logo = () => {
  return (
    <svg
      width="109"
      height="24"
      viewBox="0 0 109 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.4472 19.9793C10.4108 20.4019 10.1592 20.6485 9.94761 20.7806C9.27841 21.1117 8.52812 21.3013 7.73216 21.3013C4.94864 21.3013 2.69265 19.0407 2.69265 16.2518C2.69265 13.463 4.94864 11.2024 7.73216 11.2024C8.21029 11.2024 8.67258 11.272 9.11111 11.3967V8.63026C8.6628 8.54994 8.20237 8.50464 7.73216 8.50464C3.46204 8.50464 0 11.9729 0 16.2518C0 20.5308 3.46158 23.9991 7.73216 23.9991C9.65728 23.9991 11.457 23.2383 12.5345 22.2894C12.6016 22.2157 12.6761 22.1475 12.7712 21.9938C12.7712 21.9938 12.8206 21.9154 12.8215 21.9149C12.8523 21.8645 12.918 21.751 12.9245 21.7361C12.9763 21.6231 13.0224 21.497 13.0587 21.3513C13.0587 21.3475 13.0601 21.3438 13.0611 21.3401C13.0648 21.3251 13.0662 21.3074 13.0699 21.2929C13.075 21.2653 13.0797 21.2359 13.0853 21.2088C13.0918 21.1645 13.0984 21.1201 13.1044 21.073C13.1091 21.0337 13.117 20.9165 13.1207 20.8759V18.0329L10.4476 14.1691V19.9793H10.4472Z"
        fill="#008FD3"
      />
      <path
        d="M25.9107 8.98987C25.3706 9.20234 24.0997 9.81269 23.102 11.1576C23.0992 11.1553 23.0959 11.1525 23.0941 11.1511L18.179 18.2394L13.2644 11.1511C13.2616 11.1525 13.2592 11.1553 13.2564 11.1576C12.2587 9.81269 10.9879 9.20281 10.4478 8.98987V11.8212L18.179 22.9731L25.9111 11.8212V8.98987H25.9107Z"
        fill="#008FD3"
      />
      <path
        d="M28.6259 8.50464C28.1543 8.50464 27.6943 8.54947 27.2469 8.63072V11.3967C27.6854 11.272 28.1477 11.2024 28.6259 11.2024C31.4089 11.2024 33.6649 13.463 33.6649 16.2523C33.6649 19.0411 31.4089 21.3008 28.6259 21.3008C27.829 21.3008 27.0787 21.1112 26.4095 20.7811C26.1988 20.648 25.9467 20.4024 25.9104 19.9788V14.1686L23.2373 18.0324V20.8759C23.241 20.9165 23.2485 21.0337 23.2536 21.0739C23.2587 21.1206 23.2653 21.1649 23.2727 21.2088C23.2778 21.2364 23.2825 21.2658 23.2881 21.2929C23.2909 21.3078 23.2932 21.3256 23.297 21.3401C23.2979 21.3438 23.2984 21.3475 23.2993 21.3513C23.3347 21.497 23.3813 21.6231 23.4321 21.7361C23.4396 21.751 23.5057 21.8645 23.537 21.9149C23.537 21.9159 23.5873 21.9938 23.5873 21.9938C23.6814 22.1475 23.7569 22.2152 23.8231 22.2894C24.9005 23.2383 26.7003 24 28.6263 24C32.896 24 36.3585 20.5313 36.3585 16.2528C36.358 11.9729 32.8955 8.50464 28.6259 8.50464Z"
        fill="#008FD3"
      />
      <path
        d="M13.266 8.81986C13.191 8.4743 13.1504 8.11612 13.1504 7.74814C13.1504 4.96541 15.4018 2.70989 18.1792 2.70989C20.9567 2.70989 23.208 4.96541 23.208 7.74814C23.208 8.11612 23.1675 8.4743 23.0929 8.81986C23.9206 8.21419 24.8736 7.77056 25.9053 7.5324C25.7912 3.35292 22.3771 0 18.1792 0C13.9813 0 10.5673 3.35292 10.4531 7.5324C11.4844 7.77056 12.4374 8.21419 13.266 8.81986Z"
        fill="#008FD3"
      />
      <path
        d="M51.8289 18.716C48.1569 18.716 45.4209 15.854 45.4209 12.236V12.2C45.4209 8.618 48.1029 5.684 51.9189 5.684C54.2409 5.684 55.6449 6.494 56.8509 7.646L55.4289 9.284C54.4209 8.348 53.3409 7.718 51.9009 7.718C49.4889 7.718 47.7429 9.698 47.7429 12.164V12.2C47.7429 14.666 49.4889 16.682 51.9009 16.682C53.4489 16.682 54.4389 16.052 55.5189 15.044L56.9409 16.484C55.6269 17.852 54.1869 18.716 51.8289 18.716ZM58.6559 14.054V11.912H63.9839V14.054H58.6559ZM72.4656 18.716C68.7936 18.716 66.0576 15.854 66.0576 12.236V12.2C66.0576 8.618 68.7396 5.684 72.5556 5.684C74.8776 5.684 76.2816 6.494 77.4876 7.646L76.0656 9.284C75.0576 8.348 73.9776 7.718 72.5376 7.718C70.1256 7.718 68.3796 9.698 68.3796 12.164V12.2C68.3796 14.666 70.1256 16.682 72.5376 16.682C74.0856 16.682 75.0756 16.052 76.1556 15.044L77.5776 16.484C76.2636 17.852 74.8236 18.716 72.4656 18.716ZM78.8247 18.5L84.3687 5.81H86.4207L91.9647 18.5H89.6247L88.3467 15.458H82.3887L81.0927 18.5H78.8247ZM83.1987 13.496H87.5367L85.3587 8.456L83.1987 13.496ZM94.1254 18.5V5.9H96.4834L100.317 11.858L104.151 5.9H106.509V18.5H104.295V9.464L100.317 15.404H100.245L96.3034 9.5V18.5H94.1254Z"
        fill="#008FD3"
      />
    </svg>
  )
}
