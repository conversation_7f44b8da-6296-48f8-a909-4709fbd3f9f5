import { Client, Pool, PoolClient } from 'pg';
import { environment } from '../configs/environment';
import { logger, Service, Inject } from '@c-visitor/framework';
import { CivamsMqttService } from './civams-mqtt.service';

/**
 * Database configuration interface
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
  ssl?: boolean | { rejectUnauthorized: boolean };
}

/**
 * PostgreSQL service for executing raw SQL queries
 * This service provides methods to interact with PostgreSQL database using raw queries
 */
@Service()
export class CivamsService {
  private pool: Pool | null = null;
  private client: Client | null = null;
  private slowQueryThreshold = 200; // Log queries taking more than 200ms
  private queryCache: Map<string, { result: any; timestamp: number }> =
    new Map();
  private queryCacheExpiration = 60000; // 1 minute cache expiration
  private currentConfig: DatabaseConfig | null = null;

  /**
   * Constructor - can accept optional database configuration
   * If no configuration is provided, it will be initialized lazily when needed
   * @param civamsMqttService Optional MQTT service (can be null for seeding)
   * @param config Optional database configuration
   */
  constructor(
    @Inject(CivamsMqttService) private readonly civamsMqttService?: CivamsMqttService,
    config?: DatabaseConfig
  ) {
    if (config) {
      this.initialize(config);
    }
  }

  /**
   * Initialize the PostgreSQL service with the given configuration
   * @param config Database configuration
   * @returns The initialized PostgreSQL service
   */
  initialize(config: DatabaseConfig): this {
    this.currentConfig = config;

    // Initialize the connection pool
    this.createPool(config);

    return this;
  }

  /**
   * Get the default database configuration from environment
   * @returns Default database configuration
   */
  private getDefaultConfig(): DatabaseConfig {
    // Determine SSL configuration based on environment
    const sslConfig = process.env.NODE_ENV === 'production'
      ? { rejectUnauthorized: false }
      : undefined;

    return {
      host: environment.CIVAMS_DB_HOST,
      port: environment.CIVAMS_DB_PORT,
      user: environment.CIVAMS_DB_USER,
      password: environment.CIVAMS_DB_PASSWORD,
      database: environment.CIVAMS_DB_NAME,
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
      connectionTimeoutMillis: 2000, // How long to wait for a connection to become available
      ssl: sslConfig,
    };
  }

  /**
   * Create a new connection pool with the given configuration
   * @param config Database configuration
   */
  private createPool(config: DatabaseConfig): void {
    // Close existing pool if it exists
    if (this.pool) {
      this.pool.end().catch((err) => {
        logger.error('Error closing existing pool', err);
      });
    }

    // Create new pool
    this.pool = new Pool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      max: config.max || 20,
      idleTimeoutMillis: config.idleTimeoutMillis || 30000,
      connectionTimeoutMillis: config.connectionTimeoutMillis || 2000,
      ssl: config.ssl,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });

    logger.info('PostgreSQL service initialized with configuration', {
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
    });
  }

  /**
   * Get the current database configuration
   * @returns Current database configuration
   */
  getConfig(): DatabaseConfig {
    return this.currentConfig || this.getDefaultConfig();
  }

  /**
   * Update the database configuration and reinitialize the connection pool
   * @param config New database configuration
   */
  updateConfig(config: Partial<DatabaseConfig>): void {
    const currentConfig = this.getConfig();
    const newConfig = { ...currentConfig, ...config };
    this.initialize(newConfig);
  }

  /**
   * Ensure the pool is initialized
   */
  private ensurePool(): Pool {
    if (!this.pool) {
      this.initialize(this.getDefaultConfig());
    }

    // At this point, this.pool should be initialized
    if (!this.pool) {
      throw new Error('Failed to initialize database pool');
    }

    return this.pool;
  }

  /**
   * Connect to the database with a dedicated client (not from the pool)
   * Use this for long-running connections or special cases
   * @param config Optional database configuration to use for this connection
   */
  async connect(config?: DatabaseConfig): Promise<void> {
    if (this.client) {
      return;
    }

    const connectionConfig = config || this.getConfig();

    this.client = new Client({
      host: connectionConfig.host,
      port: connectionConfig.port,
      user: connectionConfig.user,
      password: connectionConfig.password,
      database: connectionConfig.database,
      ssl: connectionConfig.ssl,
    });

    await this.client.connect();
    logger.info('Connected to PostgreSQL with dedicated client', {
      host: connectionConfig.host,
      database: connectionConfig.database,
    });
  }

  /**
   * Close the dedicated client connection
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.end();
      this.client = null;
      logger.info('Disconnected from PostgreSQL');
    }
  }

  /**
   * End all pool connections
   */
  async end(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      logger.info('All pool connections closed');
    }
  }

  /**
   * Execute a SELECT query and return the results
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @param options Additional options for the query
   * @returns Query results
   */
  async select<T = unknown>(
    sql: string,
    params: unknown[] = [],
    options: {
      useCache?: boolean;
      cacheTime?: number;
    } = {},
  ): Promise<T[]> {
    const startTime = performance.now();

    try {
      // Get the pool (ensures it's initialized)
      const pool = this.ensurePool();

      // Check if we should use cache
      if (options.useCache) {
        const cacheKey = `${sql}:${JSON.stringify(params)}`;
        const cached = this.queryCache.get(cacheKey);

        if (
          cached &&
          Date.now() - cached.timestamp <
            (options.cacheTime || this.queryCacheExpiration)
        ) {
          return cached.result as T[];
        }
      }

      // Execute the query
      const result = await pool.query(sql, params);

      const duration = performance.now() - startTime;
      this.logQueryPerformance('SELECT', sql, duration);

      // Cache the result if requested
      if (options.useCache) {
        const cacheKey = `${sql}:${JSON.stringify(params)}`;
        this.queryCache.set(cacheKey, {
          result: result.rows,
          timestamp: Date.now(),
        });
      }

      return result.rows as T[];
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logQueryError('SELECT', sql, error, duration);
      throw error;
    }
  }

  /**
   * Execute a SELECT query and return a single result
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @param options Additional options for the query
   * @returns Single query result or null if not found
   */
  async selectOne<T = any>(
    sql: string,
    params: any[] = [],
    options: {
      useCache?: boolean;
      cacheTime?: number;
    } = {},
  ): Promise<T | null> {
    const results = await this.select<T>(sql, params, options);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * Execute an INSERT query and return the inserted record(s)
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @returns Inserted record(s)
   */
  async insert<T = unknown>(sql: string, params: unknown[] = []): Promise<T> {
    const startTime = performance.now();

    try {
      // Get the pool (ensures it's initialized)
      const pool = this.ensurePool();

      // Execute the query
      const result = await pool.query(sql, params);

      const duration = performance.now() - startTime;
      this.logQueryPerformance('INSERT', sql, duration);

      // For INSERT queries with RETURNING clause, return the first row
      return result.rows.length > 0 ? (result.rows[0] as T) : ({} as T);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logQueryError('INSERT', sql, error, duration);
      throw error;
    }
  }

  /**
   * Execute an UPDATE query
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @returns Number of affected rows
   */
  async update(sql: string, params: unknown[] = []): Promise<number> {
    const startTime = performance.now();

    try {
      // Get the pool (ensures it's initialized)
      const pool = this.ensurePool();

      // Execute the query
      const result = await pool.query(sql, params);

      const duration = performance.now() - startTime;
      this.logQueryPerformance('UPDATE', sql, duration);

      // Return the number of affected rows
      return result.rowCount || 0;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logQueryError('UPDATE', sql, error, duration);
      throw error;
    }
  }

  /**
   * Execute a DELETE query
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @returns Number of affected rows
   */
  async delete(sql: string, params: unknown[] = []): Promise<number> {
    const startTime = performance.now();

    try {
      // Get the pool (ensures it's initialized)
      const pool = this.ensurePool();

      // Execute the query
      const result = await pool.query(sql, params);

      const duration = performance.now() - startTime;
      this.logQueryPerformance('DELETE', sql, duration);

      // Return the number of affected rows
      return result.rowCount || 0;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logQueryError('DELETE', sql, error, duration);
      throw error;
    }
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query string
   * @param params Query parameters (to prevent SQL injection)
   * @returns Query result
   */
  async query<T = unknown>(sql: string, params: unknown[] = []): Promise<T> {
    const startTime = performance.now();

    try {
      // Get the pool (ensures it's initialized)
      const pool = this.ensurePool();

      // Execute the query
      const result = await pool.query(sql, params);

      const duration = performance.now() - startTime;
      this.logQueryPerformance('QUERY', sql, duration);

      // Return the result
      return result as unknown as T;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logQueryError('QUERY', sql, error, duration);
      throw error;
    }
  }

  /**
   * Execute multiple queries in a transaction
   * @param callback Function that executes queries within the transaction
   * @returns Result of the callback function
   */
  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
  ): Promise<T> {
    // Get the pool (ensures it's initialized)
    const pool = this.ensurePool();

    const client = await pool.connect();
    let result: T;

    try {
      await client.query('BEGIN');

      result = await callback(client);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Transaction failed', error);
      throw error;
    } finally {
      client.release();
    }

    return result;
  }

  /**
   * Clear the query cache
   */
  clearCache(): void {
    this.queryCache.clear();
    logger.debug('Query cache cleared');
  }

  /**
   * Log query performance metrics
   * @param queryType Type of query (SELECT, INSERT, etc.)
   * @param sql SQL query string
   * @param duration Query execution duration in milliseconds
   */
  private logQueryPerformance(
    queryType: string,
    sql: string,
    duration: number,
  ): void {
    // Log slow queries
    if (duration > this.slowQueryThreshold) {
      logger.warn(
        `Slow ${queryType} query (${Math.round(duration)}ms): ${this.truncateSql(sql)}`,
      );
    } else {
      logger.debug(
        `${queryType} query executed in ${Math.round(duration)}ms: ${this.truncateSql(sql)}`,
      );
    }
  }

  /**
   * Log query errors
   * @param queryType Type of query (SELECT, INSERT, etc.)
   * @param sql SQL query string
   * @param error Error object
   * @param duration Query execution duration in milliseconds
   */
  private logQueryError(
    queryType: string,
    sql: string,
    error: unknown,
    duration: number,
  ): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      `Error executing ${queryType} query (${Math.round(duration)}ms): ${errorMessage}`,
      {
        sql: this.truncateSql(sql),
        error,
      },
    );
  }

  /**
   * Truncate SQL query for logging
   * @param sql SQL query string
   * @returns Truncated SQL query
   */
  private truncateSql(sql: string): string {
    const maxLength = 200;
    return sql.length > maxLength ? `${sql.substring(0, maxLength)}...` : sql;
  }

  /**
   * Publish face quality request via MQTT
   * @param imageName Name of the image file
   * @param imageBase64 Base64 encoded image data
   * @param eventId Event identifier
   * @param companyId Company identifier
   */
  async faceQualityRequest(
    imageName: string,
    imageBase64: string,
    eventId: string,
    companyId: string,
  ): Promise<void> {
    if (!this.civamsMqttService) {
      throw new Error('MQTT service not available');
    }
    await this.civamsMqttService.publishFaceQualityRequest(
      imageName,
      imageBase64,
      eventId,
      companyId,
    );
  }

  /**
   * Subscribe to face quality acknowledgment messages
   * @param callback Callback function to handle acknowledgment messages
   */
  async faceQualityAck(callback: (message: any) => void): Promise<void> {
    if (!this.civamsMqttService) {
      throw new Error('MQTT service not available');
    }
    await this.civamsMqttService.subscribeToTopic('topic/FaceQualityAck/', callback);
  }

  /**
   * Publish a message to a specific MQTT topic
   * @param topic MQTT topic
   * @param message Message to publish
   */
  async publishMqttMessage(topic: string, message: any): Promise<void> {
    if (!this.civamsMqttService) {
      throw new Error('MQTT service not available');
    }
    await this.civamsMqttService.publish(topic, message);
  }

  /**
   * Subscribe to a specific MQTT topic
   * @param topic MQTT topic
   * @param handler Message handler function
   */
  async subscribeMqttTopic(
    topic: string,
    handler: (message: any) => void | Promise<void>,
  ): Promise<void> {
    if (!this.civamsMqttService) {
      throw new Error('MQTT service not available');
    }
    await this.civamsMqttService.subscribeToTopic(topic, handler);
  }

  /**
   * Check if MQTT client is connected
   * @returns True if connected, false otherwise
   */
  isMqttConnected(): boolean {
    if (!this.civamsMqttService) {
      return false;
    }
    return this.civamsMqttService.isConnected();
  }

  // ===== COMPANY AND DEPARTMENT SEEDING METHODS =====

  /**
   * Get C-Access company from PostgreSQL database
   * @returns C-Access company or null if not found
   */
  async getCAccessCompany(): Promise<any | null> {
    const query = `
      SELECT * FROM public."Company"
      WHERE name = 'C-Access'
      LIMIT 1;
    `;

    try {
      const result = await this.selectOne<any>(query);
      return result || null;
    } catch (error) {
      logger.error('Failed to get C-Access company:', error);
      throw error;
    }
  }

  /**
   * Get Visitor department from PostgreSQL database
   * @returns Visitor department or null if not found
   */
  async getVisitorDepartment(): Promise<any | null> {
    const query = `
      SELECT * FROM public."Department"
      WHERE name = 'Visitor'
      LIMIT 1;
    `;

    try {
      const result = await this.selectOne<any>(query);
      return result || null;
    } catch (error) {
      logger.error('Failed to get Visitor department:', error);
      throw error;
    }
  }

  /**
   * Insert or get existing C-Access company in PostgreSQL database
   * @param companyId Company ID to use for new company
   * @param companyName Company name (default: 'C-Access')
   * @returns Query result with company data
   */
  async insertCAccessCompany(companyId: string, companyName: string = 'C-Access'): Promise<any> {
    const query = `
      WITH insert_result AS (
        INSERT INTO public."Company" (
          id, name, "sortKey", "timezoneOffset",
          "dateCreated", "dateModified", "isActive",
          "isClusterSync", "civamsSiteId"
        )
        SELECT
          $1, $2, $3, $4, $5, $6, $7, $8, $9
        WHERE NOT EXISTS (
          SELECT 1 FROM public."Company" WHERE name = $10
        )
        RETURNING *
      )
      SELECT * FROM insert_result
      UNION ALL
      SELECT * FROM public."Company" WHERE name = $10 AND NOT EXISTS (SELECT * FROM insert_result);
    `;

    const values = [
      companyId, // $1: id
      companyName, // $2: name
      0, // $3: sortKey
      0, // $4: timezoneOffset
      new Date().toISOString(), // $5: dateCreated
      new Date().toISOString(), // $6: dateModified
      true, // $7: isActive
      false, // $8: isClusterSync
      null, // $9: civamsSiteId
      companyName // $10: companyName
    ];

    try {
      return await this.transaction(async (client) => {
        const result = await client.query(query, values);
        logger.info('C-Access company processed:', result.rows[0]);
        return result;
      });
    } catch (error) {
      logger.error('Failed to insert C-Access company:', error);
      throw error;
    }
  }

  /**
   * Insert or get existing Visitor department in PostgreSQL database
   * @param companyId Company ID that this department belongs to
   * @param departmentId Department ID to use for new department
   * @param departmentName Department name (default: 'Visitor')
   * @returns Query result with department data
   */
  async insertVisitorDepartment(companyId: string, departmentId: string, departmentName: string = 'Visitor'): Promise<any> {
    const query = `
      WITH insert_result AS (
        INSERT INTO public."Department" (
          id, name, "parentId", "companyId",
          "dateCreated", "dateModified", "isActive",
          "isClusterSync", "civamsSiteId"
        )
        SELECT
          $1, $2, $3, $4, $5, $6, $7, $8, $9
        WHERE NOT EXISTS (
          SELECT 1 FROM public."Department" WHERE name = $10 AND "companyId" = $4
        )
        RETURNING *
      )
      SELECT * FROM insert_result
      UNION ALL
      SELECT * FROM public."Department" WHERE name = $10 AND "companyId" = $4 AND NOT EXISTS (SELECT * FROM insert_result);
    `;

    const values = [
      departmentId, // $1: id
      departmentName, // $2: name
      null, // $3: parentId
      companyId, // $4: companyId
      new Date().toISOString(), // $5: dateCreated
      new Date().toISOString(), // $6: dateModified
      true, // $7: isActive
      false, // $8: isClusterSync
      null, // $9: civamsSiteId
      departmentName // $10: departmentName
    ];

    try {
      return await this.transaction(async (client) => {
        const result = await client.query(query, values);
        logger.info('Visitor department processed:', result.rows[0]);
        return result;
      });
    } catch (error) {
      logger.error('Failed to insert Visitor department:', error);
      throw error;
    }
  }

  /**
   * Seed C-Access company and Visitor department
   * This method ensures that the required company and department exist in PostgreSQL
   * @returns Object containing company and department data
   */
  async seedCAccessReferences(): Promise<{ company: any; department: any }> {
    try {
      logger.info('🌱 Seeding C-Access company and Visitor department...');

      // Generate UUIDs for new records if needed
      const { v4: uuidv4 } = await import('uuid');
      const companyId = uuidv4();
      const departmentId = uuidv4();

      // Insert or get existing company
      const companyResult = await this.insertCAccessCompany(companyId, 'C-Access');
      const company = companyResult.rows[0];

      // Insert or get existing department
      const departmentResult = await this.insertVisitorDepartment(company.id, departmentId, 'Visitor');
      const department = departmentResult.rows[0];

      logger.info('✅ C-Access company and Visitor department seeding completed');
      return { company, department };
    } catch (error) {
      logger.error('❌ Failed to seed C-Access references:', error);
      throw error;
    }
  }

  /**
   * Insert a reference as a user into PostgreSQL User table
   * @param reference Reference data from MongoDB
   * @returns Inserted user data with PostgreSQL User ID
   */
  async insertReferenceAsUser(reference: any): Promise<any> {
    try {
      logger.info('🔄 Inserting reference as user into PostgreSQL...', {
        referenceId: reference._id,
        email: reference.email,
        fullName: reference.fullName
      });

      // Get C-Access company and Visitor department
      const { company, department } = await this.seedCAccessReferences();

      // Generate UUID for PostgreSQL User
      const { v4: uuidv4 } = await import('uuid');
      const postgresUserId = uuidv4();

      const query = `
        INSERT INTO public."User" (
          id, email, name, password, birthday, gender, avatar,
          "companyId", "departmentId", "integrationKey", phone, "activeStatus",
          "dateCreated", "dateModified", "isClusterSync", "adName", "patientId",
          "isLoginAvaiable", "isFaceReScanEnable", "civamsSiteId", title,
          "studentId", "ottEnable", "telegramId", "voiceString", "welcomVoicePath",
          "welcomeImagePath", color, "displayString", "isRemoteCheckEnable",
          "userRank", "roleId", "integrationKey2", "idCardJson", "idCardNumber",
          "idCardFaceSimilarity", "MaskConfThreshold", "NoMaskConfThreshold", "autoLockTime"
        )
        VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17,
          $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32,
          $33, $34, $35, $36, $37, $38, $39
        )
        RETURNING *;
      `;

      const values = [
        postgresUserId, // $1: id
        reference.email, // $2: email
        reference.fullName, // $3: name
        'default_password', // $4: password (fake)
        new Date(), // $5: birthday (fake)
        0, // $6: gender (fake - 0 = male)
        reference.avatar || '', // $7: avatar
        company.id, // $8: companyId (C-Access)
        department.id, // $9: departmentId (Visitor)
        reference._id.toString(), // $10: integrationKey (MongoDB reference ID)
        reference.phone || '', // $11: phone
        'ACTIVE', // $12: activeStatus
        new Date(), // $13: dateCreated
        new Date(), // $14: dateModified
        false, // $15: isClusterSync
        null, // $16: adName (fake)
        null, // $17: patientId (fake)
        true, // $18: isLoginAvaiable (fake)
        true, // $19: isFaceReScanEnable (fake)
        null, // $20: civamsSiteId (fake)
        reference.fullName, // $21: title (fake)
        null, // $22: studentId (fake)
        false, // $23: ottEnable (fake)
        null, // $24: telegramId (fake)
        null, // $25: voiceString (fake)
        null, // $26: welcomVoicePath (fake)
        null, // $27: welcomeImagePath (fake)
        null, // $28: color (fake)
        reference.fullName, // $29: displayString (fake)
        false, // $30: isRemoteCheckEnable (fake)
        0, // $31: userRank (fake)
        'EMPLOYEE', // $32: roleId (fake)
        null, // $33: integrationKey2 (fake)
        JSON.stringify({ cardNumber: reference.cardIdNumber }), // $34: idCardJson
        reference.cardIdNumber, // $35: idCardNumber
        null, // $36: idCardFaceSimilarity (fake)
        null, // $37: MaskConfThreshold (fake)
        null, // $38: NoMaskConfThreshold (fake)
        null, // $39: autoLockTime (fake)
      ];

      const result = await this.insert<any>(query, values);

      logger.info('✅ Reference inserted as user into PostgreSQL', {
        postgresUserId: result.id,
        referenceId: reference._id,
        email: reference.email,
        companyId: company.id,
        departmentId: department.id
      });

      return result;
    } catch (error) {
      logger.error('❌ Failed to insert reference as user into PostgreSQL:', error);
      throw error;
    }
  }

  /**
   * Process a new reference: Insert into PostgreSQL and update MongoDB mappings
   * @param reference Reference document from MongoDB
   * @returns Object containing PostgreSQL user data and updated reference
   */
  async processNewReference(reference: any): Promise<{ postgresUser: any; updatedReference: any }> {
    try {
      logger.info('🔄 Processing new reference...', {
        referenceId: reference._id,
        email: reference.email
      });

      // Insert reference as user into PostgreSQL
      const postgresUser = await this.insertReferenceAsUser(reference);

      // Update MongoDB reference with PostgreSQL User ID in mappings array
      const updatedReference = await this.updateReferenceMappings(reference._id, postgresUser.id);

      logger.info('✅ Reference processing completed', {
        referenceId: reference._id,
        postgresUserId: postgresUser.id,
        mappingsUpdated: updatedReference?.mappings?.length > 0
      });

      return { postgresUser, updatedReference };
    } catch (error) {
      logger.error('❌ Failed to process new reference:', error);
      throw error;
    }
  }

  /**
   * Update MongoDB reference mappings array with PostgreSQL User ID
   * @param referenceId MongoDB reference ID
   * @param postgresUserId PostgreSQL User ID
   * @returns Updated reference document
   */
  private async updateReferenceMappings(referenceId: string, postgresUserId: string): Promise<any> {
    try {
      // Import mongoose and Reference model dynamically to avoid circular dependencies
      const mongoose = await import('mongoose');

      // Get the Reference model from mongoose
      const ReferenceModel = mongoose.default.model('Reference');

      // Update the reference with PostgreSQL User ID in mappings array
      const updatedReference = await ReferenceModel.findByIdAndUpdate(
        referenceId,
        {
          $addToSet: { mappings: postgresUserId } // Add PostgreSQL User ID to mappings array (avoid duplicates)
        },
        { new: true }
      );

      if (!updatedReference) {
        throw new Error(`Reference with ID ${referenceId} not found`);
      }

      logger.info('✅ Reference mappings updated', {
        referenceId,
        postgresUserId,
        mappings: updatedReference.mappings
      });

      return updatedReference;
    } catch (error) {
      logger.error('❌ Failed to update reference mappings:', error);
      throw error;
    }
  }
}
