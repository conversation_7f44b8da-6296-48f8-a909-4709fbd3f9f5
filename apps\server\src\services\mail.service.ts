import { environment } from '@/configs/environment';
import { Service } from '@c-visitor/framework';
import { createTransport, Transporter } from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';

interface EmailData {
  to: string;
  subject: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

@Service()
export class MailService {
  private readonly transporter: Transporter<SMTPTransport.Options>;

  constructor() {
    const smtpConfig: SMTPTransport.Options = {
      service: 'gmail',
      host: environment.SMTP_MAIL_HOST || 'smtp.gmail.com',
      port: environment.SMTP_MAIL_PORT || 587,
      secure: environment.SMTP_MAIL_SECURE || false,
      auth: {
        user: environment.SMTP_MAIL_AUTH_USER || '',
        pass: environment.SMTP_MAIL_AUTH_PASS || '',
      },
    };

    console.log('Initializing SMTP transporter with config:', {
      service: smtpConfig.service,
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      hasUser: !!(smtpConfig.auth as any)?.user,
      hasPass: !!(smtpConfig.auth as any)?.pass,
    });

    this.transporter = createTransport(smtpConfig);

    // Verify connection on startup
    this.verifyConnection().then(isConnected => {
      if (isConnected) {
        console.log('SMTP service initialized successfully');
      } else {
        console.error('SMTP service initialization failed');
      }
    });
  }

  verifyConnection = async (): Promise<boolean> => {
    try {
      console.log('Verifying SMTP connection...');
      await this.transporter.verify();
      console.log('SMTP connection verified successfully');
      return true;
    } catch (error) {
      console.error('SMTP connection verification failed:', {
        error: error,
        message: error instanceof Error ? error.message : 'Unknown error',
        host: environment.SMTP_MAIL_HOST,
        port: environment.SMTP_MAIL_PORT,
        user: environment.SMTP_MAIL_AUTH_USER,
        hasPassword: !!environment.SMTP_MAIL_AUTH_PASS
      });
      return false;
    }
  };

  sendEmail = async (
    emailData: EmailData,
  ): Promise<{ success: boolean; messageId?: string; error?: unknown }> => {
    try {
      // Validate email data
      if (!emailData.to || !emailData.subject) {
        const validationError = 'Missing required email data: to or subject';
        console.error('Email validation failed:', validationError);
        return { success: false, error: validationError };
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailData.to)) {
        const formatError = `Invalid email format: ${emailData.to}`;
        console.error('Email format validation failed:', formatError);
        return { success: false, error: formatError };
      }

      // Check SMTP connection before sending
      try {
        await this.transporter.verify();
        console.log('SMTP connection verified for email sending');
      } catch (connectionError) {
        console.error('SMTP connection failed before sending email:', connectionError);
        return { success: false, error: `SMTP connection failed: ${connectionError}` };
      }

      const mailOptions = {
        from: environment.SMTP_MAIL_FROM || 'C-CAM <<EMAIL>>',
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        attachments: emailData.attachments,
      };

      console.log('Attempting to send email:', {
        to: emailData.to,
        subject: emailData.subject,
        from: mailOptions.from,
        hasHtml: !!emailData.html,
        attachmentCount: emailData.attachments?.length || 0
      });

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', {
        messageId: info.messageId,
        to: emailData.to,
        subject: emailData.subject
      });
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending email:', {
        error: error,
        to: emailData.to,
        subject: emailData.subject,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      return { success: false, error };
    }
  };
}
