import React from 'react';

interface CalendarIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const CalendarIcon: React.FC<CalendarIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative", 
  width = 14, 
  height = 14 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <g clipPath="url(#clip0_166_127)">
        <path
          d="M10.5 1.16669V2.33335M3.5 1.16669V2.33335"
          stroke="#73787E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.45834 7.14189C1.45834 4.60013 1.45834 3.32925 2.18875 2.53962C2.91915 1.75 4.09471 1.75 6.44584 1.75H7.55418C9.90531 1.75 11.0809 1.75 11.8113 2.53962C12.5417 3.32925 12.5417 4.60013 12.5417 7.14189V7.44144C12.5417 9.9832 12.5417 11.2541 11.8113 12.0437C11.0809 12.8333 9.90531 12.8333 7.55418 12.8333H6.44584C4.09471 12.8333 2.91915 12.8333 2.18875 12.0437C1.45834 11.2541 1.45834 9.9832 1.45834 7.44144V7.14189Z"
          stroke="#73787E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.75 4.66669H12.25"
          stroke="#73787E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_166_127">
          <rect width={14} height={14} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
