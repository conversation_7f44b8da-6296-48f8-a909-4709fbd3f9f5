import { IRequest } from '@c-visitor/types';

interface RequestInfoSummaryProps {
  selectedRequest: IRequest | null;
}

/**
 * Component that displays a summary of the request information
 */
export function RequestInfoSummary({
  selectedRequest,
}: RequestInfoSummaryProps) {
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date
        .toLocaleString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        })
        .replace(',', '');
    } catch {
      return dateString;
    }
  };

  if (!selectedRequest) {
    return null;
  }

  return (
    <div className="bg-white w-full rounded-lg">
      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-5">
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2">
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2">
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
                <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-[#1f2329]">
                  {selectedRequest.purpose}
                </p>
                <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#53637a]">
                  -
                </p>
                <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-[#8f959e]">
                  {selectedRequest.code}
                </p>
              </div>
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                <div
                  className={`flex justify-start items-center flex-grow-0 flex-shrink-0 relative overflow-hidden gap-1 px-2 py-[3px] rounded-3xl ${
                    selectedRequest.status === 'PENDING'
                      ? 'bg-[#e8f4ff]'
                      : selectedRequest.status === 'APPROVED'
                        ? 'bg-[#e3f9ee]'
                        : selectedRequest.status === 'REJECTED'
                          ? 'bg-[#feeaeb]'
                          : 'bg-[#e8f4ff]'
                  }`}
                >
                  <p
                    className={`flex-grow-0 flex-shrink-0 text-xs font-medium text-center ${
                      selectedRequest.status === 'PENDING'
                        ? 'text-[#008fd3]'
                        : selectedRequest.status === 'APPROVED'
                          ? 'text-[#02b875]'
                          : selectedRequest.status === 'REJECTED'
                            ? 'text-[#e03e59]'
                            : 'text-[#008fd3]'
                    }`}
                  >
                    {selectedRequest.status === 'PENDING'
                      ? 'Pending'
                      : selectedRequest.status === 'APPROVED'
                        ? 'Approved'
                        : selectedRequest.status === 'REJECTED'
                          ? 'Rejected'
                          : selectedRequest.status}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
              {selectedRequest.createdByUser ? (
                <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                  <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                    Created by:
                  </p>
                  <p className="text-sm text-[#1f2329]">
                    {selectedRequest.createdByUser.fullName}
                  </p>
                </div>
              ) : null}
              {selectedRequest.createdAt && (
                <div className="flex flex-row justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
                  <p className="text-xs font-medium text-[#8f959e] min-w-[80px]">
                    Created at:
                  </p>
                  <p className="text-sm text-[#1f2329]">
                    {new Date(selectedRequest.createdAt)
                      .toLocaleString('en-GB', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false,
                      })
                      .replace(',', '')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-wrap justify-start items-center self-stretch gap-4 md:gap-6 lg:gap-8 px-3 md:px-4 py-3 rounded-lg border border-[#dde4ee]">
          <div className="flex flex-col justify-center items-start gap-2 min-w-[150px] max-w-full">
            <p className="text-sm font-medium text-left text-[#8f959e]">
              ASSIGNEE
            </p>
            <p className="text-sm text-left text-[#1f2329] break-words">
              {selectedRequest.supervisorName} -{' '}
              {selectedRequest.supervisorPhone} -{' '}
              {selectedRequest.supervisorEmail}
            </p>
          </div>
          <svg
            width={1}
            height={16}
            viewBox="0 0 1 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0"
            preserveAspectRatio="none"
          >
            <line
              x1="0.5"
              y1="2.18557e-8"
              x2="0.499999"
              y2={16}
              stroke="#DDE4EE"
            />
          </svg>
          <div className="flex flex-col justify-center items-start gap-2 min-w-[150px] max-w-full">
            <p className="text-sm font-medium text-left text-[#8f959e]">
              TIME IN
            </p>
            <p className="text-sm text-left text-[#1f2329] break-words">
              {formatDateTime(selectedRequest.timeIn)}
            </p>
          </div>
          <svg
            width={1}
            height={16}
            viewBox="0 0 1 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0"
            preserveAspectRatio="none"
          >
            <line
              x1="0.5"
              y1="2.18557e-8"
              x2="0.499999"
              y2={16}
              stroke="#DDE4EE"
            />
          </svg>
          <div className="flex flex-col justify-center items-start gap-2 min-w-[150px] max-w-full">
            <p className="text-sm font-medium text-left text-[#8f959e]">
              TIME OUT
            </p>
            <p className="text-sm text-left text-[#1f2329] break-words">
              {selectedRequest.timeOut
                ? formatDateTime(selectedRequest.timeOut)
                : 'Not set'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
