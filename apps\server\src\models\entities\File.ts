import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { FileAttributes } from '@c-visitor/types';

// File interface for Mongoose document
export interface FileDocument extends Omit<FileAttributes, 'id'>, Document {}

// File schema for Mongoose
const FileSchema = new Schema<FileDocument>(
  {
    filename: { type: String, required: true, trim: true },
    originalName: { type: String, required: true, trim: true },
    mimeType: { type: String, required: true, trim: true },
    size: { type: Number, required: true, min: 0 },
    bucket: { type: String, required: true, trim: true },
    uploadedBy: { type: String, required: true, trim: true },
  },
  { timestamps: true, collection: 'files' },
);

// Create indexes for frequently queried fields
FileSchema.index({ filename: 1 });
FileSchema.index({ bucket: 1 });
FileSchema.index({ uploadedBy: 1 });

// Create the File model
const FileModel = mongoose.model<FileDocument>('File', FileSchema);

export default FileModel;
