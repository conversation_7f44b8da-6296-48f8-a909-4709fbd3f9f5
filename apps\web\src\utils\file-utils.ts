/**
 * Utility functions for handling file operations
 */

// Get API URL from environment
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

/**
 * Convert filename to streamable URL
 * @param filename The filename stored in database
 * @param bucket Optional bucket name (default: 'images')
 * @returns Full URL to stream the file
 */
export function getFileUrl(filename: string, bucket: string = 'images'): string {
  if (!filename) {
    return '';
  }
  
  // If filename is already a full URL, return as is
  if (filename.startsWith('http://') || filename.startsWith('https://')) {
    return filename;
  }
  
  // If filename is a data URI, return as is
  if (filename.startsWith('data:')) {
    return filename;
  }
  
  // Build the streaming URL
  return `${API_URL}/api/storage/stream/filename/${encodeURIComponent(filename)}?bucket=${bucket}`;
}

/**
 * Convert avatar filename to URL
 * @param filename The avatar filename
 * @returns Full URL to stream the avatar image
 */
export function getAvatarUrl(filename: string): string {
  return getFileUrl(filename, 'images');
}

/**
 * Convert recognition detected face filename to URL
 * @param filename The detected face filename
 * @returns Full URL to stream the detected face image
 */
export function getDetectedFaceUrl(filename: string): string {
  return getFileUrl(filename, 'images');
}

/**
 * Convert card ID image filename to URL
 * @param filename The card ID image filename
 * @returns Full URL to stream the card ID image
 */
export function getCardIdUrl(filename: string): string {
  return getFileUrl(filename, 'images');
}

/**
 * Check if a string is a valid filename (not a URL or data URI)
 * @param value The value to check
 * @returns True if it's a filename, false if it's already a URL or data URI
 */
export function isFilename(value: string): boolean {
  if (!value) return false;
  
  // Check if it's a URL
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return false;
  }
  
  // Check if it's a data URI
  if (value.startsWith('data:')) {
    return false;
  }
  
  return true;
}

/**
 * Get the appropriate image URL, handling both filenames and existing URLs
 * @param imageValue The image value (could be filename, URL, or data URI)
 * @param bucket Optional bucket name (default: 'images')
 * @returns Appropriate URL for the image
 */
export function getImageUrl(imageValue: string, bucket: string = 'images'): string {
  if (!imageValue) {
    return '';
  }
  
  // If it's already a URL or data URI, return as is
  if (!isFilename(imageValue)) {
    return imageValue;
  }
  
  // Convert filename to URL
  return getFileUrl(imageValue, bucket);
}
