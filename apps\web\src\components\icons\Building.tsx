import React from 'react';
import { cn } from '@/lib/utils';

interface BuildingIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const BuildingIcon: React.FC<BuildingIconProps> = ({ 
  className, 
  color = "#141B34", 
  ...props 
}) => {
  return (
    <svg
      width={16}
      height={18}
      viewBox="0 0 16 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[13.5px] h-[15px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M10.25 1.5L5.75 1.5C3.268 1.5 2.75 2.018 2.75 4.5L2.75 16.5H13.25L13.25 4.5C13.25 2.018 12.732 1.5 10.25 1.5Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M1.25 16.5L14.75 16.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.25 16.5V14.25C10.25 13.009 9.991 12.75 8.75 12.75H7.25C6.009 12.75 5.75 13.009 5.75 14.25L5.75 16.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M9.125 4.5L6.875 4.5M9.125 7.125H6.875M9.125 9.75H6.875"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default BuildingIcon;
