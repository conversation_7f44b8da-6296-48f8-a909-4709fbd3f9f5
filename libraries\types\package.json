{"name": "@c-visitor/types", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libraries/types/dist", "main": "libraries/types/src/index.ts", "tsConfig": "libraries/types/tsconfig.lib.json", "format": ["esm"], "declarationRootDir": "libraries/types/src"}}}}, "dependencies": {}}