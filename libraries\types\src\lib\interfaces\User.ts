import { DateTracking } from '../shared/DateTracking.js';
import { RoleAttributes } from './Role.js';

/**
 * Interface cho thông tin người dùng
 */
export interface IUser extends UserAttributes, UserReference {}

export interface UserAttributes extends DateTracking {
  id: string;
  email: string;
  fullName: string;
  password: string;
  emailVerified: boolean;
  verificationToken?: string | undefined;
  verificationTokenExpiry?: Date | undefined;
  resetPasswordToken?: string | undefined;
  resetPasswordTokenExpiry?: Date | undefined;
  refreshToken?: string | undefined;
  active: boolean;
  createdAt?: Date | undefined;
  updatedAt?: Date | undefined;
}

export interface UserReference {
  roles: RoleAttributes[];
}
