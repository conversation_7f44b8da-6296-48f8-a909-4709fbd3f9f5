import { useState, useCallback } from 'react';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { toast } from 'sonner';
import { ProtectedLayout } from '@/components/layout';
import { useRequest } from '@/hooks/use-request';
import { useAuth } from '@/hooks/use-auth';
import { RequestStatus } from '@c-visitor/types';

// Import components
import { RequestHeader } from '@/components/request/request-header';
import { RequestInfoSummary } from '@/components/request/request-info-summary';
import { RequestTabs } from '@/components/request/request-tabs';
import { RejectDialog } from '@/components/request/reject-dialog';
import { ApproveDialog } from '@/components/request/approve-dialog';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

export const Route = createFileRoute('/app/requests/$id')({
  component: RequestDetailComponent,
});

function RequestDetailComponent() {
  const { id } = Route.useParams();
  const { user } = useAuth();
  const { useGetRequest, useChangeRequestStatus } = useRequest();
  const navigate = useNavigate();

  // Use the useGetRequest hook to leverage React Query's caching mechanism
  const { data: requestResponse, isLoading } = useGetRequest(id);

  // State for active tab
  const [tabActive, setTabActive] = useState('info');

  // Handle tab change - wrapped in useCallback to prevent unnecessary re-renders
  const handleTabChange = useCallback((value: string) => {
    setTabActive(value);
  }, []);

  // State for rejection dialog
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle reject reason change - wrapped in useCallback to prevent unnecessary re-renders
  const handleRejectReasonChange = useCallback((reason: string) => {
    setRejectReason(reason);
  }, []);

  // Handle dialog open/close - wrapped in useCallback to prevent unnecessary re-renders
  const handleRejectDialogChange = useCallback((open: boolean) => {
    setShowRejectDialog(open);
    if (!open) {
      setRejectReason(''); // Clear reason when closing dialog
    }
  }, []);

  // State for approve dialog
  const [showApproveDialog, setShowApproveDialog] = useState(false);

  // Handle approve dialog open/close - wrapped in useCallback to prevent unnecessary re-renders
  const handleApproveDialogChange = useCallback((open: boolean) => {
    setShowApproveDialog(open);
  }, []);

  // Change status mutation
  const changeStatusMutation = useChangeRequestStatus(id);

  // Extract request from response - references are embedded in request (simplified API)
  const selectedRequest = requestResponse || null;
  const references = selectedRequest?.references || [];

  // Handle approve request - wrapped in useCallback to prevent unnecessary re-renders
  const handleApprove = useCallback(async () => {
    if (!id) return;
    console.log('Approving request id:', id);
    try {
      setIsSubmitting(true);

      await changeStatusMutation.mutateAsync({
        status: RequestStatus.APPROVED,
        changeStatusBy: user?.id || user?.email || '',
      });

      toast.success('Request has been approved', {
        description: 'Approval successful',
        action: {
          label: 'Close',
          onClick: () => {},
        },
      });

      setShowApproveDialog(false);

      // Reload the page to display the new status
      window.location.reload();
    } catch (error) {
      console.error('Error approving request:', error);

      // More detailed error handling
      let errorMessage = 'Unable to approve the request';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast.error('An error occurred', {
        description: errorMessage,
        action: {
          label: 'Retry',
          onClick: () => handleApprove(),
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [id, changeStatusMutation, user]);

  // Handle reject button click - open dialog
  const handleRejectClick = useCallback(() => {
    setShowRejectDialog(true);
  }, []);

  // Handle approve button click - open dialog
  const handleApproveClick = useCallback(() => {
    setShowApproveDialog(true);
  }, []);

  // Handle reject request with reason
  const handleReject = useCallback(async () => {
    if (!id || !rejectReason.trim()) return;

    try {
      setIsSubmitting(true);

      // Send status, rejection reason, and rejector information
      await changeStatusMutation.mutateAsync({
        status: RequestStatus.REJECTED,
        reason: rejectReason.trim(), // Add rejection reason to body
        changeStatusBy: user?.id || user?.email || '', // Add rejector information
      });

      toast.success('Request has been rejected', {
        description: 'Rejection successful',
        action: {
          label: 'Close',
          onClick: () => {},
        },
      });

      // Close the dialog
      setShowRejectDialog(false);
      setRejectReason('');

      // Reload the page to display the new status
      window.location.reload();
    } catch (error) {
      console.error('Error rejecting request:', error);

      // More detailed error handling
      let errorMessage = 'Unable to reject the request';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast.error('An error occurred', {
        description: errorMessage,
        action: {
          label: 'Retry',
          onClick: () => handleReject(),
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [id, rejectReason, changeStatusMutation, user]);

  if (isLoading || !selectedRequest) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <p>Loading data...</p>
        </div>
      </ProtectedLayout>
    );
  }

  const handleBack = () => {
    navigate({ to: '/app/requests' });
  };

  return (
    <ProtectedLayout>
      <Breadcrumb className="text-xs pb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              onClick={handleBack}
              className="text-[#8f959e] cursor-pointer"
            >
              Access Registration List
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="text-[#67718e]" />
          <BreadcrumbItem>
            <BreadcrumbLink className="text-[#1f2329] cursor-default">
              Access Registration Details
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-6">
        {/* Header with action buttons */}
        <RequestHeader
          selectedRequest={selectedRequest}
          onReject={handleRejectClick}
          onApprove={handleApproveClick}
        />

        {/* Request summary information */}
        <RequestInfoSummary selectedRequest={selectedRequest} />

        {/* Tabs for detailed information */}
        <RequestTabs
          tabActive={tabActive}
          setTabActive={handleTabChange}
          selectedRequest={selectedRequest}
          references={references}
          isLoadingRecognitions={false}
        />

        {/* Reject dialog */}
        <RejectDialog
          open={showRejectDialog}
          onOpenChange={handleRejectDialogChange}
          rejectReason={rejectReason}
          setRejectReason={handleRejectReasonChange}
          isSubmitting={isSubmitting}
          onReject={handleReject}
        />

        {/* Approve dialog */}
        <ApproveDialog
          open={showApproveDialog}
          onOpenChange={handleApproveDialogChange}
          isSubmitting={isSubmitting}
          onApprove={handleApprove}
        />
      </div>
    </ProtectedLayout>
  );
}
