import { Request, Response, NextFunction } from 'express';
import { IServiceProvider } from './type.js';

export interface IHttpContext {
  request: Request;
  response: Response;
  next?: NextFunction;
  serviceProvider: IServiceProvider;
}

/**
 * Interface for classes that need to properly dispose of resources
 * Classes implementing this interface must release all resources in the dispose method
 */
export interface IDisposable {
  /**
   * Release all resources held by this instance
   * This might include:
   * - Clearing timers (setTimeout, setInterval)
   * - Closing connections (database, Redis, Kafka, etc.)
   * - Unregistering event listeners
   * - Closing file handles
   * - Releasing any other system resources
   *
   * @returns A promise that resolves when all resources have been released
   */
  dispose(): Promise<void>;
}

export type ServiceResponse<T = undefined> = {
  success: boolean;
  message?: string;
  data?: T;
};
