/**
 * @fileoverview JSON extensions to enhance JSON functionality
 *
 * This module extends the native JSON object with additional utility methods
 * for common JSON manipulation tasks. These extensions make JSON operations
 * more concise and readable throughout the application.
 *
 * @module @c-visitor/shared/lib/prototypes/JSON.prototype
 * <AUTHOR> Development Team
 * @version 1.0.0
 *
 * @example
 * // Import in your application's entry point
 * import '@c-visitor/shared';
 *
 * // Then use the extensions anywhere in your code
 * const obj = { name: "<PERSON>", age: 30 };
 * const safeJson = JSON.safeStringify(obj); // Same as JSON.stringify but handles circular references
 */

// Extend the JSON interface to include our new methods
declare global {
  interface JSON {
  /**
   * Safely stringifies an object, handling circular references
   *
   * Works like JSON.stringify but doesn't throw errors on circular references.
   * Instead, it replaces circular references with "[Circular]".
   *
   * @param value The value to convert to a JSON string
   * @param replacer A function that transforms the results or an array of strings and numbers that acts as an approved list for selecting the object properties that will be stringified
   * @param space Adds indentation, white space, and line break characters to the return-value JSON text to make it easier to read
   * @returns A JSON string representing the value, or undefined if the value cannot be serialized
   * @category Serialization
   * @example
   * ```typescript
   * const obj = { name: "John" };
   * obj.self = obj; // Circular reference
   *
   * // Regular JSON.stringify would throw an error
   * JSON.safeStringify(obj); // Returns '{"name":"John","self":"[Circular]"}'
   * ```
   */
  safeStringify(
    value: unknown,
    replacer?: (this: unknown, key: string, value: unknown) => unknown | (number | string)[] | null,
    space?: string | number
  ): string | undefined;

  /**
   * Safely parses a JSON string, with error handling
   *
   * Works like JSON.parse but doesn't throw errors on invalid JSON.
   * Instead, it returns the default value provided.
   *
   * @param text The JSON string to parse
   * @param defaultValue The default value to return if parsing fails (default: null)
   * @returns The parsed JSON value, or the default value if parsing fails
   * @category Deserialization
   * @example
   * ```typescript
   * // Regular JSON.parse would throw an error
   * JSON.safeParse('{"name":"John",}'); // Returns null (invalid JSON)
   * JSON.safeParse('{"name":"John",}', {}); // Returns {} (invalid JSON)
   * JSON.safeParse('{"name":"John"}'); // Returns { name: "John" }
   * ```
   */
  safeParse<T = unknown>(text: string, defaultValue?: T | null): T | null;

  /**
   * Validates if a string is valid JSON
   *
   * @param text The string to validate
   * @returns True if the string is valid JSON, false otherwise
   * @category Validation
   * @example
   * ```typescript
   * JSON.isValid('{"name":"John"}'); // Returns true
   * JSON.isValid('{"name":"John",}'); // Returns false (trailing comma)
   * JSON.isValid('not json'); // Returns false
   * ```
   */
  isValid(text: string): boolean;

  /**
   * Prettifies a JSON string or object with proper indentation
   *
   * @param input The JSON string or object to prettify
   * @param space The number of spaces to use for indentation (default: 2)
   * @returns A prettified JSON string
   * @category Formatting
   * @example
   * ```typescript
   * const obj = { name: "John", age: 30 };
   * JSON.prettify(obj);
   * // Returns:
   * // {
   * //   "name": "John",
   * //   "age": 30
   * // }
   *
   * JSON.prettify('{"name":"John","age":30}');
   * // Returns the same prettified output
   * ```
   */
  prettify(input: string | unknown, space?: number): string;

  /**
   * Minifies a JSON string by removing all whitespace
   *
   * @param input The JSON string to minify
   * @returns A minified JSON string
   * @category Formatting
   * @example
   * ```typescript
   * const prettyJson = `{
   *   "name": "John",
   *   "age": 30
   * }`;
   *
   * JSON.minify(prettyJson); // Returns '{"name":"John","age":30}'
   * ```
   */
  minify(input: string): string;
  }
}

// ===================================================
// Implementation of JSON extension methods
// ===================================================

/**
 * Safely stringify an object, handling circular references
 */
JSON.safeStringify = function(
  value: unknown,
  replacer?: (this: unknown, key: string, value: unknown) => unknown | (number | string)[] | null,
  space?: string | number
): string | undefined {
  try {
    const seen = new WeakSet();
    const safeReplacer = (key: string, value: unknown) => {
      // Handle circular references
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular]';
        }
        seen.add(value);
      }

      // Apply the original replacer if provided
      if (replacer) {
        if (typeof replacer === 'function') {
          return replacer.call(this, key, value);
        } else if (Array.isArray(replacer) && key !== '' && !(replacer as string[]).includes(key)) {
          return undefined;
        }
      }

      return value;
    };

    return JSON.stringify(value, safeReplacer as (key: string, value: unknown) => unknown, space);
  } catch (error) {
    // Use the logger instead of console.error
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error in JSON.safeStringify:', errorMessage);
    return undefined;
  }
};

/**
 * Safely parse a JSON string, with error handling
 */
JSON.safeParse = function<T = unknown>(text: string, defaultValue: T | null = null): T | null {
  try {
    return JSON.parse(text) as T;
  } catch {
    return defaultValue;
  }
};

/**
 * Validate if a string is valid JSON
 */
JSON.isValid = function(text: string): boolean {
  try {
    JSON.parse(text);
    return true;
  } catch {
    return false;
  }
};

/**
 * Prettify a JSON string or object with proper indentation
 */
JSON.prettify = function(input: string | unknown, space = 2): string {
  try {
    // If input is a string, parse it first
    const obj = typeof input === 'string' ? JSON.parse(input) : input;
    return JSON.stringify(obj, null, space);
  } catch {
    // If parsing fails, return the original input if it's a string
    return typeof input === 'string' ? input : String(input);
  }
};

/**
 * Minify a JSON string by removing all whitespace
 */
JSON.minify = function(input: string): string {
  try {
    return JSON.stringify(JSON.parse(input));
  } catch {
    // If parsing fails, return the original input
    return input;
  }
};

/**
 * Export an empty object to make this a module
 * This is required for TypeScript to recognize this file as a module
 * rather than a script file.
 */
export {};
