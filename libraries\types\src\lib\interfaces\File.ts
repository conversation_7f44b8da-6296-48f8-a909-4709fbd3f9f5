import { DateTracking } from '../shared/DateTracking.js';
import { Document } from '../shared/Document.js';

/**
 * Interface cho thông tin tập tin
 */
export interface IFile extends FileAttributes {}

export interface FileAttributes extends Omit<Document, 'id'>, FileReference, DateTracking {
  id?: string;
  filename: string;
  originalName?: string;
  mimeType: string;
  size: number;
  bucket: string;
  uploadedBy: string;
}

export interface FileReference {
  // References to other entities if needed
}
