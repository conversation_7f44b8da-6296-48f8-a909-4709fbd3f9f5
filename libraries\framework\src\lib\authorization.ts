import { Request, Response, NextFunction } from 'express';
import { handleAuthorization, IMiddleware } from './decorators.js';

/**
 * Middleware class for handling authorization based on the @Authorized decorator
 */
export class AuthorizationMiddleware implements IMiddleware {
  constructor(
    private readonly options: { roles?: string[]; required: boolean }
  ) {}

  /**
   * Middleware implementation that delegates to the handleAuthorization function
   */
  async use(req: Request, res: Response, next: NextFunction): Promise<void> {
    await handleAuthorization(req, res, next, this.options);
  }
}

/**
 * Factory function to create an authorization middleware instance
 * @param options Authorization options
 * @returns An instance of AuthorizationMiddleware
 */
export function CreateAuthorizationMiddleware(options: {
  roles?: string[];
  required: boolean;
}): AuthorizationMiddleware {
  return new AuthorizationMiddleware(options);
}

/**
 * Express middleware function for global authorization
 * This can be directly used with app.use()
 * @param options Authorization options
 * @returns Express middleware function
 */
export const GlobalAuthorizedMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Create middleware with required: false to not block requests without authorization
  const middleware = new AuthorizationMiddleware({ required: false });
  middleware.use(req, res, next);
};
