import { IRecognition } from '../interfaces/Recognition.js';
import { IReference } from '../interfaces/Reference.js';

export interface ReferenceWithRecognitionStats {
  reference: IReference;
  recognitionCount: number;
  firstCheckIn?: Date;
  lastCheckOut?: Date;
}

export interface GetRecognitionsResponse {
  references: ReferenceWithRecognitionStats[];
  recognitions: IRecognition[];
  total: number;
  pageIndex: number;
  pageSize: number;
  totalPages: number;
}
