import { useApiQuery } from './use-api-query';
import { IReference } from '@c-visitor/types';

// Types based on actual Reference entity
export interface GetReferencesParams {
  requestId?: string;
  email?: string;
  phone?: string;
  cardIdNumber?: string;
  fullName?: string;
}

export function useReferences() {
  // Get references by request ID
  const useGetReferencesByRequest = (requestId: string) => {
    return useApiQuery<IReference[]>(
      ['references', 'request', requestId],
      `/api/references/request/${requestId}`,
      undefined,
      {
        enabled: !!requestId,
      }
    );
  };

  // Get reference by ID
  const useGetReference = (id: string) => {
    return useApiQuery<IReference>(
      ['reference', id],
      `/api/references/${id}`,
      undefined,
      {
        enabled: !!id,
      }
    );
  };

  // Search references
  const useSearchReferences = (params?: GetReferencesParams) => {
    const queryParams: Record<string, any> = {};

    if (params?.requestId) queryParams.requestId = params.requestId;
    if (params?.email) queryParams.email = params.email;
    if (params?.phone) queryParams.phone = params.phone;
    if (params?.cardIdNumber) queryParams.cardIdNumber = params.cardIdNumber;
    if (params?.fullName) queryParams.fullName = params.fullName;

    // Create a stable query key
    const queryKey = ['references', 'search'];
    if (params?.requestId) queryKey.push(`requestId:${params.requestId}`);
    if (params?.email) queryKey.push(`email:${params.email}`);
    if (params?.phone) queryKey.push(`phone:${params.phone}`);
    if (params?.cardIdNumber) queryKey.push(`cardIdNumber:${params.cardIdNumber}`);
    if (params?.fullName) queryKey.push(`fullName:${params.fullName}`);

    return useApiQuery<IReference[]>(queryKey, '/api/references/search', queryParams);
  };

  return {
    useGetReferencesByRequest,
    useGetReference,
    useSearchReferences,
  };
}
