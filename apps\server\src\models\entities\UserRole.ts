import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';
import { DateTracking } from '@c-visitor/types';

// UserRole interface
export interface UserRoleAttributes extends DateTracking {
  userId: string;
  roleId: string;
}

// UserRole interface for Mongoose document
export interface UserRoleDocument extends Omit<UserRoleAttributes, 'id'>, Document {}

// UserRole schema for Mongoose
const UserRoleSchema = new Schema<UserRoleDocument>(
  {
    userId: {
      type: String,
      required: true,
      ref: 'User'
    },
    roleId: {
      type: String,
      required: true,
      ref: 'Role'
    },
  },
  { timestamps: true, collection: 'user_roles' },
);

// Create compound index for userId and roleId
UserRoleSchema.index({ userId: 1, roleId: 1 }, { unique: true });

// Create the UserRole model
const UserRoleModel = mongoose.model<UserRoleDocument>('UserRole', UserRoleSchema);

export default UserRoleModel;
