/**
 * @fileoverview Number prototype extensions to enhance number functionality
 *
 * This module extends the native Number prototype with additional utility methods
 * for common number manipulation tasks. These extensions make number operations
 * more concise and readable throughout the application.
 *
 * @module @c-visitor/shared/lib/prototypes/Number.prototype
 * <AUTHOR> Development Team
 * @version 1.0.0
 *
 * @example
 * // Import in your application's entry point
 * import '@c-visitor/shared';
 *
 * // Then use the extensions anywhere in your code
 * const formattedPrice = (123.45).formatCurrency('USD'); // "$123.45"
 * const isValid = (42).isBetween(1, 100); // true
 */

// Extend the Number interface to include our new methods
declare global {
  interface Number {
    /**
     * Formats the number as currency with the specified currency code
     *
     * Uses the Intl.NumberFormat API to format the number according to the
     * locale and currency conventions.
     *
     * @param currencyCode The ISO 4217 currency code (e.g., 'USD', 'EUR', 'JPY')
     * @param locale The locale to use for formatting (default: system locale)
     * @returns A formatted currency string
     * @category Formatting
     * @example
     * ```typescript
     * (1234.56).formatCurrency('USD'); // Returns "$1,234.56"
     * (1234.56).formatCurrency('EUR', 'de-DE'); // Returns "1.234,56 €"
     * (1234.56).formatCurrency('JPY'); // Returns "¥1,235" (JPY doesn't use decimal places)
     * ```
     */
    formatCurrency(currencyCode: string, locale?: string): string;

    /**
     * Formats the number with the specified number of decimal places
     *
     * @param decimalPlaces The number of decimal places to include (default: 2)
     * @param trailingZeros Whether to include trailing zeros (default: true)
     * @returns A formatted number string
     * @category Formatting
     * @example
     * ```typescript
     * (123.456).formatDecimal(2); // Returns "123.46"
     * (123).formatDecimal(2); // Returns "123.00"
     * (123).formatDecimal(2, false); // Returns "123"
     * ```
     */
    formatDecimal(decimalPlaces?: number, trailingZeros?: boolean): string;

    /**
     * Formats the number as a percentage
     *
     * @param decimalPlaces The number of decimal places to include (default: 0)
     * @returns A formatted percentage string
     * @category Formatting
     * @example
     * ```typescript
     * (0.1234).formatPercent(); // Returns "12%"
     * (0.1234).formatPercent(2); // Returns "12.34%"
     * (1.5).formatPercent(); // Returns "150%"
     * ```
     */
    formatPercent(decimalPlaces?: number): string;

    /**
     * Checks if the number is between the specified minimum and maximum values (inclusive)
     *
     * @param min The minimum value
     * @param max The maximum value
     * @returns True if the number is between min and max (inclusive)
     * @category Validation
     * @example
     * ```typescript
     * (5).isBetween(1, 10); // Returns true
     * (5).isBetween(5, 10); // Returns true (inclusive)
     * (5).isBetween(6, 10); // Returns false
     * ```
     */
    isBetween(min: number, max: number): boolean;

    /**
     * Checks if the number is even
     *
     * @returns True if the number is even
     * @category Validation
     * @example
     * ```typescript
     * (2).isEven(); // Returns true
     * (3).isEven(); // Returns false
     * ```
     */
    isEven(): boolean;

    /**
     * Checks if the number is odd
     *
     * @returns True if the number is odd
     * @category Validation
     * @example
     * ```typescript
     * (3).isOdd(); // Returns true
     * (2).isOdd(); // Returns false
     * ```
     */
    isOdd(): boolean;

    /**
     * Checks if the number is a positive integer
     *
     * @returns True if the number is a positive integer
     * @category Validation
     * @example
     * ```typescript
     * (5).isPositiveInteger(); // Returns true
     * (0).isPositiveInteger(); // Returns false
     * (-5).isPositiveInteger(); // Returns false
     * (5.5).isPositiveInteger(); // Returns false
     * ```
     */
    isPositiveInteger(): boolean;

    /**
     * Checks if the number is a negative integer
     *
     * @returns True if the number is a negative integer
     * @category Validation
     * @example
     * ```typescript
     * (-5).isNegativeInteger(); // Returns true
     * (0).isNegativeInteger(); // Returns false
     * (5).isNegativeInteger(); // Returns false
     * (-5.5).isNegativeInteger(); // Returns false
     * ```
     */
    isNegativeInteger(): boolean;

    /**
     * Checks if the number is an integer
     *
     * @returns True if the number is an integer
     * @category Validation
     * @example
     * ```typescript
     * (5).isInteger(); // Returns true
     * (-5).isInteger(); // Returns true
     * (0).isInteger(); // Returns true
     * (5.5).isInteger(); // Returns false
     * ```
     */
    isInteger(): boolean;

    /**
     * Rounds the number to the specified number of decimal places
     *
     * @param decimalPlaces The number of decimal places (default: 0)
     * @returns The rounded number
     * @category Math
     * @example
     * ```typescript
     * (123.456).round(); // Returns 123
     * (123.456).round(2); // Returns 123.46
     * (123.45).round(1); // Returns 123.5
     * ```
     */
    round(decimalPlaces?: number): number;

    /**
     * Converts the number to a padded string with leading zeros
     *
     * @param length The desired length of the resulting string
     * @returns A string representation of the number with leading zeros
     * @category Formatting
     * @example
     * ```typescript
     * (5).toPaddedString(2); // Returns "05"
     * (123).toPaddedString(5); // Returns "00123"
     * (1234).toPaddedString(2); // Returns "1234" (no truncation)
     * ```
     */
    toPaddedString(length: number): string;

    /**
     * Clamps the number between the specified minimum and maximum values
     *
     * @param min The minimum value
     * @param max The maximum value
     * @returns The clamped number
     * @category Math
     * @example
     * ```typescript
     * (5).clamp(1, 10); // Returns 5
     * (0).clamp(1, 10); // Returns 1
     * (15).clamp(1, 10); // Returns 10
     * ```
     */
    clamp(min: number, max: number): number;

    /**
     * Converts the number to a compact string representation
     *
     * @param locale The locale to use for formatting (default: system locale)
     * @returns A compact string representation of the number
     * @category Formatting
     * @example
     * ```typescript
     * (1234).toCompactString(); // Returns "1.2K"
     * (1234567).toCompactString(); // Returns "1.2M"
     * (1234567890).toCompactString(); // Returns "1.2B"
     * ```
     */
    toCompactString(locale?: string): string;

    /**
     * Converts the number to a string with ordinal suffix (1st, 2nd, 3rd, etc.)
     *
     * @returns A string with ordinal suffix
     * @category Formatting
     * @example
     * ```typescript
     * (1).toOrdinal(); // Returns "1st"
     * (2).toOrdinal(); // Returns "2nd"
     * (3).toOrdinal(); // Returns "3rd"
     * (4).toOrdinal(); // Returns "4th"
     * (21).toOrdinal(); // Returns "21st"
     * ```
     */
    toOrdinal(): string;

    /**
     * Converts the number to Roman numerals
     *
     * @returns A string of Roman numerals
     * @category Conversion
     * @example
     * ```typescript
     * (5).toRoman(); // Returns "V"
     * (9).toRoman(); // Returns "IX"
     * (2023).toRoman(); // Returns "MMXXIII"
     * ```
     */
    toRoman(): string;

    /**
     * Converts the number to a human-readable file size string
     *
     * @param binary Whether to use binary (1024) or decimal (1000) units (default: true)
     * @returns A human-readable file size string
     * @category Formatting
     * @example
     * ```typescript
     * (1024).toFileSize(); // Returns "1 KiB"
     * (1500).toFileSize(); // Returns "1.46 KiB"
     * (1500).toFileSize(false); // Returns "1.5 KB"
     * (1500000).toFileSize(); // Returns "1.43 MiB"
     * ```
     */
    toFileSize(binary?: boolean): string;
  }
}

// ===================================================
// Implementation of Number prototype extension methods
// ===================================================

/**
 * Format number as currency
 */
Number.prototype.formatCurrency = function(currencyCode: string, locale = 'en-US') {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode
  }).format(this as unknown as number);
};

/**
 * Format number with decimal places
 */
Number.prototype.formatDecimal = function(decimalPlaces = 2, trailingZeros = true) {
  const num = this as unknown as number;
  if (trailingZeros) {
    return num.toFixed(decimalPlaces);
  }
  return num.toFixed(decimalPlaces).replace(/\.?0+$/, '');
};

/**
 * Format number as percentage
 */
Number.prototype.formatPercent = function(decimalPlaces = 0) {
  const num = this as unknown as number;
  return (num * 100).toFixed(decimalPlaces) + '%';
};

/**
 * Check if number is between min and max (inclusive)
 */
Number.prototype.isBetween = function(min: number, max: number) {
  const num = this as unknown as number;
  return num >= min && num <= max;
};

/**
 * Check if number is even
 */
Number.prototype.isEven = function() {
  const num = this as unknown as number;
  return num % 2 === 0;
};

/**
 * Check if number is odd
 */
Number.prototype.isOdd = function() {
  const num = this as unknown as number;
  return num % 2 !== 0;
};

/**
 * Check if number is a positive integer
 */
Number.prototype.isPositiveInteger = function() {
  const num = this as unknown as number;
  return Number.isInteger(num) && num > 0;
};

/**
 * Check if number is a negative integer
 */
Number.prototype.isNegativeInteger = function() {
  const num = this as unknown as number;
  return Number.isInteger(num) && num < 0;
};

/**
 * Check if number is an integer
 */
Number.prototype.isInteger = function() {
  const num = this as unknown as number;
  return Number.isInteger(num);
};

/**
 * Round number to specified decimal places
 */
Number.prototype.round = function(decimalPlaces = 0) {
  const num = this as unknown as number;
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(num * factor) / factor;
};

/**
 * Convert number to padded string with leading zeros
 */
Number.prototype.toPaddedString = function(length: number) {
  const num = this as unknown as number;
  return num.toString().padStart(length, '0');
};

/**
 * Clamp number between min and max values
 */
Number.prototype.clamp = function(min: number, max: number) {
  const num = this as unknown as number;
  return Math.min(Math.max(num, min), max);
};

/**
 * Convert number to compact string representation
 */
Number.prototype.toCompactString = function(locale = 'en-US') {
  return new Intl.NumberFormat(locale, {
    notation: 'compact',
    compactDisplay: 'short'
  }).format(this as unknown as number);
};

/**
 * Convert number to string with ordinal suffix
 */
Number.prototype.toOrdinal = function() {
  const num = this as unknown as number;
  const j = num % 10;
  const k = num % 100;

  if (j === 1 && k !== 11) {
    return num + 'st';
  }
  if (j === 2 && k !== 12) {
    return num + 'nd';
  }
  if (j === 3 && k !== 13) {
    return num + 'rd';
  }
  return num + 'th';
};

/**
 * Convert number to Roman numerals
 */
Number.prototype.toRoman = function() {
  const num = Math.floor(this as unknown as number);
  if (num <= 0 || num > 3999) {
    return num.toString();
  }

  const romanNumerals = [
    ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX'],
    ['', 'X', 'XX', 'XXX', 'XL', 'L', 'LX', 'LXX', 'LXXX', 'XC'],
    ['', 'C', 'CC', 'CCC', 'CD', 'D', 'DC', 'DCC', 'DCCC', 'CM'],
    ['', 'M', 'MM', 'MMM']
  ];

  const digits = num.toString().split('').reverse();
  let roman = '';

  for (let i = 0; i < digits.length; i++) {
    roman = romanNumerals[i][parseInt(digits[i])] + roman;
  }

  return roman;
};

/**
 * Convert number to human-readable file size
 */
Number.prototype.toFileSize = function(binary = true) {
  const bytes = this as unknown as number;
  const base = binary ? 1024 : 1000;
  const units = binary
    ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB']
    : ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  if (bytes === 0) {
    return '0 B';
  }

  const exponent = Math.min(Math.floor(Math.log(bytes) / Math.log(base)), units.length - 1);
  const size = bytes / Math.pow(base, exponent);
  const formattedSize = exponent === 0 ? size.toString() : size.toFixed(2);

  return `${formattedSize} ${units[exponent]}`;
};

/**
 * Export an empty object to make this a module
 * This is required for TypeScript to recognize this file as a module
 * rather than a script file.
 */
export {};
