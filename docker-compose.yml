services:
  # Web frontend service
  web:
    image: ghcr.io/csmart-cloud/c-visitor-web:sha-46c13db
    container_name: c-visitor-web
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - API_URL=https://c-visitor.csmart.cloud
      - AUTH_COOKIE_NAME=
      - AUTH_COOKIE_MAX_AGE=
    networks:
      - c-visitor-network
    depends_on:
      - server

  # Server backend service
  server:
    image: ghcr.io/csmart-cloud/c-visitor-server:sha-46c13db
    container_name: c-visitor-server
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5000
      - HOST=localhost
      - API_PREFIX=/api
      - DB_CONNECTION=mongodb+srv://admin:<EMAIL>/c-visitor_db
      - JWT_SECRET=c-visitor-secret-key
      - JWT_EXPIRATION=86400
      - LOG_LEVEL=info
    ports:
      - "5000:5000"
    networks:
      - c-visitor-network

  # Nginx service for routing
  nginx:
    image: nginx:alpine
    container_name: c-visitor-nginx
    restart: unless-stopped
    ports:
      - "180:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - web
      - server
    networks:
      - c-visitor-network

networks:
  c-visitor-network:
    driver: bridge
