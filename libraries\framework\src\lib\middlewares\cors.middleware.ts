import { Request, Response, NextFunction } from 'express';
import { IMiddleware } from '../decorators/middleware.decorator.js';

/**
 * CORS configuration options
 */
export interface CorsOptions {
  /**
   * Configures the Access-Control-Allow-Origin CORS header
   * Can be a string, RegExp, or an array of strings/RegExp
   * Default: '*'
   */
  origin?: string | RegExp | (string | RegExp)[] | boolean;

  /**
   * Configures the Access-Control-Allow-Methods CORS header
   * Default: 'GET,HEAD,PUT,PATCH,POST,DELETE'
   */
  methods?: string | string[];

  /**
   * Configures the Access-Control-Allow-Headers CORS header
   * Default: 'Content-Type, Authorization'
   */
  allowedHeaders?: string | string[];

  /**
   * Configures the Access-Control-Expose-Headers CORS header
   * Default: none
   */
  exposedHeaders?: string | string[];

  /**
   * Configures the Access-Control-Allow-Credentials CORS header
   * Default: false
   */
  credentials?: boolean;

  /**
   * Configures the Access-Control-Max-Age CORS header
   * Default: 86400 (24 hours)
   */
  maxAge?: number;

  /**
   * Whether to enable CORS preflight requests
   * Default: true
   */
  preflightContinue?: boolean;

  /**
   * Pass the CORS preflight response to the next handler
   * Default: false
   */
  optionsSuccessStatus?: number;
}

/**
 * Default CORS options
 */
const defaultCorsOptions: CorsOptions = {
  origin: '*',
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  allowedHeaders: 'Content-Type, Authorization',
  credentials: false,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

/**
 * CORS middleware implementation
 */
export class CorsMiddleware implements IMiddleware {
  private readonly options: CorsOptions;

  constructor(options: CorsOptions = {}) {
    this.options = { ...defaultCorsOptions, ...options };
  }

  /**
   * Handle CORS request
   */
  public use(req: Request, res: Response, next: NextFunction): void {
    const origin = this.getOrigin(req);
    const requestMethod = req.method && req.method.toUpperCase && req.method.toUpperCase();

    // Set CORS headers
    this.configureOrigin(req, res, origin);
    this.configureMethods(req, res);
    this.configureCredentials(req, res);
    this.configureAllowedHeaders(req, res);
    this.configureExposedHeaders(req, res);
    this.configureMaxAge(req, res);

    // Handle preflight request
    if (requestMethod === 'OPTIONS') {
      if (this.options.preflightContinue) {
        next();
      } else {
        res.statusCode = this.options.optionsSuccessStatus || 204;
        res.setHeader('Content-Length', '0');
        res.end();
      }
    } else {
      next();
    }
  }

  /**
   * Configure the Access-Control-Allow-Origin header
   */
  private configureOrigin(_req: Request, res: Response, origin: string | undefined): void {
    if (!origin || this.options.origin === '*') {
      res.setHeader('Access-Control-Allow-Origin', '*');
    } else if (this.isOriginAllowed(origin, this.options.origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Vary', 'Origin');
    }
  }

  /**
   * Configure the Access-Control-Allow-Methods header
   */
  private configureMethods(_req: Request, res: Response): void {
    const methods = Array.isArray(this.options.methods)
      ? this.options.methods.join(',')
      : this.options.methods;

    res.setHeader('Access-Control-Allow-Methods', methods || 'GET,HEAD,PUT,PATCH,POST,DELETE');
  }

  /**
   * Configure the Access-Control-Allow-Credentials header
   */
  private configureCredentials(_req: Request, res: Response): void {
    if (this.options.credentials === true) {
      res.setHeader('Access-Control-Allow-Credentials', 'true');
    }
  }

  /**
   * Configure the Access-Control-Allow-Headers header
   */
  private configureAllowedHeaders(req: Request, res: Response): void {
    let allowedHeaders = this.options.allowedHeaders;

    if (!allowedHeaders) {
      allowedHeaders = req.headers['access-control-request-headers'] as string || 'Content-Type, Authorization';
      res.setHeader('Vary', 'Access-Control-Request-Headers');
    }

    if (allowedHeaders) {
      res.setHeader(
        'Access-Control-Allow-Headers',
        Array.isArray(allowedHeaders) ? allowedHeaders.join(',') : allowedHeaders
      );
    }
  }

  /**
   * Configure the Access-Control-Expose-Headers header
   */
  private configureExposedHeaders(_req: Request, res: Response): void {
    const exposedHeaders = this.options.exposedHeaders;

    if (exposedHeaders) {
      res.setHeader(
        'Access-Control-Expose-Headers',
        Array.isArray(exposedHeaders) ? exposedHeaders.join(',') : exposedHeaders
      );
    }
  }

  /**
   * Configure the Access-Control-Max-Age header
   */
  private configureMaxAge(_req: Request, res: Response): void {
    const maxAge = this.options.maxAge;

    if (maxAge) {
      res.setHeader('Access-Control-Max-Age', maxAge.toString());
    }
  }

  /**
   * Get the origin from the request
   */
  private getOrigin(req: Request): string | undefined {
    return req.headers.origin;
  }

  /**
   * Check if the origin is allowed
   */
  private isOriginAllowed(origin: string, allowedOrigin: string | RegExp | (string | RegExp)[] | boolean | undefined): boolean {
    if (Array.isArray(allowedOrigin)) {
      for (const pattern of allowedOrigin) {
        if (this.matchOrigin(origin, pattern)) {
          return true;
        }
      }
      return false;
    } else if (typeof allowedOrigin === 'string') {
      return origin === allowedOrigin;
    } else if (allowedOrigin instanceof RegExp) {
      return allowedOrigin.test(origin);
    } else if (typeof allowedOrigin === 'boolean') {
      return allowedOrigin;
    }

    return false;
  }

  /**
   * Match origin against pattern
   */
  private matchOrigin(origin: string, pattern: string | RegExp): boolean {
    if (pattern instanceof RegExp) {
      return pattern.test(origin);
    } else {
      return origin === pattern;
    }
  }
}

/**
 * Create CORS middleware with the specified options
 * @param options CORS options
 * @returns Express middleware function
 */
export function createCorsMiddleware(options?: CorsOptions): (req: Request, res: Response, next: NextFunction) => void {
  const middleware = new CorsMiddleware(options);
  return (req: Request, res: Response, next: NextFunction) => middleware.use(req, res, next);
}
