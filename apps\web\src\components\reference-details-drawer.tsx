'use client';

import { useCallback, useEffect, useState } from 'react';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { CalendarIcon, X } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@/components/ui/popover';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { DataTable } from '@/components/shared/data-table';
import { IReference } from '@c-visitor/types';
import { useRecognition } from '@/hooks/use-recognition';
import { getImageUrl } from '@/utils/file-utils';

interface ReferenceDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  referenceData: IReference | null;
}

export function ReferenceDetailsDrawer({
  isOpen,
  onClose,
  referenceData,
}: ReferenceDetailsDrawerProps) {
  // Set default date range to current month
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  const [activeTab, setActiveTab] = useState('info');
  const [date, setDate] = useState<DateRange | undefined>({
    from: firstDayOfMonth,
    to: lastDayOfMonth,
  });

  // Pagination state
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(5);

  // Get the recognition hook
  const { useGetUserRecognitions } = useRecognition();

  // Get user ID from reference mappings (use first mapping if available)
  const userId = referenceData?.mappings?.[0] || '';

  // Fetch recognition data using the hook
  const {
    data: recognitionData,
    isLoading,
    error,
  } = useGetUserRecognitions({
    userId,
    pageIndex,
    pageSize,
    fromDate: date?.from,
    toDate: date?.to,
  });

  // Extract data from the API response (axios already extracts response.data.data)
  const recognitionHistory = recognitionData?.recognitions || [];
  const totalPages = recognitionData?.totalPages || 1;

  // Debug logging
  useEffect(() => {
    if (recognitionData) {
      console.log('Recognition API Response:', recognitionData);
      console.log('Recognition History:', recognitionHistory);
      console.log('User ID:', userId);
    }
  }, [recognitionData, recognitionHistory, userId]);

  // Columns for recognition history
  const recognitionHistoryColumns = [
    {
      accessorKey: 'createdAt',
      header: 'Time',
      cell: ({ row }: any) => (
        <div className="text-sm">
          {row.original.createdAt
            ? format(new Date(row.original.createdAt), 'dd/MM/yyyy HH:mm')
            : 'N/A'}
        </div>
      ),
    },
    {
      accessorKey: 'deviceId',
      header: 'Device',
      cell: ({ row }: any) => (
        <div className="text-sm">{row.original.deviceId || 'N/A'}</div>
      ),
    },
    {
      accessorKey: 'recognizeStatus',
      header: 'Status',
      cell: ({ row }: any) => (
        <div className="text-sm">
          {row.original.recognizeStatus === 1 ? 'Recognized' : 'Unknown'}
        </div>
      ),
    },
    {
      accessorKey: 'temperature',
      header: 'Temperature',
      cell: ({ row }: any) => (
        <div className="text-sm">
          {row.original.temperature ? `${row.original.temperature}°C` : 'N/A'}
        </div>
      ),
    },
  ];

  // Handle date range change
  const handleDateRangeChange = useCallback(
    (newDateRange: DateRange | undefined) => {
      setDate(newDateRange);
      setPageIndex(1);
      // The useGetUserRecognitions hook will automatically refetch when date changes
    },
    [],
  );

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPageIndex(newPage);
    // The useGetUserRecognitions hook will automatically refetch when pageIndex changes
  }, []);

  // Handle page size change
  const handlePageSizeChange = useCallback((newSize: string) => {
    const size = parseInt(newSize);
    setPageSize(size);
    setPageIndex(1);
    // The useGetUserRecognitions hook will automatically refetch when pageSize changes
  }, []);

  // Reset pagination when drawer opens or reference changes
  useEffect(() => {
    if (isOpen && referenceData?.id) {
      setPageIndex(1);
    }
  }, [isOpen, referenceData?.id]);

  return (
    <Drawer
      open={isOpen}
      onOpenChange={(open) => {
        if (open === false) {
          onClose();
        }
      }}
      direction="right"
      dismissible={true}
      modal={true}
    >
      {isOpen && (
        <DrawerContent
          className="h-full w-full w-[30vw] min-w-[480px] right-0 left-auto border-l shadow-lg select-text"
          style={{
            userSelect: 'text',
            WebkitUserSelect: 'text',
            MozUserSelect: 'text',
            msUserSelect: 'text',
            touchAction: 'pan-y',
          }}
          onPointerDownOutside={() => onClose()}
          onDrag={(e) => e.preventDefault()}
          onDragStart={(e) => e.preventDefault()}
        >
          <DrawerHeader className="border-b">
            <div className="flex items-center justify-between">
              <DrawerTitle>Visitor Details</DrawerTitle>
              <DrawerClose asChild>
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </DrawerHeader>

          <div
            className="flex-1 overflow-auto p-4 select-text"
            style={{
              touchAction: 'pan-y',
              userSelect: 'text',
              WebkitUserSelect: 'text',
              MozUserSelect: 'text',
              msUserSelect: 'text',
            }}
            onTouchMove={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
          >
            {!referenceData ? (
              <div className="flex items-center justify-center h-full">
                <p>No data</p>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2">
                  <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative gap-2">
                    {referenceData.avatar ? (
                      <img
                        src={getImageUrl(referenceData.avatar)}
                        alt={referenceData.fullName}
                        className="flex-grow-0 flex-shrink-0 w-[46px] h-[46px] rounded-[50px] object-cover"
                      />
                    ) : (
                      <div className="flex-grow-0 flex-shrink-0 w-[46px] h-[46px] rounded-[50px] bg-[#008FD31A] text-[#008FD3] flex items-center justify-center">
                        <span className="text-sm font-bold">
                          {(() => {
                            const words =
                              referenceData.fullName
                                ?.split(' ')
                                .filter(Boolean) || [];
                            if (words.length === 1) {
                              return words[0].slice(0, 2).toUpperCase();
                            } else if (words.length > 1) {
                              return (
                                words[0][0] + words[words.length - 1][0]
                              ).toUpperCase();
                            }
                            return 'U';
                          })()}
                        </span>
                      </div>
                    )}
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-1">
                      <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4">
                        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1">
                          <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-[#081f41]">
                            {referenceData.fullName}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0">
                        <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1.5">
                          <svg
                            width={14}
                            height={14}
                            viewBox="0 0 14 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M2.9165 5.25033C2.9165 3.32542 2.9165 2.36297 3.51449 1.76498C4.11248 1.16699 5.07494 1.16699 6.99984 1.16699C8.92474 1.16699 9.88719 1.16699 10.4852 1.76498C11.0832 2.36297 11.0832 3.32542 11.0832 5.25033V8.75033C11.0832 10.6752 11.0832 11.6377 10.4852 12.2357C9.88719 12.8337 8.92474 12.8337 6.99984 12.8337C5.07494 12.8337 4.11248 12.8337 3.51449 12.2357C2.9165 11.6377 2.9165 10.6752 2.9165 8.75033V5.25033Z"
                              stroke="#73787E"
                            />
                            <path d="M6.4165 11.083H7.58317" stroke="#73787E" />
                            <path
                              d="M5.25 1.16699L5.30192 1.4785C5.41443 2.15358 5.47069 2.49112 5.70219 2.69652C5.94369 2.91077 6.28608 2.91699 7 2.91699C7.71392 2.91699 8.05631 2.91078 8.29781 2.69652C8.52931 2.49112 8.58557 2.15358 8.69808 1.4785L8.75 1.16699"
                              stroke="#73787E"
                            />
                          </svg>
                          <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#73787e]">
                            {referenceData.phone}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <Tabs defaultValue="info" className="w-full">
                    <TabsList className="flex justify-start w-full p-0 m-0 bg-transparent border-0 border-none mb-4">
                      <TabsTrigger
                        onClick={() => setActiveTab('info')}
                        value="info"
                        className="cursor-pointer flex justify-start items-center gap-1.5 px-5 py-3 rounded-none border-t-0 border-r-0 border-l-0 data-[state=active]:border-b-[3px] data-[state=active]:border-[#008fd3] data-[state=active]:text-[#008fd3] data-[state=inactive]:text-[#8f959e] data-[state=active]:shadow-none bg-transparent"
                      >
                        <span className="text-sm font-semibold">
                          General Information
                        </span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="entryHistory"
                        onClick={() => setActiveTab('entryHistory')}
                        className="cursor-pointer flex justify-start items-center gap-1.5 px-5 py-3 rounded-none border-t-0 border-r-0 border-l-0 data-[state=active]:border-b-[3px] data-[state=active]:border-[#008fd3] data-[state=active]:text-[#008fd3] data-[state=inactive]:text-[#8f959e] data-[state=active]:shadow-none bg-transparent"
                      >
                        <span className="text-sm font-semibold">
                          Recognition History
                        </span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="info" className="select-text">
                      <Card className="select-text !border-none !shadow-none py-0 px-2">
                        <CardContent
                          className="select-text p-0"
                          style={{ userSelect: 'text' }}
                        >
                          <div className="space-y-4">
                            {/* Email */}
                            <div className="flex gap-4">
                              <span className="w-[100px] text-sm text-[#1f2329]/50">
                                Email
                              </span>
                              <span className="text-sm">
                                {referenceData.email}
                              </span>
                            </div>

                            {/* Organization */}
                            <div className="flex gap-4">
                              <span className="w-[100px] text-sm text-[#1f2329]/50">
                                Organization:
                              </span>
                              <span className="text-sm">
                                {referenceData.unit}
                              </span>
                            </div>

                            {/* ID Number */}
                            <div className="flex gap-4">
                              <span className="w-[100px] text-sm text-[#1f2329]/50">
                                ID number:
                              </span>
                              <span className="text-sm">
                                {referenceData.cardIdNumber}
                              </span>
                            </div>

                            {/* ID Card Images */}
                            <div className="flex flex-col gap-4">
                              <span className="w-[100px] text-sm text-[#1f2329]/50">
                                ID images:
                              </span>
                              <div className="flex space-x-2">
                                {referenceData.cardIdFront && (
                                  <img
                                    src={getImageUrl(referenceData.cardIdFront)}
                                    alt="ID Card Front"
                                    className="w-full h-[150px] object-contain border rounded-md overflow-hidden"
                                  />
                                )}
                                {referenceData.cardIdBack && (
                                  <img
                                    src={getImageUrl(referenceData.cardIdBack)}
                                    alt="ID Card Back"
                                    className="w-full h-[150px] object-contain border rounded-md overflow-hidden"
                                  />
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="entryHistory" className="select-text">
                      <Card className="select-text !border-none !shadow-none p-0">
                        <CardContent
                          className="select-text p-0"
                          style={{ userSelect: 'text' }}
                        >
                          <div className="flex justify-end items-center flex-grow-0 flex-shrink-0">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  id="date"
                                  variant="outline"
                                  className={cn(
                                    'w-[205px] justify-start text-left font-normal',
                                    !date && 'text-muted-foreground',
                                  )}
                                >
                                  {date?.from ? (
                                    date.to ? (
                                      <>
                                        {format(date.from, 'dd/MM/yyyy')} -{' '}
                                        {format(date.to, 'dd/MM/yyyy')}
                                      </>
                                    ) : (
                                      format(date.from, 'dd/MM/yyyy')
                                    )
                                  ) : (
                                    <span>Pick date</span>
                                  )}
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0 z-[9999]"
                                align="start"
                              >
                                <Calendar
                                  mode="range"
                                  defaultMonth={date?.from}
                                  selected={date}
                                  onSelect={(newDateRange) => {
                                    handleDateRangeChange(newDateRange);
                                  }}
                                  numberOfMonths={2}
                                  className="bg-background rounded-lg z-100 border"
                                />
                              </PopoverContent>
                            </Popover>
                          </div>

                          {/* Recognition History List */}
                          <div className="space-y-4">
                            <div className="relative">
                              {isLoading && (
                                <div className="absolute inset-0 flex justify-center items-center bg-white/80 z-10">
                                  <p>Loading...</p>
                                </div>
                              )}

                              {error && (
                                <div className="flex justify-center items-center py-8">
                                  <p className="text-red-500">
                                    Error loading recognition history
                                  </p>
                                </div>
                              )}

                              {!error &&
                              recognitionHistory.length === 0 &&
                              !isLoading ? (
                                <div className="flex justify-center items-center py-8">
                                  <p>No recognition history found</p>
                                </div>
                              ) : (
                                !error && (
                                  <DataTable
                                    columns={recognitionHistoryColumns}
                                    data={recognitionHistory}
                                    pageCount={totalPages}
                                    pageSize={pageSize}
                                    pageIndex={pageIndex ? pageIndex - 1 : 0}
                                    onPaginationChange={(
                                      newPageIndex: number,
                                      newPageSize: number,
                                    ) => {
                                      if (newPageSize !== pageSize) {
                                        handlePageSizeChange(
                                          newPageSize.toString(),
                                        );
                                      } else if (
                                        newPageIndex !==
                                        (pageIndex ? pageIndex - 1 : 0)
                                      ) {
                                        handlePageChange(newPageIndex + 1);
                                      }
                                    }}
                                    sizeChanger={true}
                                    manualPagination={true}
                                    className="!border-none [&_tr]:!border-0 [&_thead]:!hidden"
                                  />
                                )
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            )}
          </div>
        </DrawerContent>
      )}
    </Drawer>
  );
}
