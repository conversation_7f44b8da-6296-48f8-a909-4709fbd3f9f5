import 'reflect-metadata';
import { Constructable, instances, ServiceIdentifier } from './decorators.js';
import { Container } from './container.js';
import { logger } from './logger.js';

// Metadata keys for dependency injection
export const INJECT_PARAM_METADATA_KEY = 'di:inject:params';
export const INJECT_PROP_METADATA_KEY = 'di:inject:props';

/**
 * Decorator for injecting dependencies into class properties or constructor parameters
 * @param type The type to inject
 * @returns A decorator that can be used for both property and parameter injection
 */
export function Inject<T>(
  type: ServiceIdentifier<T>
): PropertyDecorator & ParameterDecorator {
  return function (
    target: any,
    propertyKey?: string | symbol,
    parameterIndex?: number
  ): void {
    // For constructor injection (propertyKey is undefined for constructors)
    if (propertyKey === undefined && parameterIndex !== undefined) {
      // For constructor parameters, we need to handle both class constructors and prototype objects
      let targetClass: any;

      if (typeof target === 'function') {
        // If target is a constructor function (static method)
        targetClass = target;
      } else {
        // If target is a prototype (instance method)
        targetClass = target.constructor;
      }

      // Get or create the metadata array for storing parameter types
      const injectMetadata =
        Reflect.getMetadata(INJECT_PARAM_METADATA_KEY, targetClass) || [];

      // Store the dependency type at the parameter index
      injectMetadata[parameterIndex] = type;

      // Update the metadata on both the constructor and prototype to be safe
      Reflect.defineMetadata(
        INJECT_PARAM_METADATA_KEY,
        injectMetadata,
        targetClass
      );

      // If we have a prototype, also store on the prototype
      if (targetClass.prototype) {
        Reflect.defineMetadata(
          INJECT_PARAM_METADATA_KEY,
          injectMetadata,
          targetClass.prototype
        );
      }

      // Log for debugging
      logger.debug(
        `Registered parameter injection for ${
          targetClass.name
        } at index ${parameterIndex} with type ${
          typeof type === 'string' ? type : type.name
        }`
      );

      // Also register the target class with the container if not already registered
      if (!instances.has(targetClass)) {
        instances.set(targetClass, {});
      }
    }
    // For property injection (propertyKey is defined)
    else if (propertyKey !== undefined) {
      // Get the constructor of the class
      const constructor = target.constructor;

      // Get or create the metadata map for storing property injections
      const propsMetadata =
        Reflect.getMetadata(INJECT_PROP_METADATA_KEY, constructor) || {};

      // Store the type to inject for this property
      propsMetadata[propertyKey.toString()] = type;

      // Update the metadata
      Reflect.defineMetadata(
        INJECT_PROP_METADATA_KEY,
        propsMetadata,
        constructor
      );

      // Register the class with the container if not already registered
      if (!instances.has(constructor)) {
        instances.set(constructor, {});

        // Add a hook to initialize property injections after instance creation
        const originalGet = Container.get;
        Container.get = function <T>(
          serviceIdentifier: ServiceIdentifier<T>
        ): T {
          const instance = originalGet.call(Container, serviceIdentifier) as T;

          // If this is the class we're decorating, initialize its properties
          if (instance && serviceIdentifier === constructor) {
            initializeInjectedProperties(instance, constructor);
          }

          return instance;
        };
      }
    }
  };
}

/**
 * Initialize injected properties on an instance
 * @param instance The instance to initialize properties on
 * @param constructor The constructor of the instance
 */
function initializeInjectedProperties(
  instance: any,
  constructor: Constructable
): void {
  // Get the metadata for property injections
  const propsMetadata = Reflect.getMetadata(
    INJECT_PROP_METADATA_KEY,
    constructor
  );
  if (!propsMetadata) return;

  // For each property, define a getter that resolves the dependency
  for (const [propKey, type] of Object.entries(propsMetadata)) {
    // Skip if the property is already defined
    if (instance[propKey] !== undefined) continue;

    // Define the property with a getter
    Object.defineProperty(instance, propKey, {
      configurable: true,
      enumerable: true,
      get: function () {
        try {
          // Get the dependency from the container
          return Container.get(type as ServiceIdentifier);
        } catch (error) {
          logger.error(
            `Error injecting ${propKey} in ${constructor.name}:`,
            { error }
          );
          return undefined;
        }
      },
    });
  }
}
