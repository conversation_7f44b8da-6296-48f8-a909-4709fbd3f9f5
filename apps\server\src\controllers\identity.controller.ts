import {
  RefreshTokenExecute,
  VerifyEmailExecute,
  ForgotPasswordExecute,
  ResetPasswordExecute,
} from '@/models/executes/identity.execute';
import { IdentityService } from '@/services/identity.service';
import {
  Controller,
  ControllerBase,
  Inject,
  HttpPost,
  HttpContext,
  HttpGet,
  type IHttpContext,
} from '@c-visitor/framework';

@Controller('/api/identity')
export class IdentityController extends ControllerBase {
  constructor(
    @Inject(IdentityService) private readonly identityService: IdentityService,
  ) {
    super();
  }

  /**
   * Register a new user
   */
  @HttpPost('/signup')
  async signUp(@HttpContext() context: IHttpContext) {
    const body = context.request.body;
    const user = await this.identityService.signUp(body);
    return this.created(context.response, user, 'User registered successfully');
  }

  /**
   * Authenticate a user
   */
  @HttpPost('/signin')
  async signIn(@HttpContext() context: IHttpContext) {
    const body = context.request.body;
    console.log(body)
    const result = await this.identityService.signIn(body);
    return this.success(
      context.response,
      result,
      'User authenticated successfully',
    );
  }

  /**
   * Refresh authentication token
   */
  @HttpPost('/refresh-token')
  async refreshToken(@HttpContext() context: IHttpContext) {
    const body = context.request.body as RefreshTokenExecute;
    const result = await this.identityService.refreshToken(body.refreshToken);
    return this.success(
      context.response,
      result,
      'Token refreshed successfully',
    );
  }

  /**
   * Sign out a user
   */
  @HttpPost('/signout')
  async signOut(@HttpContext() context: IHttpContext) {
    // Implementation depends on how you handle sessions/tokens
    return this.success(context.response, null, 'User signed out successfully');
  }

  /**
   * Verify user email
   */
  @HttpPost('/verify-email')
  async verifyEmail(@HttpContext() context: IHttpContext) {
    const body = context.request.body as VerifyEmailExecute;
    await this.identityService.verifyEmail(body.token);
    return this.success(context.response, null, 'Email verified successfully');
  }

  /**
   * Request password reset
   */
  @HttpPost('/forgot-password')
  async forgotPassword(@HttpContext() context: IHttpContext) {
    const body = context.request.body as ForgotPasswordExecute;
    // Pass the entire Execute object as expected by the service
    await this.identityService.forgotPassword(body);
    return this.success(
      context.response,
      null,
      'Password reset instructions sent',
    );
  }

  /**
   * Reset password
   */
  @HttpPost('/reset-password')
  async resetPassword(@HttpContext() context: IHttpContext) {
    const body = context.request.body as ResetPasswordExecute;
    await this.identityService.resetPassword(body);
    return this.success(context.response, null, 'Password reset successfully');
  }

  /**
   * Get current user profile
   */
  @HttpGet('/profile')
  async getProfile(@HttpContext() context: IHttpContext) {
    const request = context.request;
    // Check if user is authenticated
    this.unauthorizedIf(!request.user, 'User not authenticated');

    // Get user with roles
    const user = await this.identityService.getUserWithRoles(request.user.id);
    if (!user) {
      return this.notFound(context.response, 'User not found');
    }

    const userData = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      emailVerified: user.emailVerified,
    };

    return this.success(
      context.response,
      userData,
      'Profile retrieved successfully',
    );
  }
}
