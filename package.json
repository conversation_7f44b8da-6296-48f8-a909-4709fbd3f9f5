{"name": "@c-visitor/source", "version": "0.0.0", "license": "MIT", "scripts": {"dev:server": "npx nx serve server", "dev:web": "pnpm exec nx run web:dev", "build": "nx run-many --target=build --all --parallel", "build:server": "nx run server:build", "build:framework": "nx run framework:build", "build:dev": "nx run-many --target=build --all --parallel --configuration development", "build:prod": "nx run-many --target=build --all --parallel --configuration production", "build:prod:web": "nx run web:build --configuration production", "build:prod:server": "nx run server:build --configuration production", "build:prod:framework": "nx run framework:build --configuration production"}, "private": true, "dependencies": {"axios": "^1.9.0", "compression": "^1.8.0", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "morgan": "^1.10.0", "node-schedule": "^2.1.1", "react": "19.1.0", "react-dom": "19.1.0", "uuid": "^11.1.0", "zod": "^3.25.13"}, "devDependencies": {"@eslint/js": "^9.27.0", "@nx/esbuild": "21.1.0", "@nx/eslint": "21.1.0", "@nx/eslint-plugin": "21.1.0", "@nx/express": "21.1.0", "@nx/js": "^21.0.3", "@nx/node": "21.1.0", "@nx/react": "21.1.0", "@nx/vite": "21.1.0", "@nx/web": "21.1.0", "@nx/webpack": "21.1.0", "@nx/workspace": "21.1.0", "@svgr/webpack": "^8.1.0", "@swc-node/register": "~1.10.10", "@swc/cli": "~0.7.7", "@swc/core": "~1.11.24", "@swc/helpers": "~0.5.17", "@types/express": "^5.0.2", "@types/node": "^22.15.19", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "10.4.21", "esbuild": "^0.25.4", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "jiti": "2.4.2", "jsdom": "~26.1.0", "jsonc-eslint-parser": "^2.4.0", "nx": "21.1.0", "prettier": "^3.5.3", "tslib": "^2.8.1", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.0.0"}, "workspaces": ["apps/*", "libraries/*"]}