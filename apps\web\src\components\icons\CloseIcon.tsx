import React from 'react';

interface CloseIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const CloseIcon: React.FC<CloseIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0 w-5 h-5 relative", 
  width = 20, 
  height = 20 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <path
        d="M17.315 17.315C17.4712 17.1587 17.559 16.9468 17.559 16.7258C17.559 16.5049 17.4712 16.2929 17.315 16.1367L11.1783 10L17.315 3.86334C17.4668 3.70617 17.5508 3.49567 17.5489 3.27717C17.547 3.05868 17.4593 2.84966 17.3048 2.69516C17.1503 2.54065 16.9413 2.45301 16.7228 2.45111C16.5043 2.44921 16.2938 2.53321 16.1367 2.68501L9.99999 8.82167L3.86333 2.68501C3.70616 2.53321 3.49566 2.44921 3.27716 2.45111C3.05866 2.45301 2.84965 2.54065 2.69515 2.69516C2.54064 2.84966 2.453 3.05868 2.4511 3.27717C2.4492 3.49567 2.5332 3.70617 2.68499 3.86334L8.82166 10L2.68499 16.1367C2.6054 16.2135 2.54192 16.3055 2.49824 16.4072C2.45457 16.5088 2.43158 16.6182 2.43062 16.7288C2.42966 16.8395 2.45074 16.9492 2.49264 17.0516C2.53454 17.154 2.59642 17.2471 2.67467 17.3253C2.75291 17.4036 2.84595 17.4655 2.94837 17.5074C3.05078 17.5493 3.16051 17.5703 3.27116 17.5694C3.38181 17.5684 3.49116 17.5454 3.59283 17.5018C3.6945 17.4581 3.78646 17.3946 3.86333 17.315L9.99999 11.1783L16.1367 17.315C16.2929 17.4712 16.5049 17.559 16.7258 17.559C16.9468 17.559 17.1587 17.4712 17.315 17.315Z"
        fill="#646A73"
      />
    </svg>
  );
};
