import * as React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'vertical' | 'horizontal';
}

export function Logo({
  orientation = 'horizontal',
  className,
  ...props
}: LogoProps) {
  return (
    <div
      className={cn(
        'flex items-center flex-shrink-0 gap-2',
        orientation === 'vertical' ? 'flex-col items-start' : 'flex-row',
        className,
      )}
      {...props}
    >
      <svg
        width="142"
        height="24"
        viewBox="0 0 142 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.4472 19.9786C10.4108 20.4012 10.1592 20.6477 9.94761 20.7799C9.27841 21.111 8.52812 21.3006 7.73216 21.3006C4.94864 21.3006 2.69265 19.0399 2.69265 16.2511C2.69265 13.4623 4.94864 11.2017 7.73216 11.2017C8.21029 11.2017 8.67258 11.2712 9.11111 11.3959V8.62952C8.6628 8.5492 8.20237 8.50391 7.73216 8.50391C3.46204 8.50391 0 11.9722 0 16.2511C0 20.5301 3.46158 23.9983 7.73216 23.9983C9.65728 23.9983 11.457 23.2376 12.5345 22.2887C12.6016 22.2149 12.6761 22.1467 12.7712 21.9931C12.7712 21.9931 12.8206 21.9147 12.8215 21.9142C12.8523 21.8638 12.918 21.7503 12.9245 21.7353C12.9763 21.6223 13.0224 21.4962 13.0587 21.3505C13.0587 21.3468 13.0601 21.3431 13.0611 21.3393C13.0648 21.3244 13.0662 21.3066 13.0699 21.2922C13.075 21.2646 13.0797 21.2352 13.0853 21.2081C13.0918 21.1637 13.0984 21.1194 13.1044 21.0722C13.1091 21.033 13.117 20.9158 13.1207 20.8752V18.0322L10.4476 14.1684V19.9786H10.4472Z"
          fill="#008FD3"
        />
        <path
          d="M25.9106 8.98828C25.3704 9.20076 24.0996 9.8111 23.1019 11.156C23.0991 11.1537 23.0958 11.1509 23.0939 11.1495L18.1789 18.2378L13.2642 11.1495C13.2614 11.1509 13.2591 11.1537 13.2563 11.156C12.2586 9.8111 10.9877 9.20122 10.4476 8.98828V11.8196L18.1789 22.9715L25.911 11.8196V8.98828H25.9106Z"
          fill="#008FD3"
        />
        <path
          d="M28.6261 8.50391C28.1545 8.50391 27.6945 8.54874 27.2472 8.62999V11.3959C27.6857 11.2712 28.148 11.2017 28.6261 11.2017C31.4092 11.2017 33.6652 13.4623 33.6652 16.2516C33.6652 19.0404 31.4092 21.3001 28.6261 21.3001C27.8292 21.3001 27.0789 21.1105 26.4097 20.7804C26.1991 20.6473 25.947 20.4016 25.9106 19.9781V14.1679L23.2375 18.0317V20.8752C23.2413 20.9158 23.2487 21.033 23.2539 21.0732C23.259 21.1199 23.2655 21.1642 23.273 21.2081C23.2781 21.2357 23.2828 21.2651 23.2883 21.2922C23.2911 21.3071 23.2935 21.3249 23.2972 21.3393C23.2981 21.3431 23.2986 21.3468 23.2995 21.3505C23.3349 21.4962 23.3815 21.6223 23.4323 21.7353C23.4398 21.7503 23.506 21.8638 23.5372 21.9142C23.5372 21.9151 23.5875 21.9931 23.5875 21.9931C23.6817 22.1467 23.7572 22.2145 23.8233 22.2887C24.9008 23.2376 26.7005 23.9993 28.6266 23.9993C32.8962 23.9993 36.3587 20.5305 36.3587 16.252C36.3583 11.9722 32.8958 8.50391 28.6261 8.50391Z"
          fill="#008FD3"
        />
        <path
          d="M13.2661 8.81986C13.1911 8.4743 13.1506 8.11612 13.1506 7.74814C13.1506 4.96541 15.4019 2.70989 18.1793 2.70989C20.9568 2.70989 23.2081 4.96541 23.2081 7.74814C23.2081 8.11612 23.1676 8.4743 23.093 8.81986C23.9207 8.21419 24.8737 7.77056 25.9054 7.5324C25.7913 3.35292 22.3772 0 18.1793 0C13.9815 0 10.5674 3.35292 10.4532 7.5324C11.4845 7.77056 12.4375 8.21419 13.2661 8.81986Z"
          fill="#008FD3"
        />
        <path
          d="M51.8288 18.716C48.1568 18.716 45.4208 15.854 45.4208 12.236V12.2C45.4208 8.618 48.1028 5.684 51.9188 5.684C54.2408 5.684 55.6448 6.494 56.8508 7.646L55.4288 9.284C54.4208 8.348 53.3408 7.718 51.9008 7.718C49.4888 7.718 47.7428 9.698 47.7428 12.164V12.2C47.7428 14.666 49.4888 16.682 51.9008 16.682C53.4488 16.682 54.4388 16.052 55.5188 15.044L56.9408 16.484C55.6268 17.852 54.1868 18.716 51.8288 18.716ZM58.6558 14.054V11.912H63.9838V14.054H58.6558ZM65.5355 18.5L71.0795 5.81H73.1315L78.6755 18.5H76.3355L75.0575 15.458H69.0995L67.8035 18.5H65.5355ZM69.9095 13.496H74.2475L72.0695 8.456L69.9095 13.496ZM86.6862 18.716C83.0142 18.716 80.2782 15.854 80.2782 12.236V12.2C80.2782 8.618 82.9602 5.684 86.7762 5.684C89.0982 5.684 90.5022 6.494 91.7082 7.646L90.2862 9.284C89.2782 8.348 88.1982 7.718 86.7582 7.718C84.3462 7.718 82.6002 9.698 82.6002 12.164V12.2C82.6002 14.666 84.3462 16.682 86.7582 16.682C88.3062 16.682 89.2962 16.052 90.3762 15.044L91.7982 16.484C90.4842 17.852 89.0442 18.716 86.6862 18.716ZM99.9752 18.716C96.3032 18.716 93.5672 15.854 93.5672 12.236V12.2C93.5672 8.618 96.2492 5.684 100.065 5.684C102.387 5.684 103.791 6.494 104.997 7.646L103.575 9.284C102.567 8.348 101.487 7.718 100.047 7.718C97.6352 7.718 95.8892 9.698 95.8892 12.164V12.2C95.8892 14.666 97.6352 16.682 100.047 16.682C101.595 16.682 102.585 16.052 103.665 15.044L105.087 16.484C103.773 17.852 102.333 18.716 99.9752 18.716ZM107.414 18.5V5.9H116.756V7.88H109.628V11.156H115.946V13.136H109.628V16.52H116.846V18.5H107.414ZM123.883 18.68C121.957 18.68 120.139 18.014 118.627 16.664L119.959 15.08C121.165 16.124 122.371 16.718 123.937 16.718C125.305 16.718 126.169 16.088 126.169 15.134V15.098C126.169 14.198 125.665 13.712 123.325 13.172C120.643 12.524 119.131 11.732 119.131 9.41V9.374C119.131 7.214 120.931 5.72 123.433 5.72C125.269 5.72 126.727 6.278 128.005 7.304L126.817 8.978C125.683 8.132 124.549 7.682 123.397 7.682C122.101 7.682 121.345 8.348 121.345 9.176V9.212C121.345 10.184 121.921 10.616 124.333 11.192C126.997 11.84 128.383 12.794 128.383 14.882V14.918C128.383 17.276 126.529 18.68 123.883 18.68ZM135.397 18.68C133.471 18.68 131.653 18.014 130.141 16.664L131.473 15.08C132.679 16.124 133.885 16.718 135.451 16.718C136.819 16.718 137.683 16.088 137.683 15.134V15.098C137.683 14.198 137.179 13.712 134.839 13.172C132.157 12.524 130.645 11.732 130.645 9.41V9.374C130.645 7.214 132.445 5.72 134.947 5.72C136.783 5.72 138.241 6.278 139.519 7.304L138.331 8.978C137.197 8.132 136.063 7.682 134.911 7.682C133.615 7.682 132.859 8.348 132.859 9.176V9.212C132.859 10.184 133.435 10.616 135.847 11.192C138.511 11.84 139.897 12.794 139.897 14.882V14.918C139.897 17.276 138.043 18.68 135.397 18.68Z"
          fill="#008FD3"
        />
      </svg>
    </div>
  );
}

export default Logo;
