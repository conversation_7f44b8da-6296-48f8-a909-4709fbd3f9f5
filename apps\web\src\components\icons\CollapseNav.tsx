import React from 'react';
import { cn } from '@/lib/utils';

interface CollapseNavIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const CollapseNavIcon: React.FC<CollapseNavIconProps> = ({ 
  className, 
  color = "#1F2329", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path 
        d="M3 4.5L3 13.5" 
        stroke={color} 
        strokeWidth="1.5" 
        strokeLinecap="round" 
      />
      <path
        d="M6 9.00037L15 9.00037"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99998 6C8.99998 6 6.00001 8.20947 6 9.00002C5.99999 9.79058 9 12 9 12"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CollapseNavIcon;
