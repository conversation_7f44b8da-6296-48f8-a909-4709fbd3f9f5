FROM node:20-alpine
WORKDIR /app

RUN npm install -g pnpm

# Set environment variables
ENV NODE_ENV=production
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install pnpm and jq for JSON processing
RUN corepack enable && corepack prepare pnpm@latest --activate
RUN apk add --no-cache jq

# Install PM2 globally
RUN npm install pm2 -g

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 expressjs

# Create directories and set permissions
RUN mkdir -p /app/dist
RUN chown -R expressjs:nodejs /app

# Copy the pre-built server application
COPY --chown=expressjs:nodejs ./apps/server/dist/ ./

# Copy and clean package.json
COPY ./apps/server/package.json /tmp/original-package.json
RUN cat /tmp/original-package.json | \
    jq 'del(.nx) | del(.dependencies."@c-visitor/framework") | del(.dependencies."@c-visitor/shared") | del(.dependencies."@c-visitor/types") | del(.dependencies."@types/jsonwebtoken") | del(.dependencies."webpack-cli")' > /app/package.json && \
    chown expressjs:nodejs /app/package.json

RUN pnpm install

# Create PM2 ecosystem file
RUN echo 'module.exports = { \
  apps: [{ \
    name: "server", \
    script: "./main.cjs", \
    instances: "max", \
    exec_mode: "cluster", \
    watch: false, \
    max_memory_restart: "1G", \
    env: { \
      NODE_ENV: "production" \
    } \
  }] \
}' > ecosystem.config.js

USER expressjs

# Expose port
EXPOSE 5000

# Set the command to run the app with PM2
CMD ["pm2-runtime", "ecosystem.config.js"]
