import * as jwt from 'jsonwebtoken';
import { compare, hash } from 'bcryptjs';
import { JwtPayload, Secret, SignOptions } from 'jsonwebtoken';
import { randomBytes } from 'crypto';
import {
  Service,
  Inject,
  ValidationError,
  UnauthorizedError,
  NotFoundError,
} from '@c-visitor/framework';
import { IUser } from '@c-visitor/types';
import { IdentityRepository } from '@/repositories/identity.repository';
import {
  AuthResponseExecute,
  ForgotPasswordExecute,
  ResetPasswordExecute,
  SignInExecute,
  SignUpExecute,
} from '@/models/executes/identity.execute';
import { UserDocument } from '@/models/entities/User';
import { environment } from '@/configs/environment';

@Service()
export class IdentityService {
  private readonly jwtSecret: string = environment.JWT_SECRET;
  private readonly jwtExpiresIn: string = environment.JWT_EXPIRATION.toString();
  private readonly jwtRefreshExpiresIn: string = '7d'; // 7 days for refresh token

  constructor(
    @Inject(IdentityRepository)
    private readonly identityRepository: IdentityRepository,
  ) {}

  /**
   * Create a new user account
   */
  async signUp(dto: SignUpExecute): Promise<Partial<IUser>> {
    // Check if email already exists
    const existingUser = await this.identityRepository.findUserByEmail(
      dto.email,
    );
    if (existingUser) {
      throw new ValidationError('Email already exists');
    }

    // Hash password
    const hashedPassword = await hash(dto.password, 10);

    // Generate verification token
    const verificationToken = this.generateToken();
    const verificationTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create new user
    const newUser = await this.identityRepository.createUser({
      email: dto.email,
      fullName: dto.fullName,
      password: hashedPassword,
      verificationToken,
      verificationTokenExpiry,
      emailVerified: false,
    });

    // TODO: Send verification email with the verification token

    // Return user without sensitive information
    const { password: _, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * Authenticate a user and return tokens
   */
  async signIn(dto: SignInExecute): Promise<AuthResponseExecute> {
    // Find user by email
    const user = await this.identityRepository.findUserByEmailWithPassword(
      dto.email,
    );
    if (!user) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = compare(dto.password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Check if email is verified
    if (!user.emailVerified) {
      throw new UnauthorizedError('Email not verified');
    }

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateTokens(user);

    // Update refresh token in database
    // Store the refresh token in the database for validation
    await this.identityRepository.updateUser(user.id, { refreshToken });

    // Get user with roles
    const userWithRoles = await this.identityRepository.getUserWithRoles(
      user.id,
    );
    if (!userWithRoles) {
      throw new NotFoundError('User not found');
    }

    // Return tokens and user data
    return {
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        roles: userWithRoles.roles?.map((x) => x.name),
      },
    };
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify the refresh token
      const payload = jwt.verify(refreshToken, this.jwtSecret) as JwtPayload;

      // Check if it's a refresh token
      if (payload.type !== 'refresh') {
        throw new UnauthorizedError('Invalid token type');
      }

      // Find user by ID from token
      const userId = payload.sub;
      if (!userId) {
        throw new UnauthorizedError('Invalid token payload');
      }

      const user = await this.identityRepository.findUserById(userId);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } =
        await this.generateTokens(user);

      // Update refresh token in database
      await this.identityRepository.updateUser(user.id, {
        refreshToken: newRefreshToken,
      });

      return { accessToken, refreshToken: newRefreshToken };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new UnauthorizedError('Invalid or expired refresh token');
      }
      throw error;
    }
  }

  /**
   * Sign out a user by invalidating their refresh token
   */
  async signOut(userId: string): Promise<void> {
    // Update user to clear refresh token
    await this.identityRepository.updateUser(userId, { refreshToken: '' });
  }

  /**
   * Verify user's email
   */
  async verifyEmail(token: string): Promise<void> {
    // Find user by verification token
    const user =
      await this.identityRepository.findUserByVerificationToken(token);
    if (!user) {
      throw new ValidationError('Invalid verification token');
    }

    // Check if token has expired
    if (
      user.verificationTokenExpiry &&
      user.verificationTokenExpiry < new Date()
    ) {
      throw new ValidationError('Verification token has expired');
    }

    // Update user as verified
    await this.identityRepository.updateUser(user.id, {
      emailVerified: true,
      verificationToken: user.verificationToken,
      verificationTokenExpiry: user.verificationTokenExpiry,
    });
  }

  /**
   * Initiate password reset process
   */
  async forgotPassword(dto: ForgotPasswordExecute): Promise<void> {
    // Find user by email
    const user = await this.identityRepository.findUserByEmail(dto.email);
    if (!user) {
      // Don't reveal if email exists or not
      return;
    }

    // Generate reset token
    const resetPasswordToken = this.generateToken();
    const resetPasswordTokenExpiry = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

    // Update user with reset token
    await this.identityRepository.updateUser(user.id, {
      resetPasswordToken,
      resetPasswordTokenExpiry,
    });

    // TODO: Send password reset email with the reset token
  }

  /**
   * Reset password using token
   */
  async resetPassword(dto: ResetPasswordExecute): Promise<void> {
    // Find user by reset token
    const user = await this.identityRepository.findUserByResetToken(dto.token);
    if (!user) {
      throw new ValidationError('Invalid reset token');
    }

    // Check if token has expired
    if (
      user.resetPasswordTokenExpiry &&
      user.resetPasswordTokenExpiry < new Date()
    ) {
      throw new ValidationError('Reset token has expired');
    }

    // Hash new password
    const hashedPassword = await hash(dto.password, 10);

    // Update user with new password
    await this.identityRepository.updateUser(user.id, {
      password: hashedPassword,
      resetPasswordToken: user.resetPasswordToken,
      resetPasswordTokenExpiry: user.resetPasswordTokenExpiry,
    });
  }

  /**
   * Generate random token
   */
  private generateToken(): string {
    return randomBytes(32).toString('hex');
  }

  /**
   * Generate access and refresh tokens
   */
  private async generateTokens(
    user: UserDocument,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);

    return { accessToken, refreshToken };
  }

  /**
   * Generate JWT access token
   */
  private generateAccessToken(user: UserDocument): string {
    // Get user with roles
    const payload = {
      sub: user.id,
      email: user.email,
      type: 'access', // Add token type for consistency
    };

    const signOptions: SignOptions = { expiresIn: Number(this.jwtExpiresIn) };
    return jwt.sign(payload, this.jwtSecret as Secret, signOptions);
  }

  /**
   * Generate JWT refresh token
   */
  private generateRefreshToken(user: UserDocument): string {
    // Create payload for refresh token
    const payload = {
      sub: user.id,
      type: 'refresh',
    };

    // The expiresIn option can be a string like '7d' or a number in seconds
    const signOptions: SignOptions = {
      expiresIn: this.jwtRefreshExpiresIn as any, // Cast to any to avoid type issues
    };
    return jwt.sign(payload, this.jwtSecret as Secret, signOptions);
  }

  /**
   * Verify JWT token
   * @param token The JWT token to verify
   * @param tokenType Optional token type to check ('access' or 'refresh')
   * @returns The decoded token payload
   */
  verifyToken(
    token: string,
    tokenType?: 'access' | 'refresh',
  ): JwtPayload | string {
    try {
      const decoded = jwt.verify(token, this.jwtSecret);

      // If tokenType is specified, check if the token is of the correct type
      if (tokenType && typeof decoded === 'object' && 'type' in decoded) {
        if (decoded.type !== tokenType) {
          throw new UnauthorizedError(
            `Invalid token type. Expected ${tokenType}`,
          );
        }
      }

      return decoded;
    } catch (error) {
      if (error instanceof UnauthorizedError) {
        throw error;
      }
      throw new UnauthorizedError('Invalid or expired token');
    }
  }

  /**
   * Get user by ID with roles
   */
  async getUserWithRoles(userId: string): Promise<IUser | undefined> {
    return this.identityRepository.getUserWithRoles(userId);
  }
}
