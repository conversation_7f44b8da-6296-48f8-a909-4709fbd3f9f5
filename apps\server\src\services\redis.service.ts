import { Service, logger } from '@c-visitor/framework';
import Redis from 'ioredis';
import { environment } from '@/configs/environment';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  keyPrefix?: string;
  retryDelayOnFailover?: number;
  maxRetriesPerRequest?: number;
  lazyConnect?: boolean;
}

@Service()
export class RedisService {
  private client: Redis | null = null;
  private isConnected = false;
  private connectionPromise: Promise<void> | null = null;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize Redis client with configuration
   */
  private initialize(): void {
    try {
      const config: RedisConfig = {
        host: environment.REDIS_HOST || 'localhost',
        port: environment.REDIS_PORT || 6379,
        password: environment.REDIS_PASSWORD,
        db: environment.REDIS_DB || 0,
        keyPrefix: environment.REDIS_KEY_PREFIX || 'cvisitor:',
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      };

      this.client = new Redis(config);

      // Set up event handlers
      this.setupEventHandlers();

      logger.info('Redis client initialized', {
        host: config.host,
        port: config.port,
        db: config.db,
      });
    } catch (error) {
      logger.error('Failed to initialize Redis client', { error });
      this.isConnected = false;
    }
  }

  /**
   * Set up Redis event handlers
   */
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      logger.info('Redis client connected');
      this.isConnected = true;
    });

    this.client.on('ready', () => {
      logger.info('Redis client ready');
      this.isConnected = true;
    });

    this.client.on('error', (error) => {
      logger.error('Redis client error', { error });
      this.isConnected = false;
    });

    this.client.on('close', () => {
      logger.warn('Redis client connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis client reconnecting');
    });
  }

  /**
   * Ensure Redis connection is established
   */
  private async ensureConnection(): Promise<void> {
    if (this.isConnected && this.client) {
      return;
    }

    if (!this.connectionPromise) {
      this.connectionPromise = new Promise<void>((resolve, reject) => {
        if (!this.client) {
          this.initialize();
        }

        if (!this.client) {
          return reject(new Error('Failed to initialize Redis client'));
        }

        const connectTimeout = setTimeout(() => {
          reject(new Error('Redis connection timeout'));
        }, 10000);

        this.client.once('ready', () => {
          clearTimeout(connectTimeout);
          this.isConnected = true;
          this.connectionPromise = null;
          resolve();
        });

        this.client.once('error', (err) => {
          clearTimeout(connectTimeout);
          this.connectionPromise = null;
          reject(err);
        });

        // Trigger connection
        this.client.connect().catch(reject);
      });
    }

    return this.connectionPromise;
  }

  /**
   * Set a key-value pair with optional expiration
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);

    if (ttlSeconds) {
      await this.client.setex(key, ttlSeconds, serializedValue);
    } else {
      await this.client.set(key, serializedValue);
    }
  }

  /**
   * Get a value by key
   */
  async get<T = any>(key: string): Promise<T | null> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    const value = await this.client.get(key);
    if (value === null) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch {
      return value as T;
    }
  }

  /**
   * Delete a key
   */
  async del(key: string): Promise<number> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    return await this.client.del(key);
  }

  /**
   * Check if a key exists
   */
  async exists(key: string): Promise<boolean> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    const result = await this.client.exists(key);
    return result === 1;
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    const result = await this.client.expire(key, ttlSeconds);
    return result === 1;
  }

  /**
   * Get all keys matching a pattern
   */
  async keys(pattern: string): Promise<string[]> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    return await this.client.keys(pattern);
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    return await this.client.incr(key);
  }

  /**
   * Decrement a numeric value
   */
  async decr(key: string): Promise<number> {
    await this.ensureConnection();

    if (!this.client) {
      throw new Error('Redis client not initialized');
    }

    return await this.client.decr(key);
  }

  /**
   * Get Redis client instance for advanced operations
   */
  getClient(): Redis | null {
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  isRedisConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      logger.info('Redis client connection closed');
    }
  }
}
