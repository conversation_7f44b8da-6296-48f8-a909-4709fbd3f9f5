import { UserDocument } from '@/models/entities/User';
import { RoleDocument } from '@/models/entities/Role';
import { IdentityRepository } from '@/repositories/identity.repository';
import { UserRoleRepository } from '@/repositories/user-role.repository';
import { Inject, logger, Service } from '@c-visitor/framework';
import { hash } from 'bcryptjs';

@Service()
export class Migrations {
  constructor(
    @Inject(IdentityRepository)
    private readonly identityRepository: IdentityRepository,
    @Inject(UserRoleRepository)
    private readonly userRoleRepository: UserRoleRepository
  ) {
    this.run().catch((error) => logger.error(error, 'Migrations'));
  }

  async run() {
    await this.seedRoles();
    await this.seedUsers();
  }

  /**
   * Seed roles into the database
   * Creates 'Admin', 'Staff', and 'User' roles if they don't exist
   */
  async seedRoles(): Promise<RoleDocument[]> {
    logger.info('Seeding roles...');

    const roles: RoleDocument[] = [];
    const defaultRoles = ['Admin', 'Staff', 'User'];

    for (const roleName of defaultRoles) {
      let role = await this.identityRepository.findRoleByName(roleName);
      if (!role) {
        // Create role
        role = await this.identityRepository.createRole({
          name: roleName,
          active: true,
        });
        logger.info(`${roleName} role created`);
        roles.push(role);
      } else {
        logger.info(`${roleName} role already exists`);
        roles.push(role);
      }
    }

    return roles;
  }

  /**
   * Seed users into the database
   * Creates an admin user if it doesn't exist
   */
  async seedUsers(): Promise<UserDocument[]> {
    logger.info('Seeding users...');

    const users: UserDocument[] = [];

    // Check if admin user exists
    const adminEmail = '<EMAIL>';
    let adminUser = await this.identityRepository.findUserByEmail(adminEmail);

    if (!adminUser) {
      // Hash password
      const hashedPassword = await hash('PasS@W0rd', 10);

      // Create admin user
      adminUser = await this.identityRepository.createUser({
        email: adminEmail,
        fullName: 'Admin',
        password: hashedPassword,
        emailVerified: true,
        active: true,
      });

      logger.info('Admin user created');
      users.push(adminUser);

      // Assign Admin role to admin user
      await this.assignRoleToUser(adminUser, 'Admin');
    } else {
      logger.info('Admin user already exists');
      users.push(adminUser);
    }

    return users;
  }

  /**
   * Assign a role to a user
   * @param user The user to assign the role to
   * @param roleName The name of the role to assign
   */
  private async assignRoleToUser(user: UserDocument, roleName: string): Promise<void> {
    // Find role by name
    const role = await this.identityRepository.findRoleByName(roleName);
    if (!role) {
      logger.error(`Role '${roleName}' not found`);
      return;
    }

    // Check if the user-role association already exists
    const userId = user._id ? user._id.toString() : '';
    const roleId = role._id ? role._id.toString() : '';

    const existingUserRole = await this.userRoleRepository.findByUserIdAndRoleId(userId, roleId);

    if (existingUserRole) {
      logger.info(`User '${user.email}' already has role '${roleName}'`);
      return;
    }

    try {
      // Create the user-role association
      await this.userRoleRepository.create({
        userId: userId,
        roleId: roleId
      });

      logger.info(`Role '${roleName}' assigned to user '${user.email}'`);
    } catch (error) {
      logger.error(`Failed to create user-role association: ${error}`);
    }
  }
}
