import { Client } from 'minio';
import { environment } from './environment';

export const minioClient = new Client({
  endPoint: environment.MINIO_ENDPOINT,
  port: environment.MINIO_PORT,
  useSSL: environment.MINIO_USE_SSL,
  accessKey: environment.MINIO_ACCESS_KEY,
  secretKey: environment.MINIO_SECRET_KEY,
  region: 'us-east-1', // Set default region
  pathStyle: true, // Use path-style URLs for localhost
});

// Initialize MinIO buckets
export async function initializeMinIO() {
  try {
    console.log('🔧 Initializing MinIO with config:', {
      endpoint: environment.MINIO_ENDPOINT,
      port: environment.MINIO_PORT,
      useSSL: environment.MINIO_USE_SSL,
      accessKey: environment.MINIO_ACCESS_KEY ? '***' : 'NOT_SET',
      secretKey: environment.MINIO_SECRET_KEY ? '***' : 'NOT_SET'
    });

    const buckets = ['public', 'private', 'images'];

    for (const bucket of buckets) {
      const exists = await minioClient.bucketExists(bucket);
      if (!exists) {
        await minioClient.makeBucket(bucket, 'us-east-1');
        console.log(`✅ Created MinIO bucket: ${bucket}`);
      } else {
        console.log(`✅ MinIO bucket exists: ${bucket}`);
      }
    }
  } catch (error) {
    console.error('❌ Error initializing MinIO:', error);
    console.log('⚠️ MinIO initialization failed, but server will continue...');
  }
}
