import { RequestStatus } from '@c-visitor/types';

export interface ListRequestQuery {
  keyword?: string;
  pageIndex?: number;
  pageSize?: number;
  timeIn?: Date;
  timeOut?: Date;
  status?: RequestStatus;
  // New search and filter fields
  search?: string;
  // Legacy date filters (for backward compatibility)
  timeInFrom?: Date;
  timeInTo?: Date;
  // New clear date range filters
  startDate?: Date;
  endDate?: Date;
}
