import { memo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface RejectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rejectReason: string;
  setRejectReason: (reason: string) => void;
  isSubmitting: boolean;
  onReject: () => void;
}

/**
 * Dialog component for rejecting a request
 * Wrapped with React.memo to prevent unnecessary re-renders
 */
const RejectDialogComponent = ({
  open,
  onOpenChange,
  rejectReason,
  setRejectReason,
  isSubmitting,
  onReject
}: RejectDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[90vw] max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Reject Access Registration</DialogTitle>
        </DialogHeader>
        <div className='grid gap-4 py-4'>
          <div className='flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]'>
            <p className='flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]'>Reason</p>
            <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]'>*</p>
          </div>
          <div className='grid gap-2'>
            <Textarea
              id='rejectReason'
              placeholder='Enter reason for rejection'
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className='col-span-3'
            />
          </div>
        </div>
        <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-0.5'>
          <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1'>
            <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
              <span className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
                The system will update the access registration status to →{' '}
              </span>
              <span className='flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#23262f]'>
                "Rejected"
              </span>
            </p>
          </div>
          <div className='flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1'>
            <p className='flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]'>
              Are you sure you want to proceed?
            </p>
          </div>
        </div>
        <DialogFooter className='flex-col sm:flex-row gap-2'>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            className='w-full sm:w-auto'
          >
            Cancel
          </Button>
          <Button onClick={onReject} disabled={isSubmitting || !rejectReason.trim()} className='w-full sm:w-auto'>
            {isSubmitting ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Memoized version of RejectDialog to prevent unnecessary re-renders
 * Only re-renders when props change
 */
export const RejectDialog = memo(RejectDialogComponent, (prevProps, nextProps) => {
  // Return true if passing nextProps to render would return
  // the same result as passing prevProps to render,
  // otherwise return false
  return (
    prevProps.open === nextProps.open &&
    prevProps.isSubmitting === nextProps.isSubmitting &&
    prevProps.rejectReason === nextProps.rejectReason
    // We don't compare function props like onOpenChange, setRejectReason, onReject
    // because they should be wrapped in useCallback by the parent component
  )
});
