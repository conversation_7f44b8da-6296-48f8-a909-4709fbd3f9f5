import React from 'react';
import { cn } from '@/lib/utils';

interface ServiceIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const ServiceIcon: React.FC<ServiceIconProps> = ({ 
  className, 
  color = "#141B34", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M2.45201 11.313C2.97218 10.2199 3.23226 9.67338 3.68547 9.35737C3.72207 9.33185 3.75944 9.30758 3.79754 9.2846C4.26921 9 4.84614 9 6 9C7.15386 9 7.73079 9 8.20246 9.2846C8.24056 9.30758 8.27793 9.33185 8.31453 9.35737C8.76774 9.67338 9.02782 10.2199 9.54799 11.313C10.3236 12.9429 10.7114 13.7579 10.3824 14.3569C10.3701 14.3793 10.3572 14.4014 10.3436 14.423C9.98077 15 9.12101 15 7.40151 15H4.5985C2.87899 15 2.01923 15 1.65644 14.423C1.64285 14.4014 1.62988 14.3793 1.61755 14.3569C1.2886 13.7579 1.67641 12.9429 2.45201 11.313Z"
        stroke={color}
        strokeWidth="1.5"
      />
      <path
        d="M10.9102 9.0155C11.2074 9 11.5619 9 11.9992 9C13.1531 9 13.73 9 14.2017 9.2846C14.2398 9.30758 14.2771 9.33185 14.3137 9.35737C14.7669 9.67338 15.027 10.2199 15.5472 11.313C16.3228 12.9429 16.7106 13.7579 16.3817 14.3569C16.3693 14.3793 16.3564 14.4014 16.3428 14.423C15.98 15 15.1202 15 13.4007 15H12.5543"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M13.2059 6.75C13.0436 6.35365 12.8197 5.88321 12.5484 5.31304C12.0283 4.21993 11.7682 3.67338 11.315 3.35737C11.2784 3.33185 11.241 3.30758 11.2029 3.2846C10.7312 3 10.1543 3 9.00043 3C7.84657 3 7.26964 3 6.79797 3.2846C6.75987 3.30758 6.7225 3.33185 6.6859 3.35737C6.23269 3.67338 5.97261 4.21993 5.45244 5.31304C5.18111 5.88321 4.95725 6.35365 4.79492 6.75"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default ServiceIcon;
