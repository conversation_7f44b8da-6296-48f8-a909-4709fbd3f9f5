import React from 'react';

interface ArrowRightIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const ArrowRightIcon: React.FC<ArrowRightIconProps> = ({ 
  className = "flex-grow-0 flex-shrink-0 w-5 h-5 relative", 
  width = 20, 
  height = 20 
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      preserveAspectRatio="none"
    >
      <g clipPath="url(#clip0_174_2170)">
        <path
          d="M2.5 10H14.375"
          stroke="#9CA5B3"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.75 4.375L14.375 10L8.75 15.625"
          stroke="#9CA5B3"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.875 3.125V16.875"
          stroke="#9CA5B3"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_174_2170">
          <rect width={20} height={20} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
