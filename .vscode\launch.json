{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      // Debug using direct ts-node approach (best for TypeScript debugging)
      "type": "node",
      "request": "launch",
      "name": "Debug: C-Visitor|Server",
      "program": "${workspaceFolder}/apps/server/src/main.ts",
      "cwd": "${workspaceFolder}",
      "runtimeArgs": [
        "--require",
        "ts-node/register",
        "--require",
        "tsconfig-paths/register"
      ],
      "env": {
        "TS_NODE_PROJECT": "${workspaceFolder}/apps/server/tsconfig.app.json",
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true,
      "restart": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug: C-Visitor|Server (Nx)",
      "runtimeExecutable": "npx",
      "args": ["nx", "serve", "server"],
      "cwd": "${workspaceFolder}",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug: C-Visitor|Server (pnpm)",
      "runtimeExecutable": "pnpm",
      "args": ["nx", "serve", "server", "--inspect"],
      "cwd": "${workspaceFolder}",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug: C-Visitor|Server (Direct)",
      "program": "${workspaceFolder}/apps/server/src/main.ts",
      "cwd": "${workspaceFolder}/apps/server",
      "runtimeArgs": [
        "--require",
        "ts-node/register",
        "--require",
        "tsconfig-paths/register"
      ],
      "env": {
        "TS_NODE_PROJECT": "${workspaceFolder}/apps/server/tsconfig.app.json",
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**"
      ],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to C-Visitor Server",
      "port": 9229,
      "restart": true,
      "localRoot": "${workspaceFolder}/apps/server",
      "remoteRoot": "${workspaceFolder}/apps/server",
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**"
      ]
    }
  ]
}
