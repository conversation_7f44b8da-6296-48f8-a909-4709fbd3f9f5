import { minioClient } from '@/configs/minio';
import { Service } from '@c-visitor/framework';

@Service()
export class BucketService {
  static async initializeBucket(): Promise<void> {
    try {
      const bucketPublicExists = await minioClient.bucketExists('public');
      const bucketPrivateExists = await minioClient.bucketExists('private');
      const bucketRecognitionExists =
        await minioClient.bucketExists('recognitions');
      if (!bucketPublicExists) {
        await minioClient.makeBucket('public', 'us-east-1');
      }
      if (!bucketPrivateExists) {
        await minioClient.makeBucket('private', 'us-east-1');
      }
      if (!bucketRecognitionExists) {
        await minioClient.makeBucket('recognitions', 'us-east-1');
      }
    } catch (error) {
      console.error('Failed to initialize MinIO bucket:', error);
    }
  }
}
