import mongoose from 'mongoose';
import { environment } from './environment';
import { logger } from '@c-visitor/framework';

/**
 * Connect to MongoDB using Mongoose
 */
export const connectToMongoDB = async (): Promise<typeof mongoose> => {
  try {
    // Build MongoDB connection string
    const connectionString = environment.DB_CONNECTION;
    // Set Mongoose options
    mongoose.set('strictQuery', true);

    // Connect to MongoDB
    await mongoose.connect(connectionString);

    logger.info('Connected to MongoDB successfully');
    return mongoose;
  } catch (error) {
    logger.error('Failed to connect to MongoDB', { error });
    throw error;
  }
};

/**
 * Disconnect from MongoDB
 */
export const disconnectFromMongoDB = async (): Promise<void> => {
  try {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error('Error disconnecting from MongoDB', { error });
    throw error;
  }
};

export default mongoose;
