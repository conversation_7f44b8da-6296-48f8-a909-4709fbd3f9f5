import React from 'react';
import { cn } from '@/lib/utils';

interface CustomerIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const CustomerIcon: React.FC<CustomerIconProps> = ({ 
  className, 
  color = "#1F2329", 
  ...props 
}) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("w-[18px] h-[18px]", className)}
      preserveAspectRatio="none"
      {...props}
    >
      <g clipPath="url(#clip0_84_668)">
        <path
          d="M5.83692 12.8334C5.01164 13.3072 2.8478 14.2748 4.16572 15.4856C4.80951 16.077 5.52654 16.5 6.42801 16.5H11.572C12.4735 16.5 13.1905 16.077 13.8343 15.4856C15.1522 14.2748 12.9884 13.3072 12.1631 12.8334C10.2278 11.7222 7.77219 11.7222 5.83692 12.8334Z"
          stroke={color}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.625 7.5C11.625 8.94975 10.4497 10.125 9 10.125C7.55025 10.125 6.375 8.94975 6.375 7.5C6.375 6.05025 7.55025 4.875 9 4.875C10.4497 4.875 11.625 6.05025 11.625 7.5Z"
          stroke={color}
          strokeWidth="1.5"
        />
        <path
          d="M2.1405 12C1.72875 11.0748 1.5 10.0507 1.5 8.97342C1.5 4.84596 4.85786 1.5 9 1.5C13.1421 1.5 16.5 4.84596 16.5 8.97342C16.5 10.0507 16.2712 11.0748 15.8595 12"
          stroke={color}
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_84_668">
          <rect width={18} height={18} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CustomerIcon;
