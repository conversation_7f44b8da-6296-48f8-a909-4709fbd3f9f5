{"name": "@c-visitor/framework", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "libraries/framework/dist", "main": "libraries/framework/src/index.ts", "tsConfig": "libraries/framework/tsconfig.lib.json", "format": ["esm"], "declarationRootDir": "libraries/framework/src"}}}}, "dependencies": {"@c-visitor/types": "workspace:^", "cors": "^2.8.5", "express": "^5.1.0", "mongoose": "^8.15.0", "reflect-metadata": "^0.2.2", "winston": "^3.17.0", "zod": "^3.25.13"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/mongoose": "^5.11.97"}}