import mongoose from '@/configs/mongoose';
import { Document, Schema } from 'mongoose';

// Import the IRecognition interface
// We're defining it here temporarily until the types package is updated
export interface IRecognition {
  id: string;
  avatar: string;
  company: string;
  detectedFace: string;
  deviceId: string;
  eventId: string;
  job: string;
  mask: number;
  recognizeId: string;
  recognizeName: string;
  recognizeStatus: number;
  srcId: string;
  temperature: number;
  trackingId: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Recognition interface for Mongoose document
export interface RecognitionDocument extends Omit<IRecognition, 'id'>, Document {}

// Recognition schema for Mongoose
const RecognitionSchema = new Schema<RecognitionDocument>(
  {
    avatar: { type: String, required: true, trim: true },
    company: { type: String, required: true, trim: true },
    detectedFace: { type: String, required: true, trim: true },
    deviceId: { type: String, required: true, trim: true },
    eventId: { type: String, required: true, trim: true },
    job: { type: String, required: true, trim: true },
    mask: { type: Number, required: true },
    recognizeId: { type: String, required: true, trim: true },
    recognizeName: { type: String, required: true, trim: true },
    recognizeStatus: { type: Number, required: true },
    srcId: { type: String, required: true, trim: true },
    temperature: { type: Number, required: true },
    trackingId: { type: Number, required: true },
  },
  { timestamps: true, collection: 'recognitions' },
);

// Create indexes for frequently queried fields
RecognitionSchema.index({ recognizeId: 1 });
RecognitionSchema.index({ deviceId: 1 });
RecognitionSchema.index({ recognizeStatus: 1 });
RecognitionSchema.index({ createdAt: 1 });

// Create the Recognition model
const RecognitionModel = mongoose.model<RecognitionDocument>('Recognition', RecognitionSchema);

export default RecognitionModel;
