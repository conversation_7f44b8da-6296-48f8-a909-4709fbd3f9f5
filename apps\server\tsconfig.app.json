{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node", "express"], "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "baseUrl": ".", "sourceMap": true, "inlineSourceMap": false, "inlineSources": false, "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts"], "exclude": ["eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "references": [{"path": "../../libraries/types/tsconfig.lib.json"}, {"path": "../../libraries/shared/tsconfig.lib.json"}, {"path": "../../libraries/framework/tsconfig.lib.json"}]}