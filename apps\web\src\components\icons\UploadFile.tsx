import React from 'react';


interface UploadFileIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  color?: string;
}

export const UploadFileIcon: React.FC<UploadFileIconProps> = ({
  className,
  color = '#1F2329',
  ...props
}) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-5 h-5"
    >
      <path
        d="M12.3333 13.75H15.1042C17.1094 13.75 18.75 12.669 18.75 10.6562C18.75 8.64346 16.8177 7.64132 15.25 7.5625C14.9259 4.41472 12.6615 2.5 10 2.5C7.48437 2.5 5.86417 4.19453 5.33333 5.875C3.14583 6.08594 1.25 7.49885 1.25 9.8125C1.25 12.1262 3.21875 13.75 5.625 13.75H7.66667"
        stroke="#008FD3"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 9.99595L10 7.50006M10 7.50006L7.5 9.99595M10 7.50006V17.5M10 7.50006V8.12403"
        stroke="#008FD3"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default UploadFileIcon;
