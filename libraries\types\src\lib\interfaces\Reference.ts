import { DateTracking } from '../shared/DateTracking.js';
import { IRequest } from './Request.js';

/**
 * Interface cho thông tin người tham chiếu
 */
export interface IReference extends ReferenceAttributes {}

export interface ReferenceAttributes extends ReferenceReference, DateTracking {
  id: string;
  // Danh sách mapping
  mappings: string[];
  // Họ và tên
  fullName: string;
  // Email
  email: string;
  // Số điện thoại
  phone: string;
  // Đơn vị/Tổ chức
  unit: string;
  // S<PERSON> chứng minh thư
  cardIdNumber: string;
  // Mặt trước chứng minh thư
  cardIdFront: string;
  // Mặt sau chứng minh thư
  cardIdBack: string;
  // Hình ảnh chân dung
  avatar: string;
  // ID của yêu cầu liên quan
  requestId?: string;
}

export interface ReferenceReference {
  unitId?: string;
  request?: IRequest;
}
