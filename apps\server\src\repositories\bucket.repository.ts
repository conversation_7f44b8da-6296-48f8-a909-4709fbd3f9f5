import Bucket, { BucketDocument } from '@/models/entities/Bucket';
import { Service } from '@c-visitor/framework';

/**
 * Repository for Bucket entity
 */
@Service()
export class BucketRepository {
  /**
   * Find a bucket by ID
   * @param id Bucket ID
   * @returns Bucket or null if not found
   */
  async findById(id: string): Promise<BucketDocument | null> {
    return Bucket.findById(id).exec();
  }

  /**
   * Find a bucket by name
   * @param name Bucket name
   * @returns Bucket or null if not found
   */
  async findByName(name: string): Promise<BucketDocument | null> {
    return Bucket.findOne({ name }).exec();
  }

  /**
   * Find buckets by region
   * @param region Region name
   * @returns Array of buckets in the specified region
   */
  async findByRegion(region: string): Promise<BucketDocument[]> {
    return Bucket.find({ region }).exec();
  }

  /**
   * Find all buckets
   * @param options Query options
   * @returns Array of buckets
   */
  async findAll(options: {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
  } = {}): Promise<BucketDocument[]> {
    const { limit, skip, sort } = options;
    let query = Bucket.find();

    if (skip) {
      query = query.skip(skip);
    }

    if (limit) {
      query = query.limit(limit);
    }

    if (sort) {
      query = query.sort(sort);
    }

    return query.exec();
  }

  /**
   * Create a new bucket
   * @param bucketData Bucket data
   * @returns Created bucket
   */
  async create(bucketData: Partial<BucketDocument>): Promise<BucketDocument> {
    const bucket = new Bucket(bucketData);
    return bucket.save();
  }

  /**
   * Update a bucket
   * @param id Bucket ID
   * @param bucketData Bucket data to update
   * @returns Updated bucket or null if not found
   */
  async update(
    id: string,
    bucketData: Partial<BucketDocument>,
  ): Promise<BucketDocument | null> {
    return Bucket.findByIdAndUpdate(id, bucketData, { new: true }).exec();
  }

  /**
   * Delete a bucket
   * @param id Bucket ID
   * @returns True if deleted, false if not found
   */
  async delete(id: string): Promise<boolean> {
    const result = await Bucket.deleteOne({ _id: id }).exec();
    return result.deletedCount > 0;
  }
}
