import { connectToMongoDB } from '@/configs/mongoose.js';
import { logger } from '@c-visitor/framework';

// Import Mongoose models
import Role from './entities/Role';
import UserRole from './entities/UserRole';
import Bucket from './entities/Bucket';
import File from './entities/File';
import Request from './entities/Request';
import Reference from './entities/Reference';
import User from './entities/User';

/**
 * Initialize MongoDB connection and models
 */
export const initializeModels = async () => {
  try {
    // Connect to MongoDB
    await connectToMongoDB();
    logger.info('MongoDB models initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize MongoDB models', { error });
    throw error;
  }
};

export { User, Role, UserRole, Bucket, File, Request, Reference };
