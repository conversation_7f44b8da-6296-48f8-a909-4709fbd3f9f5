import { Service } from '@c-visitor/framework';
import Recognition, { RecognitionDocument } from '@/models/entities/Recognition';
import { FilterQuery, ClientSession } from 'mongoose';
import mongoose from '@/configs/mongoose';

/**
 * Interface for recognition query parameters
 */
export interface ListRecognitionQuery {
  recognizeId?: string;
  recognizeStatus?: number;
  pageIndex?: number;
  pageSize?: number;
  fromDate?: Date;
  toDate?: Date;
}

/**
 * Repository for Recognition entity
 */
@Service()
export class RecognitionRepository {
  /**
   * Find a recognition by ID
   * @param id Recognition ID
   * @returns Recognition or null if not found
   */
  async findById(id: string): Promise<RecognitionDocument | null> {
    return Recognition.findById(id).exec();
  }

  /**
   * Create a new recognition
   * @param data Recognition data
   * @returns Created recognition
   */
  async create(data: Partial<RecognitionDocument>): Promise<RecognitionDocument> {
    return Recognition.create(data);
  }

  /**
   * Update a recognition
   * @param id Recognition ID
   * @param data Recognition data to update
   * @returns Updated recognition or null if not found
   */
  async update(id: string, data: Partial<RecognitionDocument>): Promise<RecognitionDocument | null> {
    return Recognition.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  /**
   * Delete a recognition
   * @param id Recognition ID
   * @returns Deleted recognition or null if not found
   */
  async delete(id: string): Promise<RecognitionDocument | null> {
    return Recognition.findByIdAndDelete(id).exec();
  }

  /**
   * Upsert a recognition (create or update)
   * @param data Recognition data
   * @param session Optional MongoDB session for transactions
   * @returns Upserted recognition
   */
  async upsertAsync(data: Partial<RecognitionDocument>, session?: ClientSession): Promise<RecognitionDocument> {
    // Remove timestamps if present
    const { createdAt, updatedAt, ...recognitionData } = data as any;

    // If id is provided, try to update first
    if (data._id) {
      const existingRecognition = await Recognition.findById(data._id).session(session || null).exec();

      if (existingRecognition) {
        // Update existing recognition
        return Recognition.findByIdAndUpdate(data._id, recognitionData, {
          new: true,
          session: session || null
        }).exec() as Promise<RecognitionDocument>;
      }
    }

    // Create new recognition
    return Recognition.create([recognitionData], { session: session || null })
      .then(docs => docs[0]);
  }

  /**
   * Get recognitions with pagination and filtering
   * @param params Query parameters
   * @returns Paginated recognitions and total count
   */
  async getRecognitions(params: ListRecognitionQuery) {
    // Build filter query
    const filter: FilterQuery<RecognitionDocument> = {};

    if (params.recognizeId) filter.recognizeId = params.recognizeId;
    if (params.recognizeStatus !== undefined) filter.recognizeStatus = params.recognizeStatus;

    // Add date filters if provided
    if (params.fromDate || params.toDate) {
      filter.createdAt = {};

      if (params.fromDate) {
        filter.createdAt.$gte = params.fromDate;
      }

      if (params.toDate) {
        filter.createdAt.$lte = params.toDate;
      }
    }

    // Set up pagination options
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const skip = (pageIndex - 1) * pageSize;

    // Execute count query
    const countPromise = Recognition.countDocuments(filter).exec();

    // Execute find query with pagination
    const docsPromise = Recognition.find(filter)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(skip)
      .limit(pageSize)
      .exec();

    // Wait for both queries to complete
    const [count, rows] = await Promise.all([countPromise, docsPromise]);

    // Return both the paginated results and total count
    return {
      rows,
      count,
    };
  }

  /**
   * Find recognitions by recognize IDs
   * @param recognizeIds Array of recognize IDs
   * @returns Array of recognitions
   */
  async findByRecognizeIds(recognizeIds: string[]): Promise<RecognitionDocument[]> {
    return Recognition.find({ recognizeId: { $in: recognizeIds } })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Find recognitions by date range
   * @param fromDate Start date
   * @param toDate End date
   * @returns Array of recognitions
   */
  async findByDateRange(fromDate: Date, toDate: Date): Promise<RecognitionDocument[]> {
    return Recognition.find({
      createdAt: {
        $gte: fromDate,
        $lte: toDate
      }
    })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Get recognition statistics for multiple recognize IDs (mappings)
   * @param recognizeIds Array of recognize IDs (mappings from references)
   * @returns Recognition stats for each recognize ID
   */
  async getRecognitionStatsByIds(recognizeIds: string[]): Promise<{ [key: string]: { recognitionCount: number; firstCheckIn: Date | null; lastCheckOut: Date | null } }> {
    try {
      const pipeline = [
        {
          $match: {
            recognizeId: { $in: recognizeIds }
          }
        },
        {
          $group: {
            _id: '$recognizeId',
            recognitionCount: { $sum: 1 },
            firstCheckIn: { $min: '$createdAt' },
            lastCheckOut: { $max: '$createdAt' }
          }
        }
      ];

      const results = await Recognition.aggregate(pipeline).exec();

      // Convert array to object for easy lookup
      const statsMap: { [key: string]: { recognitionCount: number; firstCheckIn: Date | null; lastCheckOut: Date | null } } = {};

      results.forEach(result => {
        statsMap[result._id] = {
          recognitionCount: result.recognitionCount,
          firstCheckIn: result.firstCheckIn,
          lastCheckOut: result.lastCheckOut
        };
      });

      // Fill in missing recognize IDs with zero stats
      recognizeIds.forEach(id => {
        if (!statsMap[id]) {
          statsMap[id] = {
            recognitionCount: 0,
            firstCheckIn: null,
            lastCheckOut: null
          };
        }
      });

      return statsMap;
    } catch (error) {
      console.error('Error getting recognition stats:', error);
      // Return empty stats for all IDs if error occurs
      const emptyStats: { [key: string]: { recognitionCount: number; firstCheckIn: Date | null; lastCheckOut: Date | null } } = {};
      recognizeIds.forEach(id => {
        emptyStats[id] = {
          recognitionCount: 0,
          firstCheckIn: null,
          lastCheckOut: null
        };
      });
      return emptyStats;
    }
  }

  /**
   * Execute a transaction
   * @param callback Function to execute within the transaction
   * @returns Result of the callback function
   */
  async withTransaction<T>(callback: (session: ClientSession) => Promise<T>): Promise<T> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const result = await callback(session);
      await session.commitTransaction();
      return result;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }
}
