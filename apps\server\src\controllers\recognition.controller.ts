import { <PERSON>, ControllerBase, HttpContext, HttpGet, type IHttpContext, Inject } from '@c-visitor/framework'
import { RecognitionService } from '@/services/recognition.service'
import { GetRecognitionsResponse } from '@c-visitor/types'

// Define APIResponse interface if it's not available in the types package
interface APIResponse<T> {
  code: string;
  statusCode: number;
  success: boolean;
  data: T;
  message?: string;
}

@Controller('/api/recognition')
export class RecognitionController extends ControllerBase {
  constructor(@Inject(RecognitionService) private readonly recognitionService: RecognitionService) {
    super()
  }

  @HttpGet('/reference/:id')
  async getReferenceRecognitions(@HttpContext() context: IHttpContext) {
    try {
      const referenceId = (context.request as any).params.id
      const { fromDate, toDate } = (context.request as any).query
      const pageIndex = parseInt((context.request as any).query.pageIndex as string) || 1
      const pageSize = parseInt((context.request as any).query.pageSize as string) || 10

      // Validate date parameters
      let fromDateObj: Date | undefined = undefined
      let toDateObj: Date | undefined = undefined

      if (fromDate && fromDate !== 'undefined' && fromDate !== 'null') {
        try {
          fromDateObj = new Date(fromDate as string)
          // Check if date is valid
          if (isNaN(fromDateObj.getTime())) {
            fromDateObj = undefined
          }
        } catch (e) {
          // Invalid date format, ignore
        }
      }

      if (toDate && toDate !== 'undefined' && toDate !== 'null') {
        try {
          toDateObj = new Date(toDate as string)
          // Check if date is valid
          if (isNaN(toDateObj.getTime())) {
            toDateObj = undefined
          }
        } catch (e) {
          // Invalid date format, ignore
        }
      }

      const result = await this.recognitionService.getReferenceRecognitions({
        referenceId,
        pageIndex,
        pageSize,
        fromDate: fromDateObj,
        toDate: toDateObj
      })

      const response: APIResponse<GetRecognitionsResponse> = {
        code: result.success ? 'SUCCESS' : 'ERROR',
        statusCode: result.success ? 200 : 404,
        success: result.success,
        data: result.data || {
          references: [],
          recognitions: [],
          total: 0,
          pageIndex,
          pageSize,
          totalPages: 0
        },
        message: result.message || (result.success ? 'Success' : 'Error')
      }

      return this.success(context.response, response)
    } catch (error) {
      return this.error(context.response, error instanceof Error ? error.message : 'An unexpected error occurred')
    }
  }
}
