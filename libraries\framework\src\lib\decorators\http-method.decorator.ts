import 'reflect-metadata';

// Metadata keys
export const METHOD_METADATA = 'method';
export const ROUTES_METADATA = 'routes';

// HTTP Method enum
export enum HttpMethod {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  DELETE = 'delete',
  PATCH = 'patch',
  OPTIONS = 'options',
  HEAD = 'head',
}

// Interface cho route metadata
export interface RouteMetadata {
  path: string;
  method: HttpMethod;
  handlerName: string | symbol;
}

/**
 * Decorator factory cho HTTP methods
 */
function createMappingDecorator(method: HttpMethod) {
  return function (path: string): MethodDecorator {
    return function (
      target: any,
      propertyKey: string | symbol,
      descriptor: PropertyDescriptor
    ) {
      // Lưu thông tin route vào metadata của class
      const routes: RouteMetadata[] =
        Reflect.getMetadata(ROUTES_METADATA, target.constructor) || [];
      routes.push({
        path,
        method,
        handlerName: propertyKey,
      });
      Reflect.defineMetadata(ROUTES_METADATA, routes, target.constructor);

      return descriptor;
    };
  };
}

// HTTP method decorators
export const HttpGet = createMappingDecorator(HttpMethod.GET);
export const HttpPost = createMappingDecorator(HttpMethod.POST);
export const HttpPut = createMappingDecorator(HttpMethod.PUT);
export const HttpDelete = createMappingDecorator(HttpMethod.DELETE);
export const HttpPatch = createMappingDecorator(HttpMethod.PATCH);
export const HttpOptions = createMappingDecorator(HttpMethod.OPTIONS);
export const HttpHead = createMappingDecorator(HttpMethod.HEAD);
