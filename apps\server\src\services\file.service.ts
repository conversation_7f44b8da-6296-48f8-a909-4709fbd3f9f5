import fs from 'fs';
import FileModel from '@/models/entities/File';
import { promisify } from 'util';
import { minioClient } from '@/configs/minio';
import { environment } from '@/configs/environment';
import { Service, ServiceResponse } from '@c-visitor/framework';
import { IFile } from '@c-visitor/types';
import { v4 as uuidv4 } from 'uuid';

const unlinkAsync = promisify(fs.unlink);

@Service()
export class FileService {
  async uploadFile(
    file: {
      originalname: string;
      mimetype: string;
      size: number;
      path: string;
    },
    bucket?: string,
  ): Promise<ServiceResponse> {
    const fileData: IFile = {
      filename: file.originalname,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      bucket: bucket ?? 'public',
      uploadedBy: 'system',
    };
    await FileModel.create(fileData);
    await unlinkAsync(file.path);
    // return this.success(fileData, 'File uploaded successfully');
    return {
      success: true,
      message: 'File uploaded successfully',
      data: fileData as unknown as any,
    } satisfies ServiceResponse<IFile>;
  }

  async getFile(id: string): Promise<{
    stream: NodeJS.ReadableStream;
    file: IFile;
  }> {
    const file = await FileModel.findOne({ where: { id } });
    if (!file) {
      throw new Error('File not found');
    }

    const fileName = file.filename;
    const bucketName = file.bucket;
    const stream = await minioClient.getObject(
      bucketName ?? 'public',
      fileName,
    );
    return {
      stream,
      file,
    };
  }

  async getFileByFilename(filename: string, bucket?: string): Promise<{
    stream: NodeJS.ReadableStream;
    mimeType: string;
  }> {
    try {
      // Get the object from MinIO directly using filename
      const stream = await minioClient.getObject(bucket ?? 'images', filename);

      // Try to determine MIME type from filename extension
      const mimeType = this.getMimeTypeFromFilename(filename);

      return {
        stream,
        mimeType,
      };
    } catch (error) {
      console.error('Error getting file by filename:', error);
      throw new Error(`File not found: ${filename}`);
    }
  }

  private getMimeTypeFromFilename(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      'pdf': 'application/pdf',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  async deleteFile(filename: string, bucket?: string): Promise<void> {
    const file = await FileModel.findOne({ where: { filename } });
    if (!file) {
      throw new Error('File not found');
    }

    await minioClient.removeObject(bucket ?? 'public', filename);
  }

  /**
   * Save a base64 image to MinIO and return the file name for database storage
   * @param base64Data Base64 encoded image data (with or without data URI prefix)
   * @param fileName Optional filename, will generate UUID if not provided
   * @param mimeType Optional mime type, will try to determine from base64 data if not provided
   * @param bucket Optional bucket name, defaults to 'images'
   * @returns MinIO file name for database storage
   */
  async saveBase64Image(
    base64Data: string,
    fileName?: string,
    mimeType?: string,
    bucket?: string,
  ): Promise<string> {
    try {
      // Extract the actual base64 data if it includes the data URI prefix
      let actualBase64: string = base64Data;
      let detectedMimeType: string | undefined = mimeType;

      if (base64Data.includes('base64,')) {
        const matches = base64Data.match(/^data:([\w/+]+);base64,(.*)$/);
        if (matches && matches.length === 3) {
          detectedMimeType = detectedMimeType || matches[1];
          actualBase64 = matches[2];
        }
      }

      if (!detectedMimeType) {
        // Default to PNG if we couldn't detect the mime type
        detectedMimeType = 'image/png';
      }

      // Generate a unique filename if not provided
      const uniqueFileName =
        fileName ||
        `${uuidv4()}.${this.getExtensionFromMimeType(detectedMimeType)}`;

      // Convert base64 to buffer
      const buffer = Buffer.from(actualBase64, 'base64');

      // Create file metadata - MongoDB will auto-generate _id
      const fileData: IFile = {
        filename: uniqueFileName,
        originalName: uniqueFileName,
        mimeType: detectedMimeType,
        size: buffer.length,
        bucket: bucket ?? 'images',
        uploadedBy: 'system',
      };

      // Save to MinIO with retry logic
      try {
        await minioClient.putObject(
          bucket ?? 'images',
          uniqueFileName,
          buffer,
          buffer.length,
          {
            'Content-Type': detectedMimeType,
          },
        );
      } catch (minioError: any) {
        console.error('MinIO upload error:', minioError);

        // If signature error, try with different approach
        if (minioError.code === 'SignatureDoesNotMatch') {
          console.log('Retrying with different MinIO configuration...');

          // Try without metadata
          await minioClient.putObject(
            bucket ?? 'images',
            uniqueFileName,
            buffer,
            buffer.length
          );
        } else {
          throw minioError;
        }
      }

      // Save metadata to database - MongoDB will auto-generate _id
      await FileModel.create(fileData);

      // Return only the MinIO file name for database storage
      return uniqueFileName;
    } catch (error) {
      console.error('Error saving base64 image:', error);
      throw error;
    }
  }

  /**
   * Get file extension from mime type
   * @param mimeType Mime type string
   * @returns File extension without dot
   */
  private getExtensionFromMimeType(mimeType: string): string {
    const extensions: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/svg+xml': 'svg',
      'application/pdf': 'pdf',
    };

    return extensions[mimeType] || 'bin';
  }

  /**
   * Get the public URL for a file stored in MinIO
   * @param fileId The ID of the file
   * @param bucket Optional bucket name, defaults to 'public'
   * @returns The public URL for the file
   */
  async getPublicUrl(fileId: string, bucket?: string): Promise<string> {
    try {
      // Find the file in the database
      const file = await FileModel.findOne({ where: { id: fileId } });
      if (!file) {
        throw new Error('File not found');
      }

      const fileName = file.filename;
      const bucketName = bucket || file.bucket || 'public';

      // Fallback: Generate a presigned URL if no public URL is configured
      // This URL will expire after the specified time (default: 7 days)
      const presignedUrl = await minioClient.presignedGetObject(
        bucketName,
        fileName,
        60 * 60 * 24 * 7, // 7 days in seconds
      );

      return presignedUrl;
    } catch (error) {
      console.error('Error generating public URL:', error);
      throw error;
    }
  }

  /**
   * Get the public URL for a file without database lookup
   * @param fileName The name of the file in the bucket
   * @param bucket Optional bucket name, defaults to 'public'
   * @returns The public URL for the file
   */
  getPublicUrlByFileName(fileName: string, bucket = 'public'): string {
    try {
      // Check if we have a configured public URL for MinIO
      if (environment.MINIO_PUBLIC_URL) {
        // Construct the URL using the configured public URL
        // Format: https://minio.example.com/bucket/filename
        const publicUrl = new URL(environment.MINIO_PUBLIC_URL);
        return `${publicUrl.origin}/${bucket}/${fileName}`;
      }

      // If no public URL is configured, return a relative URL that will be handled by the API
      return `/api/storage/stream/${fileName}`;
    } catch (error) {
      console.error('Error generating public URL by filename:', error);
      // Return a fallback URL that will be handled by the API
      return `/api/storage/stream/${fileName}`;
    }
  }

  /**
   * Get a base64 encoded data URI for an image stored in MinIO by file name
   * @param fileName The name of the file in MinIO
   * @param bucket Optional bucket name, defaults to 'images'
   * @returns An object containing the image name and base64 encoded data URI
   */
  async getBase64ImageByFileName(
    fileName: string,
    bucket = 'images',
  ): Promise<{ fileName: string; base64: string }> {
    try {
      // Get the object from MinIO as a readable stream
      const stream = await minioClient.getObject(bucket, fileName);

      // Convert the stream to a buffer
      const chunks: Buffer[] = [];

      // Return a promise that resolves when the stream is fully read
      return new Promise<{ fileName: string; base64: string }>(
        (resolve, reject) => {
          stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));

          stream.on('error', (err) => {
            reject(new Error(`Error reading file from MinIO: ${err.message}`));
          });

          stream.on('end', () => {
            // Concatenate all chunks into a single buffer
            const buffer = Buffer.concat(chunks);

            // Convert buffer to base64
            const base64Data = buffer.toString('base64');

            // Format as data URI (assume image/jpeg for now)
            const dataUri = `data:image/jpeg;base64,${base64Data}`;

            resolve({
              fileName: fileName,
              base64: dataUri,
            });
          });
        },
      );
    } catch (error) {
      console.error('Error getting base64 image by filename:', error);
      throw error;
    }
  }

  /**
   * Get a base64 encoded data URI for an image stored in MinIO
   * @param fileId The ID of the file to retrieve
   * @returns An object containing the image name and base64 encoded data URI
   */
  async getBase64Image(
    fileId: string,
  ): Promise<{ fileName: string; base64: string }> {
    try {
      // Find the file in the database
      const file = await FileModel.findById(fileId);
      if (!file) {
        throw new Error('File not found');
      }

      const fileName = file.filename;
      const bucketName = file.bucket || 'public';
      const mimeType = file.mimeType || 'application/octet-stream';

      // Get the object from MinIO as a readable stream
      const stream = await minioClient.getObject(bucketName, fileName);

      // Convert the stream to a buffer
      const chunks: Buffer[] = [];

      // Return a promise that resolves when the stream is fully read
      return new Promise<{ fileName: string; base64: string }>(
        (resolve, reject) => {
          stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));

          stream.on('error', (err) => {
            reject(new Error(`Error reading file from MinIO: ${err.message}`));
          });

          stream.on('end', () => {
            // Concatenate all chunks into a single buffer
            const buffer = Buffer.concat(chunks);

            // Convert buffer to base64
            const base64Data = buffer.toString('base64');

            // Format as data URI
            const dataUri = `data:${mimeType};base64,${base64Data}`;

            resolve({
              fileName: fileName,
              base64: dataUri,
            });
          });
        },
      );
    } catch (error) {
      console.error('Error getting base64 image:', error);
      throw error;
    }
  }
}
