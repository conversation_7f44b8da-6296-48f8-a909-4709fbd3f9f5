import { Request, Response } from 'express';
import { IHttpContext } from './interfaces.js';

// Core interfaces for dependency injection
export interface IServiceCollection {
  AddSingleton<T>(
    serviceType: any,
    implementationType?: any
  ): IServiceCollection;
  AddScoped<T>(serviceType: any, implementationType?: any): IServiceCollection;
  AddTransient<T>(
    serviceType: any,
    implementationType?: any
  ): IServiceCollection;
  BuildServiceProvider(): IServiceProvider;
}

export interface IServiceProvider {
  GetService<T>(serviceType: any): T;
  CreateScope(): IServiceScope;
}

export interface IServiceScope {
  serviceProvider: IServiceProvider;
  dispose(): void;
}

// Service lifetime types
export enum ServiceLifetime {
  Singleton,
  Scoped,
  Transient,
}

// Type definitions for dependency injection
export type ServiceDescriptor = {
  serviceType: any;
  implementationType: any;
  lifetime: ServiceLifetime;
};

export type Constructor<T = any> = new (...args: any[]) => T;

export type RequestDelegate = (context: IHttpContext) => Promise<void>;
export type MiddlewareFunction = (
  context: IHttpContext,
  next: () => Promise<void>
) => Promise<void>;

/**
 * Get the service provider from the request
 */
export function GetServiceProvider(req: Request): IServiceProvider {
  return (req as any).serviceProvider;
}

/**
 * Create a new HTTP context
 */
export function CreateHttpContext(req: Request, res: Response): IHttpContext {
  return {
    request: req,
    response: res,
    serviceProvider: GetServiceProvider(req),
  };
}
